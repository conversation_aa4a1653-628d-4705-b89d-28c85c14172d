{"name": "drumkit", "version": "0.44.5", "description": "Copilot for Logistics", "repository": {"type": "git", "url": "https://github.com/drumkitai/vulcan.git"}, "scripts": {"build": "vite build", "build:staging": "cross-env __STAGING__=true vite build --mode staging", "build:watch": "cross-env __DEV__=true vite build -w --mode dev", "build:staging:watch": "cross-env __STAGING__=true vite build -w --mode staging", "build:hmr": "rollup --config scripts/reload/rollup.config.ts --configPlugin typescript", "wss": "node scripts/reload/initReloadServer.js", "dev": "run-p wss build:watch", "staging": "run-p wss build:staging:watch", "test": "jest", "prettier": "prettier --write '**/*.{js,jsx,ts,tsx,json,css,scss,md}'", "lint": "eslint --max-warnings=0 '**/*.{ts,tsx,js,jsx}'", "release": "node scripts/release.js", "changeset": "changeset"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "type": "module", "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@hookform/error-message": "^2.0.1", "@inboxsdk/core": "2.2.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.1.4", "@sentry/browser": "8.41.0", "@sentry/integrations": "7.114.0", "@sentry/types": "^8.41.0", "@types/lodash": "^4.17.13", "@types/mustache": "^4.2.6", "animejs": "^3.2.2", "antd": "^5.22.2", "axios": "^1.8.2", "axios-retry": "^4.5.0", "cal-sans": "^1.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "construct-style-sheets-polyfill": "^3.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "flat": "^5.0.2", "framer-motion": "^12.19.1", "fuse.js": "^7.0.0", "lodash": "^4.17.21", "lucide-react": "^0.462.0", "mustache": "^4.2.0", "neverthrow": "^8.1.1", "posthog-js": "^1.194.1", "quill": "^2.0.3", "react": "^18.3.1", "react-content-loader": "^7.0.2", "react-day-picker": "^8.7.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-feather": "^2.0.10", "react-hook-form": "^7.53.2", "react-quill": "^2.0.0", "react-router-dom": "^7.0.1", "recharts": "^2.13.3", "styled-components": "^6.1.12", "swr": "^2.2.5", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "typescript-eslint": "^8.16.0", "uuid": "^11.0.3"}, "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.11", "@eslint/compat": "^1.2.3", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@rollup/plugin-typescript": "^12.1.1", "@sentry/vite-plugin": "^2.22.6", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.0.1", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/animejs": "^3.1.12", "@types/chrome": "^0.0.323", "@types/jest": "^29.5.14", "@types/node": "^22.10.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "8.16.0", "@typescript-eslint/parser": "^8.16.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.19", "chokidar": "^4.0.1", "cross-env": "^7.0.3", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.37.2", "fs-extra": "11.2.0", "jest": "29.7.0", "jest-chrome": "^0.8.0", "jest-environment-jsdom": "29.7.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.49", "postcss-nested": "^7.0.1", "prettier": "3.4.1", "rollup": "4.28.0", "sass": "1.81.0", "tailwindcss": "^3.4.15", "ts-jest": "29.2.5", "ts-loader": "9.5.1", "tslib": "^2.8.1", "typescript": "5.7.2", "vite": "^5.1.8", "ws": "8.18.0"}, "packageManager": "yarn@3.6.3"}