/** @type {import('tailwindcss').Config} */
/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable no-undef */

module.exports = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    '!**/node_modules/**',
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      screens: {
        xxs: '330px',
      },
      keyframes: {
        'accordion-down': {
          from: { height: 0 },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: 0 },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
      colors: {
        grayscale: {
          'content-1': '#1c1c1c', // TODO: Deprecate for a more descriptive name
          'content-2': '#585757', // TODO: Deprecate for a more descriptive name
          'content-3': '#969696', // TODO: Deprecate for a more descriptive name
          content: {
            DEFAULT: '#585757',
            label: '#1c1c1c',
            input: '#585757',
            description: '#969696',
            disabled: '#969696',
          },
          border: {
            input: '#585757',
            divider: '#969696',
            tag: '#969696',
            outline: '#585757',
            disabled: '#e8e8e8',
          },
          icon: {
            stroke: '#585757',
          },
          bg: {
            primary: '#ffffff',
            secondary: '#f7f7f7',
            tertiary: '#f0f0f0',
          },
        },
        'violet-blue': {
          main: '#3c38b4',
          'st-patrick': '#282583',
          space: '#1b1958',
          cetacean: '#020042',
          border: '#cce7ff',
          bg: '#e5f3ff',
        },
        orange: {
          main: '#f98600',
          hover: '#fe9659',
          pressed: '#fe9659',
          border: {
            light: '#fee7cc',
            main: '#f98600',
          },
          bg: '#fff4e8',
        },
        pink: {
          crimson: '#e50c45',
          vivid: '#cb0035',
          carmine: '#d90038',
          'dark-carmine': '#950027',
          border: '#fbd5eb',
          bg: '#fdeaf5',
        },
        red: {
          main: '#e92c2c',
          hover: '#ed5656',
          pressed: '#ed5656',
          bg: '#ffebeb',
          lightbg: '#fff0f0',
          border: '#e90e0e',
        },
        yellow: {
          main: '#f9c200',
          hover: '#fbd644',
          pressed: '#facc15',
          border: '#fef5d0',
          bg: '#fefae8',
        },
        green: {
          main: '#00ba34',
          hover: '#33c85d',
          pressed: '#00952a',
          border: '#ccf1d6',
          bg: '#e5f8eb',
        },
        blue: {
          main: '#0ea5e9',
          bg: '#e3f2fc',
          lightbg: '#f0f8ff',
        },
        primary: {
          DEFAULT: '#f98600',
          foreground: 'hsl(0 0% 98%)',
        },
        secondary: {
          DEFAULT: 'hsl(240 4.8% 95.9%)',
          foreground: 'hsl(240 5.9% 10%)',
        },
        muted: {
          DEFAULT: 'hsl(240 4.8% 95.9%)',
          foreground: 'hsl(240 3.8% 66.1%)',
        },
        accent: {
          DEFAULT: 'hsl(240 4.8% 95.9%)',
          foreground: 'hsl(240 5.9% 10%)',
        },
        destructive: {
          DEFAULT: 'hsl(0 72.22% 50.59%)',
          foreground: 'hsl(0 0% 98%)',
        },
      },
      borderRadius: {
        DEFAULT: '0.5rem',
        md: '1rem',
      },
    },
    fontFamily: {
      sans: ['Plus Jakarta Sans', 'sans-serif'],
      serif: ['serif'],
    },
  },
  plugins: [require('tailwindcss-animate')],
};
