import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react';
import path, { resolve } from 'path';
import { defineConfig, loadEnv } from 'vite';

import manifest from './manifest';
import packageJson from './package.json';
import addHmr from './scripts/plugins/add-hmr';
import customDynamicImport from './scripts/plugins/custom-dynamic-import';
import makeManifest from './scripts/plugins/make-manifest';
import watchRebuild from './scripts/plugins/watch-rebuild';

const rootDir = resolve(__dirname);
const srcDir = resolve(rootDir, 'src');

const alexandriaDir = resolve(srcDir, 'alexandria');
const alexandriaAssetsDir = resolve(alexandriaDir, 'assets');
const alexandriaConstantsDir = resolve(alexandriaDir, 'constants');
const alexandriaComponentsDir = resolve(alexandriaDir, 'components');
const alexandriaContextsDir = resolve(alexandriaDir, 'contexts');
const alexandriaHooksDir = resolve(alexandriaDir, 'hooks');
const alexandriaLibDir = resolve(alexandriaDir, 'lib');
const alexandriaIconsDir = resolve(alexandriaDir, 'icons');
const alexandriaTypesDir = resolve(alexandriaDir, 'types');
const alexandriaUtilsDir = resolve(alexandriaDir, 'utils');
const alexandriaPagesDir = resolve(alexandriaDir, 'pages');

const assetsDir = resolve(srcDir, 'assets');
const authDir = resolve(srcDir, 'auth');
const componentsDir = resolve(srcDir, 'components');
const constantsDir = resolve(srcDir, 'constants');
const contextsDir = resolve(srcDir, 'contexts');
const cssDir = resolve(srcDir, 'css');
const hooksDir = resolve(srcDir, 'hooks');
const libDir = resolve(srcDir, 'lib');
const pagesDir = resolve(srcDir, 'pages');
const utilsDir = resolve(srcDir, 'utils');

const outDir = resolve(rootDir, 'dist');
const publicDir = resolve(rootDir, 'public');

const isDev = process.env.__DEV__ === 'true';
const isStaging = process.env.__STAGING__ === 'true';
const isProduction = !(isDev || !isStaging);

const packageVersion = packageJson.version;

// ENABLE HMR IN BACKGROUND SCRIPT
const enableHmrInBackgroundScript = true;

type ViteConfigProps = {
  mode: string;
};

export default ({ mode }: ViteConfigProps) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };

  return defineConfig({
    define: {
      'import.meta.env.PACKAGE_VERSION': JSON.stringify(packageVersion),
    },
    resolve: {
      alias: {
        '@alexandria': alexandriaDir,
        asserts: alexandriaAssetsDir,
        constants: alexandriaConstantsDir,
        components: alexandriaComponentsDir,
        contexts: alexandriaContextsDir,
        hooks: alexandriaHooksDir,
        lib: alexandriaLibDir,
        icons: alexandriaIconsDir,
        types: alexandriaTypesDir,
        utils: alexandriaUtilsDir,
        pages: alexandriaPagesDir,

        '@assets': assetsDir,
        '@auth': authDir,
        '@constants': constantsDir,
        '@contexts': contextsDir,
        '@components': componentsDir,
        '@css': cssDir,
        '@hooks': hooksDir,
        '@lib': libDir,
        '@pages': pagesDir,
        '@utils': utilsDir,
        '@src': srcDir,
      },
    },
    plugins: [
      react(),
      makeManifest(manifest, {
        isDev: isDev || isStaging,
        contentScriptCssKey: regenerateCacheInvalidationKey(),
      }),
      customDynamicImport(),
      addHmr({ background: enableHmrInBackgroundScript, view: true }),
      watchRebuild(),
      sentryVitePlugin({
        org: 'drumkit',
        project: 'vulcan',
        authToken: process.env.VITE_SENTRY_AUTH_TOKEN,
        disable: isDev || isStaging,
        release: {
          inject: false,
          name: `${packageVersion}-${
            isDev ? 'dev' : isStaging ? 'staging' : 'prod'
          }`,
        },
      }),
    ],
    publicDir,
    build: {
      sourcemap: true,
      target: 'esnext',
      outDir,
      minify: isProduction,
      modulePreload: false,
      reportCompressedSize: isProduction,
      assetsInlineLimit: 22000,
      rollupOptions: {
        input: {
          content: resolve(pagesDir, 'content', 'index.ts'),
          pageWorld: '@inboxsdk/core/pageWorld.js',
          sidepanel: resolve(pagesDir, 'sidepanel', 'index.html'),
          options: resolve(pagesDir, 'options', 'index.html'),
          background: resolve(pagesDir, 'background', 'index.ts'),
        },
        output: {
          entryFileNames: 'src/pages/[name]/index.js',
          chunkFileNames: isDev
            ? 'assets/js/[name].js'
            : 'assets/js/[name].[hash].js',
          assetFileNames: (assetInfo) => {
            const { dir, name: _name } = path.parse(assetInfo.name || '');
            const assetFolder = dir.split('/').at(-1);
            const name = assetFolder + firstUpperCase(_name);
            return `assets/[ext]/${name}.chunk.[ext]`;
          },
        },
        /**
         * antd introduced an issue documented on the thread linked below, where it
         * seems like some libraries have sourcemap location-resolving issues with vite
         * https://github.com/vitejs/vite/issues/15012
         */
        onwarn(warning, defaultHandler) {
          if (warning.code === 'SOURCEMAP_ERROR') {
            return;
          }

          defaultHandler(warning);
        },
      },
    },
  });
};

function firstUpperCase(str: string) {
  const firstAlphabet = new RegExp(/( |^)[a-z]/, 'g');
  return str.toLowerCase().replace(firstAlphabet, (L) => L.toUpperCase());
}

let cacheInvalidationKey: string = generateKey();
function regenerateCacheInvalidationKey() {
  cacheInvalidationKey = generateKey();
  return cacheInvalidationKey;
}

function generateKey(): string {
  return `${(Date.now() / 100).toFixed()}`;
}
