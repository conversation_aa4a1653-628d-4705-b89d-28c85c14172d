import { useContext, useEffect } from 'react';

import SearchBar from 'components/SearchBar';
import Titlebar, { TitlebarButtons } from 'components/Titlebar';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { SidebarViewContext } from 'contexts/sidebarViewContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import LoadsSidebar from 'pages/LoadView/LoadsSidebar';
import QuoteSidebar from 'pages/QuoteView/QuoteSidebar';
import SidebarView from 'types/enums/SidebarView';

import { LoadSearchProvider } from '../../../alexandria/contexts/loadSearchContext';

/**
 * `SidebarExternalWrapper` is used to integrate the sidebar into
 * external sites like a TMS or a delegated inbox.
 * It adapts the sidebar based on feature flags and the current
 * platform context (e.g., Aljex).
 */
export default function SidebarExternalWrapper({
  proNumbers,
  disableCollapseButton,
}: {
  proNumbers: string[];
  disableCollapseButton?: boolean;
}) {
  const { currentView, setCurrentView } = useContext(SidebarViewContext);
  const {
    currentState: { isChromeSidePanel },
  } = useContext(SidebarStateContext);

  const {
    serviceFeaturesEnabled: { isLoadViewEnabled, isQuoteViewEnabled },
  } = useServiceFeatures();

  const determineDefaultView = (): SidebarView => {
    if (isLoadViewEnabled && proNumbers.length > 0) {
      return SidebarView.Loads;
    }

    if (isQuoteViewEnabled) {
      return SidebarView.Quote;
    }

    // One of the two views must be enabled.
    // If Quote View is not enabled, then default to Load View
    return SidebarView.Loads;
  };

  useEffect(() => {
    const defaultView = determineDefaultView();

    if (currentView !== defaultView) {
      setCurrentView(defaultView);
    }
  }, [isLoadViewEnabled, isQuoteViewEnabled, proNumbers]);

  return (
    <LoadSearchProvider initialFreightTrackingIDs={proNumbers || []}>
      <div
        style={{
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          overflowY: 'auto',
          overflowX: 'hidden',
          borderLeft: '1px solid rgb(229, 231, 235)',
        }}
        id='drumkit-content-view-root'
      >
        <Titlebar>
          <TitlebarButtons
            disableCollapseButton={disableCollapseButton}
            hideSearchBar={isLoadViewEnabled || isChromeSidePanel}
          />
          {currentView === SidebarView.Loads && <SearchBar />}
        </Titlebar>

        {currentView === SidebarView.Quote ? (
          <QuoteSidebar email={null} />
        ) : (
          currentView === SidebarView.Loads && <LoadsSidebar email={null} />
        )}
      </div>
    </LoadSearchProvider>
  );
}
