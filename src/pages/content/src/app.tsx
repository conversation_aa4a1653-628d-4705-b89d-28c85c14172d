import { useEffect, useState } from 'react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';

// Manually importing the Recorder module necessary for Session Replay in order
// to avoid PostHog lazy loading, as that behavior doesn't work in Browser Extensions
// due to Content Security Policy (CSP) restrictions.
// Reference: https://posthog.com/docs/advanced/browser-extension
import 'posthog-js/dist/recorder';
import { PostHogProvider } from 'posthog-js/react';
import { SWRConfig } from 'swr';

import POSTHOG_API_KEY from '@constants/PosthogApiKey';
import '@css/global/Global.css';

import AuthProvider from 'components/AuthProvider';
import ErrorBoundary from 'components/ErrorBoundary';
import RequireAuth from 'components/RequireAuth';
import ServiceProvider from 'components/ServiceProvider';
import SidebarStateProvider from 'components/SidebarStateContext';
import SidebarViewProvider from 'components/SidebarViewContext';
import { DrumkitPlatform } from 'contexts/sidebarStateContext';
import { getCurrentTab } from 'lib/chromeScripting/util';
import { IngestionMessage } from 'types/IngestionMessage';
import { Undef } from 'types/UtilityTypes';
import { fetcher } from 'utils/fetcher';

import { InboxProvider } from './InboxContext';
import SidebarExternalWrapper from './SidebarExternalWrapper';
import SidebarGmailWrapper from './SidebarGmailWrapper';

export function AppGmail({
  threadId,
  ingestionMessage,
  inboxEmailAddress,
}: {
  threadId: string;
  ingestionMessage: IngestionMessage;
  inboxEmailAddress: string;
}) {
  const [key, setKey] = useState<number>(new Date().getTime());

  const handleMessage = (msg: any) => {
    if (msg.command === 'CookieUpdated') {
      setKey(new Date().getTime());
    }
  };

  useEffect(() => {
    chrome.runtime.onMessage.addListener(handleMessage);

    // Cleanup function to remove the listener
    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage);
    };
  }, []);

  return (
    <div key={key}>
      <style
        dangerouslySetInnerHTML={{
          __html:
            "@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600&display=swap');",
        }}
      />
      <SWRConfig
        value={{
          revalidateOnFocus: false,
          fetcher,
        }}
      >
        <MemoryRouter>
          <Routes>
            <Route
              path='/'
              element={
                <ErrorBoundary>
                  <PostHogProvider
                    apiKey={POSTHOG_API_KEY}
                    options={{
                      api_host: 'https://us.i.posthog.com',
                      person_profiles: 'always',
                      capture_pageview: true,
                      persistence: 'localStorage',
                    }}
                  >
                    <ServiceProvider>
                      <SidebarStateProvider
                        inboxEmailAddress={inboxEmailAddress}
                        drumkitPlatform={DrumkitPlatform.Gmail}
                      >
                        <AuthProvider>
                          <RequireAuth>
                            <InboxProvider
                              initialThreadId={threadId}
                              ingestionMessage={ingestionMessage}
                            >
                              <SidebarViewProvider>
                                <SidebarGmailWrapper />
                              </SidebarViewProvider>
                            </InboxProvider>
                          </RequireAuth>
                        </AuthProvider>
                      </SidebarStateProvider>
                    </ServiceProvider>
                  </PostHogProvider>
                </ErrorBoundary>
              }
            />
          </Routes>
        </MemoryRouter>
      </SWRConfig>
    </div>
  );
}

export function AppTMS({
  proNumbers,
  wrapperPlatform,
  isChromeSidePanel,
  disableCollapseButton,
}: {
  proNumbers: string[];
  wrapperPlatform: DrumkitPlatform;
  isChromeSidePanel?: boolean;
  disableCollapseButton?: boolean;
}) {
  const [key, setKey] = useState<number>(new Date().getTime());
  const [tabId, setTabId] = useState<Undef<number>>(undefined);

  const handleMessage = (msg: any) => {
    if (msg.command === 'CookieUpdated') {
      setKey(new Date().getTime());
    }
  };

  useEffect(() => {
    chrome.runtime.onMessage.addListener(handleMessage);

    async function getTab() {
      const tab = await getCurrentTab();
      setTabId(tab?.id);
    }
    getTab();

    // Cleanup function to remove the listener
    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage);
    };
  }, []);

  return (
    <div key={key}>
      <style
        dangerouslySetInnerHTML={{
          __html:
            "@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600&display=swap');",
        }}
      />
      <SWRConfig
        value={{
          revalidateOnFocus: false,
          fetcher,
        }}
      >
        <MemoryRouter>
          <Routes>
            <Route
              path='/'
              element={
                <ErrorBoundary>
                  <ServiceProvider>
                    <SidebarStateProvider
                      key={tabId} // Remount the component when the tabId changes
                      drumkitPlatform={wrapperPlatform}
                      isChromeSidePanel={isChromeSidePanel}
                      tabId={tabId}
                    >
                      <AuthProvider>
                        <RequireAuth>
                          <SidebarViewProvider>
                            <SidebarExternalWrapper
                              proNumbers={proNumbers}
                              disableCollapseButton={disableCollapseButton}
                            />
                          </SidebarViewProvider>
                        </RequireAuth>
                      </AuthProvider>
                    </SidebarStateProvider>
                  </ServiceProvider>
                </ErrorBoundary>
              }
            />
          </Routes>
        </MemoryRouter>
      </SWRConfig>
    </div>
  );
}
