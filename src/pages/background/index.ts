import reloadOnUpdate from 'virtual:reload-on-update-in-background-script';

import DRUMKIT_AUTH_URL from '@constants/DrumkitAuthUrl';

import { registerRelayModalListener } from 'lib/hosts/relayStyling';
import captureException from 'utils/captureException';

import './modules/e2open';
import './modules/freightview';
import './modules/util';

reloadOnUpdate('pages/background');

let aljexParsedID: string | undefined = '';
const COOKIE_NAME = 'drumkit-auth';

// List of hosts that we disable the side panel on due to legacy HTML injection
// & custom webpage parsing.

const sidepanelBlacklist = ['relaytms.com', 'mail.google.com'];

/**
 * Extension reloading is necessary because the browser automatically caches the css.
 * If you do not use the css of the content script, please delete it.
 */

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'inboxsdk__injectPageWorld' && sender.tab) {
    if (chrome.scripting) {
      // MV3
      chrome.scripting.executeScript({
        target: { tabId: sender.tab.id! },
        world: 'MAIN',
        files: ['src/pages/pageWorld/index.js'],
      });
      sendResponse(true);
    } else {
      // MV2 fallback. Tell content script it needs to figure things out.
      sendResponse(false);
    }
    return true;
  }

  if (message.command === 'GetAuthCookie') {
    checkAuthCookies(sendResponse);
    return true;
  }

  if (message.command === 'RemoveAuthCookie') {
    removeAuthCookies(sendResponse);
    return true;
  }
});

function checkAuthCookies(authCallback: (response?: string) => void) {
  chrome.cookies.get({ url: DRUMKIT_AUTH_URL, name: COOKIE_NAME }, (cookie) => {
    if (cookie !== null) {
      authCallback(decodeURIComponent(cookie?.value));
      // Must send callback or extension will hang for several minutes
    } else {
      authCallback();
    }
  });
}

function removeAuthCookies(authCallback: (response?: string) => void) {
  chrome.cookies.remove(
    { url: DRUMKIT_AUTH_URL, name: COOKIE_NAME },
    (value) => {
      authCallback(value?.name);
    }
  );
}

chrome.action.onClicked.addListener(async (tab) => {
  if (!tab || !tab.id) {
    return;
  }

  chrome.tabs.sendMessage(tab.id, {
    command: 'DrumkitIconClicked',
  });
});

// Injects the Relay script into the tab when the user navigates to the planning board or hub
// This is necessary because Relay uses a modal to render the appointment forms, which is not
// rendered in the main DOM hence the need to inject a listening script to detect when the modal is opened.
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status !== 'complete' || !tab.url) {
    return;
  }

  const url = new URL(tab.url);

  // Inject the Relay script into the tab when the user navigates to the planning board or hub
  if (
    url.href.includes('relaytms.com/planning_board') ||
    url.href.includes('relaytms.com/hub')
  ) {
    console.log('injecting Relay script');
    chrome.scripting.executeScript({
      target: { tabId: tabId },
      func: registerRelayModalListener,
      args: [tab.url],
    });
  }

  // Disables the side panel on blacklisted sites
  if (sidepanelBlacklist.some((host) => url.origin.includes(host))) {
    await chrome.sidePanel.setOptions({
      tabId,
      enabled: false,
    });
    return;
  }

  // Enables the side panel on all other sites
  try {
    await chrome.sidePanel.setOptions({
      tabId,
      path: 'src/pages/sidepanel/index.html',
      enabled: true,
    });
  } catch (err) {
    captureException(new Error('error enabling side panel: ' + err));
  }

  const isAljex = url.origin?.includes('nfi.aljex.com');
  if (isAljex) {
    const aljexResult = await chrome.scripting.executeScript({
      target: { tabId },
      func: parseAljexPRO,
    });

    // we rely on onConnect only for the first parsed load id.
    // when tab changes to another id, we sendMessage to the sidebar with the new PRO here.
    if (aljexParsedID != aljexResult[0].result) {
      try {
        await chrome.runtime.sendMessage({
          action: 'updateParsedID',
          parsedID: aljexResult[0].result,
        });
      } catch {
        // Error expected if sidePanel isn't open yet
      }
    }

    aljexParsedID = aljexResult[0].result;
  }
});

// Allows users to open the side panel on Aljex by clicking on the action toolbar icon
chrome.sidePanel
  .setPanelBehavior({ openPanelOnActionClick: true })
  .catch((error) => captureException(error));

// When the sidepanel first connects, send the parsed ID to the sidepanel
chrome.runtime.onConnect.addListener(async (port) => {
  if (port.name === 'drumkitSidepanel' && aljexParsedID) {
    await chrome.runtime.sendMessage({
      action: 'updateParsedID',
      parsedID: aljexParsedID,
    });
  }
});

chrome.runtime.onInstalled.addListener(async () => {
  chrome.contextMenus.create({
    id: 'openSidePanel',
    title: 'Open Drumkit',
    contexts: ['all'],
  });
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (
    info.menuItemId === 'openSidePanel' &&
    tab?.url &&
    !sidepanelBlacklist.some((host) => tab.url?.includes(host))
  ) {
    chrome.sidePanel.open({ tabId: tab.id, windowId: tab.windowId });
  } else {
    // Disables the side panel on all other sites
    chrome.sidePanel.setOptions({
      tabId: tab?.id,
      enabled: false,
    });
  }
});

chrome.cookies.onChanged.addListener((info) => {
  if (
    DRUMKIT_AUTH_URL?.includes(info.cookie.domain) &&
    info.cookie.name === COOKIE_NAME
  ) {
    chrome.runtime.sendMessage({ command: 'CookieUpdated' });
  }
});

export function parseAljexPRO() {
  return document.querySelector('#headpro')?.textContent?.trim();
}
