import React, { useEffect, useState } from 'react';

import { AppTMS } from '@pages/content/src/app';

import { DrumkitPlatform } from 'contexts/sidebarStateContext';

const SidePanel: React.FC = () => {
  const [parsedID, setParsedID] = useState<string[]>([]);

  const handleMessage = (message: any) => {
    if (message.action === 'updateParsedID' && message.parsedID) {
      setParsedID([message.parsedID]);
    }
  };

  useEffect(() => {
    // Add listener for messages from the background script
    chrome.runtime.onMessage.addListener(handleMessage);

    // Open a port connection to let background script know sidepanel has loaded
    chrome.runtime.connect({ name: 'drumkitSidepanel' });

    // Cleanup function to remove the listener
    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage);
    };
  }, []);

  return (
    <>
      <AppTMS
        proNumbers={parsedID}
        wrapperPlatform={DrumkitPlatform.Sidepanel}
        isChromeSidePanel={true}
        disableCollapseButton={true}
      ></AppTMS>
    </>
  );
};

export default SidePanel;
