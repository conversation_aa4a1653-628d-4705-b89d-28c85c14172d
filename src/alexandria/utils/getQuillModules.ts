// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore react-quill is in the parent dir
import { Quill as GlobalQuill } from 'react-quill';

const fontSizeArr = ['12px', '14px', '16px', '20px', '24px', '32px'];

export const getQuillModules = () => {
  const SizeStyle = GlobalQuill.import('attributors/style/size') as any;
  SizeStyle.whitelist = fontSizeArr;
  GlobalQuill.register(SizeStyle, true);

  return {
    toolbar: [
      [{ size: fontSizeArr }],
      ['bold', 'italic', 'underline'],
      [{ color: [] }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      ['link'],
    ],
  };
};

export const htmlToPlainText = (content: string): string => {
  if (content.includes('<')) {
    const processedContent = content
      .replace(/<\/p>/gi, '\n') // Convert closing </p> tags to newlines
      .replace(/<br\s*\/?>/gi, '\n'); // Convert <br> tags to newlines

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = processedContent;

    // Get the text content, which will now have newlines from the converted tags
    let text = tempDiv.textContent || tempDiv.innerText || '';

    // Replace multiple spaces with single spaces, but preserve line breaks
    text = text.replace(/[ \t]+/g, ' ');

    // Remove extra line breaks and trim
    text = text.replace(/\n\s*\n/g, '\n').trim();

    return text;
  }

  // If it's already plain text, just return it
  return content;
};
