import { UseFormSetValue } from 'react-hook-form';

import { QuickQuoteInputs, Stop } from 'lib/api/getQuickQuote';

const zipPlus4Regex = /^\d{5}-\d{4}$/;

type ConvertZipPlus4ToRegularProps = {
  formValues: QuickQuoteInputs;
  setValue: UseFormSetValue<QuickQuoteInputs>;
};

export default function convertZipPlus4ToRegular({
  formValues,
  setValue,
}: ConvertZipPlus4ToRegularProps): boolean {
  let conversionMade = false;

  formValues.stops.forEach((stop: Stop, i: number) => {
    if (stop.location && zipPlus4Regex.test(stop.location)) {
      const trimmedZipcode = stop.location.split('-')[0];

      // Convert to regular ZIP code by removing the "-\d{4}" part
      formValues.stops[i].location = trimmedZipcode; // Updates formValues object
      setValue(`stops.${i}.location`, trimmedZipcode); // Updates actual form field

      conversionMade = true;
    }
  });

  return conversionMade;
}
