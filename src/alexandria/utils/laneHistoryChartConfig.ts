// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { ChartConfig } from 'components/Chart';

export const chartConfig: ChartConfig = {
  maxRate: {
    label: 'Max',
    color: '#e92c2c', // tailwind config red-main
  },
  averageRate: {
    label: 'Avg',
    color: '#f98600', // tailwind config orange-main
  },
  lowestRate: {
    label: 'Min',
    color: '#00ba34', // tailwind config green-main
  },
};

export const chartConfigPercentile: ChartConfig = {
  maxRate: {
    label: '75%',
    color: '#e92c2c', // tailwind config red-main
  },
  averageRate: {
    label: '50%',
    color: '#f98600', // tailwind config orange-main
  },
  lowestRate: {
    label: '25%',
    color: '#00ba34', // tailwind config green-main
  },
};

// reference: QuoteView/Quoting/RequestQuickQuote.tsx
