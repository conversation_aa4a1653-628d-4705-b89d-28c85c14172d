import * as React from 'react';

import Retalix from '../assets/retalix-logo.png';
import { cn } from '../utils/shadcn';

export default function RetalixLogo(
  props: React.ImgHTMLAttributes<HTMLImageElement>
): JSX.Element {
  const { className: classNameProp, ...otherProps } = props;

  return (
    <img
      src={Retalix}
      alt='Retalix logo'
      className={cn('filter invert brightness-75', classNameProp)}
      {...otherProps}
    />
  );
}
