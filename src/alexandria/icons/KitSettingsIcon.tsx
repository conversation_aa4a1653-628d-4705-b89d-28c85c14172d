import * as React from 'react';

export default function KitSettingsIcon(
  props: React.SVGProps<SVGSVGElement>
): JSX.Element {
  return (
    <svg
      width='32'
      height='32'
      viewBox='0 0 32 32'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path d='M13.7215 29.9263H6.92371C6.79909 29.9263 6.69712 30.0299 6.69712 30.1567V31.0783C6.69712 31.5881 7.10215 32 7.60349 32H13.0417C13.5431 32 13.9481 31.5881 13.9481 31.0783V30.1567C13.9481 30.0299 13.8461 29.9263 13.7215 29.9263ZM10.3226 6.19354C5.19311 6.19354 1.03229 10.4245 1.03229 15.6405C1.03229 19.1371 2.90168 22.1901 5.67745 23.8232V27.1613C5.67745 27.6711 6.08248 28.0829 6.58382 28.0829H14.0614C14.5627 28.0829 14.9678 27.6711 14.9678 27.1613V23.8232C17.7435 22.1901 19.6129 19.1371 19.6129 15.6405C19.6129 10.4245 15.4521 6.19354 10.3226 6.19354ZM13.9453 22.0288L12.9284 22.6279V26.0092H7.71679V22.6279L6.69995 22.0288C4.47084 20.7183 3.07163 18.3047 3.07163 15.6405C3.07163 11.568 6.31757 8.26728 10.3226 8.26728C14.3276 8.26728 17.5736 11.568 17.5736 15.6405C17.5736 18.3047 16.1744 20.7183 13.9453 22.0288Z' />
      <path
        d='M20.2022 7.30321C20.1607 7.14246 20.0769 6.99576 19.9595 6.87837C19.8421 6.76098 19.6954 6.67719 19.5347 6.63572L16.6849 5.90087C16.6363 5.88707 16.5935 5.85779 16.5631 5.81747C16.5326 5.77715 16.5161 5.72799 16.5161 5.67745C16.5161 5.62691 16.5326 5.57775 16.5631 5.53743C16.5935 5.4971 16.6363 5.46782 16.6849 5.45402L19.5347 4.71871C19.6954 4.67728 19.842 4.59356 19.9594 4.47626C20.0768 4.35895 20.1606 4.21235 20.2022 4.05168C20.4891 2.93879 20.65 2.31484 20.937 1.20195C20.9507 1.15314 20.9799 1.11013 21.0203 1.0795C21.0607 1.04887 21.11 1.03229 21.1607 1.03229C21.2114 1.03229 21.2607 1.04887 21.301 1.0795C21.3414 1.11013 21.3707 1.15314 21.3843 1.20195L22.1187 4.05168C22.1602 4.21243 22.244 4.35914 22.3614 4.47653C22.4788 4.59392 22.6255 4.67771 22.7862 4.71918L25.6359 5.45356C25.6849 5.46707 25.7282 5.4963 25.759 5.53674C25.7898 5.57718 25.8064 5.62661 25.8064 5.67745C25.8064 5.72828 25.7898 5.77771 25.759 5.81816C25.7282 5.8586 25.6849 5.88782 25.6359 5.90134L22.7862 6.63572C22.6255 6.67719 22.4788 6.76098 22.3614 6.87837C22.244 6.99576 22.1602 7.14246 22.1187 7.30321L21.3839 10.1529C21.3702 10.2018 21.341 10.2448 21.3006 10.2754C21.2602 10.306 21.2109 10.3226 21.1602 10.3226C21.1095 10.3226 21.0602 10.306 21.0198 10.2754C20.9795 10.2448 20.9502 10.2018 20.9366 10.1529L20.2022 7.30321Z'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M27.8543 10.7097C27.8312 10.6204 27.7847 10.5389 27.7195 10.4737C27.6542 10.4084 27.5727 10.3619 27.4834 10.3388L25.9003 9.9306C25.8732 9.92293 25.8495 9.90667 25.8325 9.88427C25.8156 9.86186 25.8065 9.83455 25.8065 9.80647C25.8065 9.7784 25.8156 9.75109 25.8325 9.72868C25.8495 9.70628 25.8732 9.69002 25.9003 9.68235L27.4834 9.27384C27.5727 9.25083 27.6542 9.20432 27.7194 9.13915C27.7846 9.07398 27.8312 8.99253 27.8543 8.90327C28.0137 8.285 28.1031 7.93836 28.2625 7.32009C28.2701 7.29297 28.2864 7.26908 28.3088 7.25206C28.3312 7.23504 28.3586 7.22583 28.3868 7.22583C28.4149 7.22583 28.4423 7.23504 28.4647 7.25206C28.4872 7.26908 28.5034 7.29297 28.511 7.32009L28.919 8.90327C28.9421 8.99258 28.9886 9.07408 29.0538 9.1393C29.119 9.20451 29.2005 9.25106 29.2898 9.2741L30.873 9.68209C30.9003 9.6896 30.9243 9.70583 30.9414 9.7283C30.9585 9.75077 30.9677 9.77823 30.9677 9.80647C30.9677 9.83472 30.9585 9.86218 30.9414 9.88465C30.9243 9.90712 30.9003 9.92335 30.873 9.93086L29.2898 10.3388C29.2005 10.3619 29.119 10.4084 29.0538 10.4737C28.9886 10.5389 28.9421 10.6204 28.919 10.7097L28.5108 12.2929C28.5032 12.32 28.4869 12.3439 28.4645 12.3609C28.4421 12.3779 28.4147 12.3871 28.3865 12.3871C28.3584 12.3871 28.331 12.3779 28.3085 12.3609C28.2861 12.3439 28.2698 12.32 28.2623 12.2929L27.8543 10.7097Z'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M24.7575 16.9032C24.7344 16.8139 24.6879 16.7324 24.6227 16.6672C24.5574 16.602 24.4759 16.5554 24.3866 16.5324L22.8035 16.1241C22.7764 16.1165 22.7527 16.1002 22.7357 16.0778C22.7188 16.0554 22.7097 16.0281 22.7097 16C22.7097 15.9719 22.7188 15.9446 22.7357 15.9222C22.7527 15.8998 22.7764 15.8836 22.8035 15.8759L24.3866 15.4674C24.4759 15.4444 24.5574 15.3979 24.6226 15.3327C24.6878 15.2675 24.7344 15.1861 24.7575 15.0968C24.9169 14.4785 25.0063 14.1319 25.1657 13.5136C25.1733 13.4865 25.1896 13.4626 25.212 13.4456C25.2344 13.4286 25.2618 13.4194 25.29 13.4194C25.3181 13.4194 25.3455 13.4286 25.3679 13.4456C25.3904 13.4626 25.4066 13.4865 25.4142 13.5136L25.8222 15.0968C25.8453 15.1861 25.8918 15.2676 25.957 15.3328C26.0222 15.3981 26.1037 15.4446 26.193 15.4676L27.7762 15.8756C27.8035 15.8831 27.8275 15.8994 27.8446 15.9218C27.8617 15.9443 27.8709 15.9718 27.8709 16C27.8709 16.0283 27.8617 16.0557 27.8446 16.0782C27.8275 16.1007 27.8035 16.1169 27.7762 16.1244L26.193 16.5324C26.1037 16.5554 26.0222 16.602 25.957 16.6672C25.8918 16.7324 25.8453 16.8139 25.8222 16.9032L25.414 18.4864C25.4064 18.5135 25.3901 18.5374 25.3677 18.5544C25.3453 18.5715 25.3179 18.5807 25.2897 18.5807C25.2616 18.5807 25.2342 18.5715 25.2117 18.5544C25.1893 18.5374 25.173 18.5135 25.1655 18.4864L24.7575 16.9032Z'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
}
