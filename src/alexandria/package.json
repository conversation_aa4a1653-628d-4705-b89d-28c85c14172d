{"name": "alexandria", "version": "0.0.1", "repository": {"type": "git", "url": "https://github.com/drumkitai/alexandria.git"}, "license": "MIT", "scripts": {"lint": "eslint --max-warnings=0 '**/*.{ts,tsx,js,jsx}'", "prettier": "prettier --write '**/*.{js,jsx,ts,tsx,json,css,scss,md}'", "test": "jest"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "devDependencies": {"@hookform/error-message": "^2.0.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.1", "@testing-library/dom": "^10.4.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/chrome": "^0.0.323", "@types/eslint": "^9", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.0", "@types/office-js": "^1.0.449", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/testing-library__react": "^10.2.0", "@types/testing-library__user-event": "^4.2.0", "@types/uuid": "^10", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "antd": "^5.22.2", "class-variance-authority": "^0.7.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "neverthrow": "^8.0.0", "prettier": "^3.4.1", "react-content-loader": "^7.0.0", "react-router-dom": "^7.0.1", "ts-jest": "^29.1.4", "ts-node": "^10.9.2", "typescript": "^5.7.2"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toggle-group": "^1.1.10", "@sentry/browser": "^8.41.0", "@types/node": "^22.10.1", "axios": "^1.7.8", "clsx": "^2.1.1", "dayjs": "^1.11.13", "fuse.js": "^7.0.0", "lodash": "^4.17.21", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "recharts": "^2.15.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "typescript-eslint": "^8.16.0", "uuid": "^11.0.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}