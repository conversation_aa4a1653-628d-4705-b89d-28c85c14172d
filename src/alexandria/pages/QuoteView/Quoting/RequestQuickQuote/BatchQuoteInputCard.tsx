import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  Control,
  Controller,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore framer-motion is in the parent dir
import { AnimatePresence, motion } from 'framer-motion';
import _ from 'lodash';
import {
  ArrowRightIcon,
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CopyIcon,
  EditIcon,
  InfoIcon,
  MapIcon,
  TrashIcon,
  XIcon,
} from 'lucide-react';

import { Checkbox } from 'components/Checkbox';
import { GenericLineChart } from 'components/GenericLineChart';
import { Label } from 'components/Label';
import { LaneHistoryFromServiceChart } from 'components/LaneHistoryFromServiceChart';
import { QuoteCard, QuoteCardType } from 'components/QuoteCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import CarrierPriceCalculator, {
  CarrierPriceCalculatorParent,
} from 'components/pricing/CarrierPriceCalculator';
import { QuickQuoteConfig } from 'contexts/serviceContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import {
  LaneHistoryResponse,
  SourceHistory,
  WeekLaneData,
} from 'lib/api/getLaneHistory';
import { LaneHistoryFromServiceResponse } from 'lib/api/getLaneHistoryFromService';
import { SelectedCarrierType } from 'lib/api/getQuickQuote';
import { Maybe } from 'types/UtilityTypes';
import {
  Quoting,
  QuotingPortals,
  TMS,
  integrationNameMap,
} from 'types/enums/Integrations';
import { copyToClipboard } from 'utils/copyToClipboard';
import { formatCurrency } from 'utils/formatCurrency';
import { titleCase } from 'utils/formatStrings';
import { constructGoogleMapsUrl } from 'utils/googleMaps';
import {
  chartConfig,
  chartConfigPercentile,
} from 'utils/laneHistoryChartConfig';
import { cn } from 'utils/shadcn';

import { QuickQuoteTextInput } from './QuickQuoteForm';
import { useHelperFunctions } from './helperFunctions';
import {
  BatchQuoteResult,
  CarrierCostType,
  LaneTier,
  ProfitType,
  TransportTypeOption,
} from './types';

export type DistanceSource = Quoting | QuotingPortals;

interface BatchQuoteInputCardProps {
  index: number;
  control: Control<any>;
  transportTypeOptions: TransportTypeOption[];
  onRemove: (index: number) => void;
  result: BatchQuoteResult | undefined;
  laneHistoryResult: LaneHistoryResponse | undefined;
  laneHistoryFromServiceResult?: LaneHistoryFromServiceResponse | undefined;
  setValue: UseFormSetValue<any>;
  isSelected: boolean;
  onSelect: (isSelected: boolean) => void;
  quickQuoteConfig: QuickQuoteConfig | undefined;
  isExpanded: boolean;
  onToggleExpand: () => void;
  registerCarrierSetter?: (
    index: number,
    setter: (carrier: SelectedCarrierType) => void
  ) => void;
  registerPriceCallback?: (
    index: number,
    priceData: {
      finalPrice: number;
      maxDistance: number;
      fuelEstimate: number;
    } | null
  ) => void;
  registerPriceFormatSetter?: (
    index: number,
    setter: (priceFormat: CarrierCostType | 'Both') => void
  ) => void;
  onPriceFormatChange?: (
    index: number,
    priceFormat: CarrierCostType | 'Both'
  ) => void;
  isTMSLaneHistoryEnabled: boolean;
  isQuoteLaneHistoryEnabled: boolean;
  isGetLaneRateFromServiceEnabled?: boolean;
}

export const BatchQuoteInputCard = ({
  index,
  control,
  transportTypeOptions,
  onRemove,
  result,
  laneHistoryResult,
  laneHistoryFromServiceResult,
  setValue,
  isSelected,
  onSelect,
  isExpanded,
  onToggleExpand,
  registerCarrierSetter,
  registerPriceCallback,
  registerPriceFormatSetter,
  onPriceFormatChange,
  isTMSLaneHistoryEnabled,
  isQuoteLaneHistoryEnabled,
  isGetLaneRateFromServiceEnabled,
}: BatchQuoteInputCardProps) => {
  const values = useWatch({
    control,
    name: `quotes.${index}`,
  });

  const { profit, profitType } = values;

  const {
    serviceFeaturesEnabled: { isQuoteCalculatorMarginEnabled },
  } = useServiceFeatures();

  // Proper price calculation with margin/markup logic
  const calculateFinalPrice = useCallback(
    (
      carrierCost: number,
      carrierCostType: CarrierCostType,
      profit: number,
      profitType: ProfitType,
      distance: number
    ): number => {
      // Calculate flat carrier cost
      const flatCarrierCost =
        carrierCostType === CarrierCostType.Flat
          ? carrierCost
          : carrierCost * distance;

      if (profitType === ProfitType.Amount) {
        // Flat profit: Add the profit to the carrier cost
        return flatCarrierCost + profit;
      } else {
        // ProfitType.Percentage
        if (isQuoteCalculatorMarginEnabled) {
          // Margin formula: Price = Cost / (1 - Profit_Percentage)
          if (profit >= 100) {
            // Profit margin cannot be 100% or more.
            return flatCarrierCost / (1 - 0.999);
          } else {
            return flatCarrierCost / (1 - profit / 100);
          }
        } else {
          // Markup formula: Price = Cost * (1 + Markup_Percentage)
          return flatCarrierCost * (1 + profit / 100);
        }
      }
    },
    [isQuoteCalculatorMarginEnabled]
  );

  // Check if this is an empty card (no locations filled)
  const isEmptyCard =
    !values?.stops?.[0]?.location && !values?.stops?.[1]?.location;

  const [isEditing, setIsEditing] = useState(isEmptyCard);
  const [originalValues, setOriginalValues] = useState(null);
  const [hasCopied, setHasCopied] = useState(false);
  const copyTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [selectedCarrier, setSelectedCarrier] = useState<SelectedCarrierType>(
    SelectedCarrierType.NETWORK
  );
  const [carrierCost, setCarrierCost] = useState(0);
  const [carrierCostType, setCarrierCostType] = useState<CarrierCostType>(
    CarrierCostType.Flat
  );
  const [finalPrice, setFinalPrice] = useState<Maybe<number>>(null);
  const [maxDistance, setMaxDistance] = useState(0);
  const [datFuelSurcharge, setDATFuelSurcharge] = useState<Maybe<number>>(null);
  const [fuelEstimate, setFuelEstimate] = useState(0);
  const [distanceSource, setDistanceSource] =
    useState<Maybe<DistanceSource>>(null);
  const [priceFormat, setPriceFormat] = useState<CarrierCostType | 'Both'>(
    CarrierCostType.Flat
  );

  // Lane history state
  const [tmsLaneHistory, setTMSLaneHistory] =
    useState<Maybe<SourceHistory[]>>(null);
  const [tmsLaneHistorySelectedTierIndex, setTmsLaneHistorySelectedTierIndex] =
    useState<Maybe<number>>(null);

  const setProfitHandler = (newProfit: number) => {
    setValue(`quotes.${index}.profit`, newProfit);
  };

  const setProfitTypeHandler = (newProfitType: ProfitType) => {
    setValue(`quotes.${index}.profitType`, newProfitType);
  };

  // Track if we've already set the initial carrier selection to avoid resetting it
  const [hasSetInitialCarrier, setHasSetInitialCarrier] = useState(false);

  // Reset the initial carrier flag when a new result comes in
  useEffect(() => {
    if (result?.response) {
      setHasSetInitialCarrier(false);
    }
  }, [result?.response?.quoteRequestId]);

  // Generate combined quote cards (original + lane history)
  const allQuoteCards = useMemo(() => {
    if (!result?.cards) return [];

    let combinedCards = [...result.cards];

    // Add lane history cards if available
    if (laneHistoryResult) {
      const laneHistoryCards =
        useHelperFunctions.handleQuoteLaneHistory(laneHistoryResult);
      combinedCards = [...combinedCards, ...laneHistoryCards];
    }

    return combinedCards;
  }, [result?.cards, laneHistoryResult]);

  useEffect(() => {
    // When the result comes in, we need to derive multiple state values.
    // Consolidating them here prevents a flicker/delay where the price
    // would first show a value based on old state, then update.

    if (!result?.response) {
      // If there's no result, just calculate the price with the current state.
      const newFinalPrice = calculateFinalPrice(
        carrierCost,
        carrierCostType,
        profit,
        profitType,
        maxDistance
      );
      setFinalPrice(newFinalPrice);
      return;
    }

    const newMaxDistance =
      result.response.quotes?.reduce(
        (max, current) => (current.distance > max.distance ? current : max),
        { distance: 0 }
      ).distance || 0;

    // Set distance source from the quote with maximum distance
    const maxDistanceQuote = result.response.quotes?.length
      ? result.response.quotes.reduce((max, current) =>
          current.distance > max.distance ? current : max
        )
      : null;
    const newDistanceSource =
      (maxDistanceQuote?.source as DistanceSource) || null;

    let newCarrierCost = carrierCost;
    // Only set the initial carrier selection when result first comes in
    if (allQuoteCards?.length && !hasSetInitialCarrier) {
      // If TMS lane history is enabled and available, always select that first
      if (
        isTMSLaneHistoryEnabled &&
        laneHistoryResult &&
        Object.values(laneHistoryResult?.resultsBySource)?.length > 0
      ) {
        const laneHistoryCard = allQuoteCards.find(
          (c: QuoteCardType) =>
            c.type === SelectedCarrierType.LANE_HISTORY ||
            c.type === SelectedCarrierType.MCLEOD_LANE_HISTORY ||
            c.type === SelectedCarrierType.TURVO_LANE_HISTORY ||
            c.type === SelectedCarrierType.GLOBALTRANZ_LANE_HISTORY ||
            c.type === SelectedCarrierType.GLOBALTRANZ_TMS_LANE_HISTORY
        );
        if (laneHistoryCard) {
          setSelectedCarrier(laneHistoryCard.type);
          newCarrierCost = Math.round(laneHistoryCard.cost * 100) / 100;
          setCarrierCost(newCarrierCost);
          setHasSetInitialCarrier(true);
        }
      }

      // If no lane history card was selected, prefer DAT carrier if available, otherwise use the lowest cost carrier
      if (!hasSetInitialCarrier) {
        const datCarrier = allQuoteCards.find(
          (card: QuoteCardType) => card.type === SelectedCarrierType.DAT
        );
        const defaultCarrier =
          datCarrier ||
          allQuoteCards.reduce((prev: QuoteCardType, curr: QuoteCardType) =>
            prev.cost < curr.cost ? prev : curr
          );

        setSelectedCarrier(defaultCarrier.type);
        newCarrierCost = Math.round(defaultCarrier.cost * 100) / 100;
        setCarrierCost(newCarrierCost);
        setHasSetInitialCarrier(true);
      }
    }

    const newFinalPrice = calculateFinalPrice(
      newCarrierCost,
      carrierCostType,
      profit,
      profitType,
      newMaxDistance
    );

    setMaxDistance(newMaxDistance);
    setDistanceSource(newDistanceSource);
    setDATFuelSurcharge((result.response as any).datFuelSurcharge || null);
    setFinalPrice(newFinalPrice);
  }, [
    result,
    carrierCostType,
    profit,
    profitType,
    hasSetInitialCarrier,
    calculateFinalPrice,
    allQuoteCards,
    isTMSLaneHistoryEnabled,
    laneHistoryResult,
  ]);

  // Recalculate final price whenever pricing parameters change
  useEffect(() => {
    if (result?.response) {
      const newFinalPrice = calculateFinalPrice(
        carrierCost,
        carrierCostType,
        profit,
        profitType,
        maxDistance
      );
      setFinalPrice(newFinalPrice);
    }
  }, [
    carrierCost,
    carrierCostType,
    profit,
    profitType,
    maxDistance,
    result?.response,
    calculateFinalPrice,
  ]);

  // Notify parent of price changes
  useEffect(() => {
    if (result?.response && finalPrice !== null && registerPriceCallback) {
      registerPriceCallback(index, {
        finalPrice,
        maxDistance,
        fuelEstimate,
      });
    }
  }, [
    finalPrice,
    maxDistance,
    fuelEstimate,
    result?.response,
    registerPriceCallback,
    index,
  ]);

  // Start in editing mode if this is an empty card
  useEffect(() => {
    if (isEmptyCard && !isEditing) {
      setIsEditing(true);
    }
  }, [isEmptyCard, isEditing]);

  // Lane history effect
  useEffect(() => {
    // If TMS lane history is enabled, set the selected lane tier to the first tier in the list
    const tmsHistoriesKey = Object.keys(
      laneHistoryResult?.resultsBySource || {}
    ).find((source) => Object.values(TMS).includes(source as TMS));

    if (tmsHistoriesKey) {
      setTMSLaneHistory(
        laneHistoryResult?.resultsBySource[tmsHistoriesKey as TMS] || null
      );
      setTmsLaneHistorySelectedTierIndex(0);
    }
  }, [laneHistoryResult]);

  const toggleLaneHistoryGraph = (option: string) => {
    if (!tmsLaneHistory) return;

    const tierIndex = tmsLaneHistory?.findIndex(
      (history) => history.laneTier === option
    );
    if (tierIndex === -1) return;

    setTmsLaneHistorySelectedTierIndex(tierIndex);
  };

  const handlers = {
    edit: () => {
      setOriginalValues(values);
      setIsEditing(true);
    },
    save: () => {
      setIsEditing(false);
      setOriginalValues(null);
    },
    discard: () => {
      if (originalValues) {
        setValue(`quotes.${index}`, originalValues);
      }
      setIsEditing(false);
      setOriginalValues(null);
    },
    remove: () => onRemove(index),
  };

  const ActionButton = ({
    onClick,
    className,
    icon: Icon,
    'aria-label': ariaLabel,
  }: {
    onClick: () => void;
    className: string;
    icon: any;
    'aria-label': string;
  }) => (
    <motion.button
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className={`p-1 rounded-full transition-colors ${className}`}
      type='button'
      aria-label={ariaLabel}
    >
      <Icon className='h-3.5 w-3.5' />
    </motion.button>
  );

  const renderActions = () => (
    <AnimatePresence mode='wait'>
      {isEditing ? (
        <motion.div
          key='editing-actions'
          {...ANIMATIONS.actions}
          className='flex gap-1'
        >
          {!isEmptyCard ? (
            <>
              <ActionButton
                onClick={handlers.save}
                className='hover:bg-blue-200 text-blue-600'
                icon={CheckIcon}
                aria-label='Save changes'
              />
              <ActionButton
                onClick={handlers.discard}
                className='hover:bg-red-200 text-red-600'
                icon={XIcon}
                aria-label='Discard changes'
              />
            </>
          ) : (
            <ActionButton
              onClick={handlers.remove}
              className='hover:bg-red-300 text-red-600'
              icon={TrashIcon}
              aria-label='Remove lane'
            />
          )}
        </motion.div>
      ) : (
        <motion.div
          key='view-actions'
          {...ANIMATIONS.actions}
          className='flex gap-1'
        >
          {!result?.response ? (
            <ActionButton
              onClick={handlers.edit}
              className='hover:bg-blue-200 text-blue-600'
              icon={EditIcon}
              aria-label='Edit lane'
            />
          ) : (
            <ActionButton
              onClick={async () => {
                const success = await copyToClipboard(
                  formatPriceForCopy(
                    finalPrice,
                    priceFormat,
                    maxDistance,
                    fuelEstimate
                  )
                );
                if (success) {
                  setHasCopied(true);
                  // Clear any existing timeout
                  if (copyTimeoutRef.current) {
                    clearTimeout(copyTimeoutRef.current);
                  }
                  copyTimeoutRef.current = setTimeout(() => {
                    setHasCopied(false);
                    copyTimeoutRef.current = null;
                  }, 2000);
                }
              }}
              className='hover:bg-blue-200 text-blue-600'
              icon={hasCopied ? CheckIcon : CopyIcon}
              aria-label='Copy quote price'
            />
          )}
          <ActionButton
            onClick={handlers.remove}
            className='hover:bg-red-300 text-red-600'
            icon={TrashIcon}
            aria-label='Remove lane'
          />
        </motion.div>
      )}
    </AnimatePresence>
  );

  const renderEditingForm = () => (
    <motion.div
      key='editing-form'
      {...ANIMATIONS.toggle}
      className='overflow-hidden'
    >
      <div className='w-full mb-3'>
        <Label
          name={`quotes.${index}.transportType`}
          required={true}
          className='text-xs'
        >
          Transport Type
        </Label>
        <Controller
          name={`quotes.${index}.transportType`}
          control={control}
          rules={{ required: 'Required' }}
          render={({ field }) => (
            <Select onValueChange={field.onChange} value={field.value}>
              <SelectTrigger className='mt-1 bg-white border-gray-300 hover:border-primary transition-colors text-xs'>
                <SelectValue placeholder='Choose' />
              </SelectTrigger>
              <SelectContent>
                {transportTypeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        />
      </div>

      <div className='flex justify-between items-start gap-1 w-full mx-0'>
        <div className='w-full'>
          <QuickQuoteTextInput
            name={`quotes.${index}.stops.0.location` as any}
            label='Pickup'
            placeholder='ZIP or City, State'
            className='text-xs'
            inputClassName='text-xs tracking-tight bg-white border-gray-300'
            required
          />
        </div>

        <div className='flex items-center justify-center mt-7'>
          <ArrowRightIcon className='w-4 h-4 text-grayscale-content-label' />
        </div>

        <div className='w-full'>
          <QuickQuoteTextInput
            name={`quotes.${index}.stops.1.location` as any}
            label='Dropoff'
            placeholder='ZIP or City, State'
            className='text-xs'
            inputClassName='text-xs tracking-tight bg-white border-gray-300'
            required
          />
        </div>
      </div>
    </motion.div>
  );

  const renderViewMode = () => {
    return (
      <motion.div
        key='view-mode'
        {...ANIMATIONS.toggle}
        className='overflow-hidden'
      >
        <div className='flex items-center gap-1.5 text-xs text-grayscale-content-1'>
          <span className='font-medium'>{values.stops[0].location}</span>
          <ArrowRightIcon className='w-3.5 h-3.5 text-grayscale-content-label' />
          <span className='font-medium'>{values.stops[1].location}</span>
        </div>
      </motion.div>
    );
  };

  const formatFinalPriceDisplay = (
    price: Maybe<number>,
    format: CarrierCostType | 'Both',
    distance: number,
    fuelEstimate: number
  ): string => {
    const finalPrice = price ?? 0;

    if (format === CarrierCostType.Linehaul && fuelEstimate) {
      return formatCurrency(finalPrice - fuelEstimate, 'USD');
    }

    if (!distance) return formatCurrency(finalPrice, 'USD');

    if (format === 'Both') {
      return `${formatCurrency(finalPrice, 'USD')} (${formatCurrency(
        finalPrice / distance,
        'USD',
        2
      )}/mile)`;
    } else if (format === CarrierCostType.PerMile) {
      return `${formatCurrency(finalPrice / distance, 'USD', 2)}/mi`;
    } else {
      return formatCurrency(finalPrice, 'USD');
    }
  };

  const formatPriceForCopy = (
    price: Maybe<number>,
    format: CarrierCostType | 'Both',
    distance: number,
    fuelEstimate: number
  ): string => {
    const finalPrice = price ?? 0;

    if (format === CarrierCostType.Linehaul && fuelEstimate) {
      const linehaulPrice = finalPrice - fuelEstimate;
      return linehaulPrice.toFixed(0);
    }

    if (!distance) return finalPrice.toFixed(0);

    if (format === 'Both') {
      // For "Both" format, keep the dollar formatting as requested
      return `${formatCurrency(finalPrice, 'USD')} (${formatCurrency(
        finalPrice / distance,
        'USD',
        2
      )}/mile)`;
    } else if (format === CarrierCostType.PerMile) {
      const perMilePrice = finalPrice / distance;
      return perMilePrice.toFixed(2);
    } else {
      // Flat rate - return plain number without dollar signs/commas
      return finalPrice.toFixed(0);
    }
  };

  // Render lane history charts
  const renderLaneHistoryCharts = () => {
    if (
      !laneHistoryResult ||
      !Object.values(laneHistoryResult.resultsBySource).length
    ) {
      return null;
    }

    return (
      <div>
        {Object.entries(laneHistoryResult.resultsBySource).map(
          ([source, history], chartIndex) => {
            let laneTierHistory = history[0];

            const isTMSChart =
              tmsLaneHistory &&
              tmsLaneHistory.length > 0 &&
              source === tmsLaneHistory[0].source;

            // If there's TMS lane history and multiple tiers, graph the selected tier
            if (
              tmsLaneHistory &&
              isTMSChart &&
              tmsLaneHistorySelectedTierIndex !== null
            ) {
              laneTierHistory = tmsLaneHistory[tmsLaneHistorySelectedTierIndex];
            }

            let laneTierOptions: LaneTier[] | undefined = undefined;
            if (tmsLaneHistory && isTMSChart) {
              laneTierOptions = tmsLaneHistory
                .filter((history) => Boolean(history.laneTier))
                .map((history) => history.laneTier as LaneTier);
            }

            const yAxisMax = Math.max(
              ...history[0].weeks
                .filter((item) => Boolean(item.maxRate))
                .map((item) => item.maxRate)
            );

            const tooltipElt = (
              inputtedTransportType: string,
              proxiedTransportType: string
            ) => (
              <div className='flex justify-between'>
                <div className='flex items-baseline gap-x-2'>
                  <Tooltip delayDuration={10}>
                    <TooltipTrigger className='border-b border-dashed border-black text-sm font-normal'>
                      {`(${titleCase(proxiedTransportType)})`}
                    </TooltipTrigger>
                    <TooltipContent className='mr-1 font-normal max-w-60 whitespace-pre-wrap'>
                      <p>{`Greenscreens does not support ${titleCase(inputtedTransportType)} quotes, showing equivalent ${titleCase(proxiedTransportType)} quote.`}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </div>
            );

            return (
              <div key={`${source}-${chartIndex}`} className='mb-4'>
                <GenericLineChart
                  data={laneTierHistory.weeks}
                  title={`${integrationNameMap[laneTierHistory.source as keyof typeof integrationNameMap]} Lane History`}
                  subtitle={
                    laneTierHistory.inputtedTransportType !==
                    laneTierHistory.proxiedTransportType
                      ? tooltipElt(
                          laneTierHistory.inputtedTransportType,
                          laneTierHistory.proxiedTransportType
                        )
                      : titleCase(laneTierHistory.proxiedTransportType)
                  }
                  // Don't repeat description for each chart
                  description={
                    chartIndex === 0
                      ? laneTierHistory.isPercentile
                        ? `${result?.response?.stops[0].state} to ${result?.response?.stops[1].state} rates from the last 4 weeks`
                        : 'Rates from the last four weeks'
                      : ''
                  }
                  toggleOptions={laneTierOptions}
                  selectedData={laneTierHistory.laneTier as string}
                  toggleDataHandler={toggleLaneHistoryGraph}
                  chartConfig={
                    laneTierHistory.isPercentile
                      ? chartConfigPercentile
                      : chartConfig
                  }
                  yAxisDomainMax={yAxisMax}
                  yAxisDomainMin={0}
                  yAxisWidth={yAxisMax > 999 ? 45 : 40}
                  thirdTooltipLabel='Loads'
                  dataKeys={
                    [
                      'maxRate',
                      'averageRate',
                      'lowestRate',
                    ] as (keyof WeekLaneData)[]
                  }
                />
              </div>
            );
          }
        )}
      </div>
    );
  };

  // Render quote results
  const renderResults = () => {
    if (!result || !result.response) return null;

    const calculator = (
      <CarrierPriceCalculator
        calculatorParent={CarrierPriceCalculatorParent.QuickQuote}
        parentQuoteRequestId={result.response.quoteRequestId}
        showTitle={false}
        mileage={maxDistance}
        finalPrice={finalPrice}
        fuelEstimate={fuelEstimate}
        datFuelSurcharge={datFuelSurcharge}
        profit={profit}
        profitType={profitType}
        maxDistance={maxDistance}
        terminatingActionHandler={async () => {}}
        setProfitTypeHandler={setProfitTypeHandler}
        carrierCost={carrierCost}
        carrierCostType={carrierCostType}
        setCarrierCostTypeHandler={setCarrierCostType}
        setCarrierCostHandler={setCarrierCost}
        setProfitHandler={setProfitHandler}
        setFuelEstimateHandler={setFuelEstimate}
        setFinalPriceHandler={setFinalPrice}
        selectedQuickQuoteId={result.response.quoteRequestId}
        mileageSource={null}
        portalFuelSurchargeSource={null}
        portalFuelSurcharge={null}
      />
    );

    const quoteCards = allQuoteCards.map((card, cardIndex) => (
      <motion.div
        key={card.type}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.05 * cardIndex }}
      >
        <QuoteCard
          carrier={card}
          isSelected={selectedCarrier === card.type}
          onClick={() => {
            setSelectedCarrier(card.type);
            if (carrierCostType === CarrierCostType.Flat) {
              setCarrierCost(Math.round(card.cost * 100) / 100);
            } else {
              if (card?.costPerMile) {
                setCarrierCost(card?.costPerMile);
              } else {
                setCarrierCostType(CarrierCostType.Flat);
                setCarrierCost(Math.round(card.cost * 100) / 100);
              }
            }
          }}
          lowConfidenceThreshold={
            result.response?.configuration?.lowConfidenceThreshold || 70
          }
          mediumConfidenceThreshold={
            result.response?.configuration?.mediumConfidenceThreshold || 80
          }
        />
      </motion.div>
    ));

    const quoteInfo = (
      <div className='text-xs'>
        <div className=' w-full text-left text-grayscale-content-label font-semibold'>
          Quote for
        </div>
        <h3 className='w-full text-left'>
          {`
          ${useHelperFunctions.toTitleCase(values.stops[0].city)}, ${values.stops[0].state} to
          ${useHelperFunctions.toTitleCase(values.stops[1].city)}, ${values.stops[1].state}
        `}
        </h3>
        {maxDistance > 0 && (
          <h3 className='w-full text-left flex items-center gap-2'>
            {`Distance: ${_.round(maxDistance)} miles`}
            <Tooltip delayDuration={10}>
              <TooltipTrigger asChild>
                <InfoIcon className='h-3.5 w-3.5' />
              </TooltipTrigger>
              <TooltipContent>
                {distanceSource && (
                  <p>{`Distance from ${integrationNameMap[distanceSource]}`}</p>
                )}
              </TooltipContent>
            </Tooltip>

            {result?.response?.stops && (
              <Tooltip delayDuration={10}>
                <TooltipTrigger asChild>
                  <a
                    href={constructGoogleMapsUrl(result.response.stops)}
                    target='_blank'
                    rel='noreferrer'
                    className='text-orange-main/60 hover:text-orange-main transition-colors'
                    aria-label='View route in Google Maps'
                  >
                    <MapIcon className='h-4 w-4' />
                  </a>
                </TooltipTrigger>
                <TooltipContent>View route in Google Maps</TooltipContent>
              </Tooltip>
            )}
          </h3>
        )}
      </div>
    );

    return (
      <div className='mt-2'>
        <div className='hidden'>{calculator}</div>
        <div className='h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent my-2' />
        <div
          className='flex justify-between items-center cursor-pointer p-2 -m-2 hover:scale-[1.01] transition-all duration-200'
          onClick={onToggleExpand}
          onKeyDown={(e) => e.key === 'Enter' && onToggleExpand()}
          role='button'
          tabIndex={0}
          aria-label={
            isExpanded ? 'Collapse quote details' : 'Expand quote details'
          }
        >
          <div className='flex items-center gap-2'>
            <p className='text-sm font-medium text-gray-700'>
              {priceFormat === CarrierCostType.Linehaul
                ? 'Linehaul Rate'
                : 'Sell Rate'}
            </p>
          </div>
          <div className='flex items-center gap-2'>
            <span className='text-sm font-bold text-grayscale-label'>
              {formatFinalPriceDisplay(
                finalPrice,
                priceFormat,
                maxDistance,
                fuelEstimate
              )}
            </span>
            {isExpanded ? (
              <ChevronUpIcon className='w-4 h-4 text-gray-500' />
            ) : (
              <ChevronDownIcon className='w-4 h-4 text-gray-500' />
            )}
          </div>
        </div>
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              key='details'
              {...ANIMATIONS.toggle}
              className='flex flex-col gap-2 mt-2 w-full'
            >
              <div className='h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent' />

              {quoteInfo}
              {quoteCards}

              {/* Lane History Charts */}
              {(isQuoteLaneHistoryEnabled || isTMSLaneHistoryEnabled) && (
                <div className='mt-3'>
                  {laneHistoryResult &&
                  Object.values(laneHistoryResult.resultsBySource).length ? (
                    renderLaneHistoryCharts()
                  ) : (
                    <div className='flex justify-center py-2'>
                      <p className='text-xs text-grayscale-content-input'>
                        No Lane History Available
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Lane History from Service Chart */}
              {isGetLaneRateFromServiceEnabled &&
                laneHistoryFromServiceResult && (
                  <div className='mt-3'>
                    <LaneHistoryFromServiceChart
                      months={laneHistoryFromServiceResult.months}
                      last7Days={laneHistoryFromServiceResult.last7Days}
                      title='DAT Rate History'
                      subtitle={titleCase(
                        laneHistoryFromServiceResult.equipment
                      )}
                      description='Monthly rate trends with 7-day recent data'
                    />
                  </div>
                )}

              {calculator}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  // Function that can be called by parent to externally update selected carrier
  const externallySetSelectedCarrier = (carrierType: SelectedCarrierType) => {
    if (!allQuoteCards?.length) return;
    const card = allQuoteCards.find(
      (c: QuoteCardType) => c.type === carrierType
    );
    if (!card) return;

    setSelectedCarrier(card.type);

    if (carrierCostType === CarrierCostType.Flat) {
      setCarrierCost(Math.round(card.cost * 100) / 100);
    } else if (card?.costPerMile) {
      setCarrierCost(card.costPerMile);
    } else {
      // Fallback to flat rate
      setCarrierCostType(CarrierCostType.Flat);
      setCarrierCost(Math.round(card.cost * 100) / 100);
    }
  };

  // Function that can be called by parent to externally update price format
  const externallySetPriceFormat = (
    newPriceFormat: CarrierCostType | 'Both'
  ) => {
    setPriceFormat(newPriceFormat);
    // Notify parent of the change
    if (onPriceFormatChange) {
      onPriceFormatChange(index, newPriceFormat);
    }
  };

  // Register the setter with parent whenever result or state changes
  useEffect(() => {
    if (registerCarrierSetter) {
      registerCarrierSetter(index, externallySetSelectedCarrier);
      // Clean up on unmount
      return () => {
        registerCarrierSetter(index, null as any);
      };
    }
    // Explicitly return undefined so all code paths have a return value
    return undefined;
  }, [registerCarrierSetter, index, result, carrierCostType, allQuoteCards]);

  // Register the price format setter with parent
  useEffect(() => {
    if (registerPriceFormatSetter) {
      registerPriceFormatSetter(index, externallySetPriceFormat);
      // Clean up on unmount
      return () => {
        registerPriceFormatSetter(index, null as any);
      };
    }
    // Explicitly return undefined so all code paths have a return value
    return undefined;
  }, [registerPriceFormatSetter, index]);

  // Notify parent of initial price format
  useEffect(() => {
    if (onPriceFormatChange) {
      onPriceFormatChange(index, priceFormat);
    }
  }, [onPriceFormatChange, index, priceFormat]);

  useEffect(() => {
    return () => {
      if (copyTimeoutRef.current) {
        clearTimeout(copyTimeoutRef.current);
      }
    };
  }, []);

  return (
    <motion.div
      layout
      transition={{ type: 'spring', duration: 0.2, bounce: 0 }}
      {...ANIMATIONS.card}
      className={`overflow-hidden rounded-[4px] border bg-gradient-to-br shadow-sm relative transition-all duration-200 ${
        isSelected
          ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100/75'
          : 'border-gray-400 bg-gradient-to-br from-gray-50 to-blue-50/50'
      }`}
    >
      <div className='p-2.5'>
        <div className='flex justify-between items-center mb-2'>
          <div className='flex items-center gap-3'>
            <div className='relative flex h-4 w-4 items-center justify-center group'>
              <Checkbox
                checked={isSelected}
                onCheckedChange={onSelect}
                aria-label={`Select lane ${index + 1}`}
                showCheck={false}
                className={cn(
                  'h-5 w-5 bg-white hover:scale-105 transition-all duration-200',
                  isSelected
                    ? ''
                    : 'group-hover:bg-gray-600 group-hover:border-gray-600'
                )}
              />
              <span
                className={cn(
                  'absolute pointer-events-none text-xs font-medium text-gray-500',
                  isSelected
                    ? 'text-white'
                    : 'text-grayscale-content-2 group-hover:text-white'
                )}
              >
                {index + 1}
              </span>
            </div>
            {!isEditing && values?.transportType && (
              <span className='text-xs text-blue-600 font-semibold'>
                {transportTypeOptions.find(
                  (o) => o.value === values.transportType
                )?.label || ''}
              </span>
            )}
          </div>

          {/* Action buttons */}
          {renderActions()}
        </div>

        <AnimatePresence mode='wait'>
          {isEditing ? renderEditingForm() : renderViewMode()}
        </AnimatePresence>

        {/* Quote results */}
        {renderResults()}
      </div>
    </motion.div>
  );
};

const ANIMATIONS = {
  card: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  toggle: {
    initial: { opacity: 0, height: 0 },
    animate: { opacity: 1, height: 'auto' },
    exit: { opacity: 0, height: 0 },
    transition: { duration: 0.15 },
  },
  actions: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 },
    transition: { duration: 0.1 },
  },
};
