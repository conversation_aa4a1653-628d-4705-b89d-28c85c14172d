// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { useContext, useEffect, useMemo, useState } from 'react';

import { DollarSign, List } from 'lucide-react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/Accordion';
import {
  DrumkitPlatform,
  SidebarStateContext,
} from 'contexts/sidebarStateContext';
import useLogPostHogPageView from 'hooks/useLogPostHogPageView';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { Email } from 'types/Email';
import { Maybe } from 'types/UtilityTypes';
import Pageview from 'types/enums/Pageview';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';

import BatchQuoteForm from './BatchQuoteForm';
import QuickQuoteForm from './QuickQuoteForm';

enum QuickQuoteSections {
  QuickQuote = 'quick-quote',
  BatchQuote = 'batch-quote',
}

type QuickQuoteSectionsArray = QuickQuoteSections[] | undefined;

export default function QuickQuoteTab({ email }: { email: Maybe<Email> }) {
  const { serviceID, serviceFeaturesEnabled } = useServiceFeatures();
  const {
    currentState: { curSuggestionList, drumkitPlatform },
  } = useContext(SidebarStateContext);

  const quickQuoteSuggestions = useMemo(
    () =>
      curSuggestionList.filter(
        (s) => s.pipeline === SuggestionPipelines.QuickQuote
      ),
    [curSuggestionList]
  );

  const quickQuoteSuggestionCount = quickQuoteSuggestions.length;

  const [accordionTab, setAccordionTab] =
    useState<QuickQuoteSectionsArray>(undefined);

  useEffect(() => {
    if (quickQuoteSuggestionCount === 1) {
      setAccordionTab([QuickQuoteSections.QuickQuote]);
    } else if (
      quickQuoteSuggestionCount > 1 &&
      serviceFeaturesEnabled.isBatchQuoteEnabled
    ) {
      setAccordionTab([QuickQuoteSections.BatchQuote]);
    } else {
      // No suggestions, show quick quote and batch quote if enabled
      const tabs = [QuickQuoteSections.QuickQuote];
      if (serviceFeaturesEnabled.isBatchQuoteEnabled) {
        tabs.push(QuickQuoteSections.BatchQuote);
      }
      setAccordionTab(tabs);
    }
  }, [quickQuoteSuggestionCount, serviceFeaturesEnabled.isBatchQuoteEnabled]);

  const hasQRSuggestions = quickQuoteSuggestionCount > 0;

  useLogPostHogPageView(
    Pageview.QuickQuote,
    {
      serviceID,
      emailID: email?.id,
      hasQRSuggestions,
    },
    drumkitPlatform === DrumkitPlatform.Outlook
      ? ['serviceID', 'emailID']
      : ['serviceID']
  );

  return (
    <div className='mb-4'>
      {accordionTab && (
        <Accordion
          type='multiple'
          value={accordionTab}
          onValueChange={(v) => setAccordionTab(v as QuickQuoteSections[])}
        >
          <AccordionItem value={QuickQuoteSections.QuickQuote}>
            <AccordionTrigger
              icon={<DollarSign className='h-5 w-5' strokeWidth={2} />}
            >
              Quick Quote
            </AccordionTrigger>
            <AccordionContent>
              <QuickQuoteForm email={email} />
            </AccordionContent>
          </AccordionItem>

          {serviceFeaturesEnabled.isBatchQuoteEnabled && (
            <AccordionItem value={QuickQuoteSections.BatchQuote}>
              <AccordionTrigger
                icon={<List className='h-5 w-5' strokeWidth={2} />}
                underline={false}
                className='group'
              >
                <div className='flex items-center gap-2'>
                  <span className='group-hover:underline'>Batch Quote</span>
                  <span className='text-[10px] text-white bg-primary/90 rounded-full px-2 py-0.5'>
                    Beta
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <BatchQuoteForm
                  suggestions={quickQuoteSuggestions}
                  email={email}
                />
              </AccordionContent>
            </AccordionItem>
          )}
        </Accordion>
      )}
    </div>
  );
}
