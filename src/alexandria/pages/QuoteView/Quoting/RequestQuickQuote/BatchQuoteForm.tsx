import {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  FormProvider,
  UseFormReturn,
  useFieldArray,
  useForm,
  useWatch,
} from 'react-hook-form';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore lodash is in the parent dir
import _ from 'lodash';
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CopyIcon,
  PlusIcon,
  SparklesIcon,
  Truck,
} from 'lucide-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore mustache is in the parent dir
import Mustache from 'mustache';

import { Button } from 'components/Button';
import { Checkbox } from 'components/Checkbox';
import { Textarea } from 'components/Textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import ButtonLoader from 'components/loading/ButtonLoader';
import {
  DrumkitPlatform,
  SidebarStateContext,
  isEmailPlatform,
} from 'contexts/sidebarStateContext';
import { useCustomers } from 'hooks/useCustomers';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { LaneHistoryResponse, getLaneHistory } from 'lib/api/getLaneHistory';
import { LaneHistoryFromServiceResponse } from 'lib/api/getLaneHistoryFromService';
import { SelectedCarrierType, getQuickQuote } from 'lib/api/getQuickQuote';
import { Stop } from 'lib/api/getQuickQuote';
import { updateQuoteRequestSuggestion } from 'lib/api/updateQuoteRequestSuggestion';
import { createMailClientInstance } from 'lib/mailclient/interface';
import { Email } from 'types/Email';
import { TransportType } from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import ButtonText from 'types/enums/ButtonText';
import { TMS } from 'types/enums/Integrations';
import { GenericSuggestion } from 'types/suggestions/CoreSuggestions';
import { SuggestionStatus } from 'types/suggestions/LoadSuggestions';
import { QuoteChanges } from 'types/suggestions/QuoteSuggestions';
import captureException from 'utils/captureException';
import { copyToClipboard } from 'utils/copyToClipboard';
import { formatCurrency } from 'utils/formatCurrency';
import { titleCase } from 'utils/formatStrings';
import { mapCustomerToAntdOptions } from 'utils/loadInfoAndBuilding';
import { calculatePricing } from 'utils/priceCalculations';
import { cn } from 'utils/shadcn';

import BatchQuoteEdit from './BatchQuoteEdit';
import { BatchQuoteInputCard } from './BatchQuoteInputCard';
import { QuoteCountries, useHelperFunctions } from './helperFunctions';
import {
  BatchEditValues,
  BatchQuoteFormInput,
  BatchQuoteFormValues,
  BatchQuoteResult,
  CarrierCostType,
  ProfitType,
  getTransportTypeOptions,
} from './types';

interface BatchQuoteFormProps {
  suggestions: GenericSuggestion[];
  email: Maybe<Email>;
}

const EMPTY_STOP: Stop = {
  zip: '',
  city: '',
  state: '',
  country: QuoteCountries.USA,
  location: '',
};

const BatchQuoteForm = ({ suggestions, email }: BatchQuoteFormProps) => {
  const { toast } = useToast();
  const {
    quickQuoteConfig,
    tmsIntegrations,
    serviceFeaturesEnabled: {
      isQuoteLaneHistoryEnabled,
      isTMSLaneHistoryEnabled,
      isGetLaneRateFromServiceEnabled,
    },
  } = useServiceFeatures();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [results, setResults] = useState<(BatchQuoteResult | null)[]>([]);
  const [laneHistoryResults, setLaneHistoryResults] = useState<
    (LaneHistoryResponse | null)[]
  >([]);
  const [laneHistoryFromServiceResults, setLaneHistoryFromServiceResults] =
    useState<(LaneHistoryFromServiceResponse | null)[]>([]);
  const [selectedLanes, setSelectedLanes] = useState<Set<number>>(new Set());
  const [showBatchEdit, setShowBatchEdit] = useState(false);
  const [expandedLanes, setExpandedLanes] = useState<Set<number>>(new Set());
  const [draftResponse, setDraftResponse] = useState('');
  const [userEditedDraft, setUserEditedDraft] = useState(false);
  const [hasCopiedDraftResponse, setHasCopiedDraftResponse] = useState(false);
  const [loadingDraftReply, setLoadingDraftReply] = useState(false);
  const formContainerRef = useRef<HTMLFormElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const {
    currentState: { drumkitPlatform, threadItemId, isOutlookReply },
    setCurrentState,
  } = useContext(SidebarStateContext);

  const [lastBatchEditMargin, setLastBatchEditMargin] = useState(() => {
    const defaultProfitType =
      (quickQuoteConfig?.defaultMarginType as ProfitType) ||
      ProfitType.Percentage;
    const defaultProfit =
      defaultProfitType === ProfitType.Amount
        ? quickQuoteConfig?.defaultFlatMargin || 100
        : quickQuoteConfig?.defaultPercentMargin || 10;

    return {
      margin: defaultProfit.toString(),
      marginType: defaultProfitType,
    };
  });

  const transportTypeOptions = useMemo(
    () => getTransportTypeOptions(quickQuoteConfig),
    [quickQuoteConfig]
  );

  const createEmptyQuote = (): BatchQuoteFormInput => {
    const defaultProfitType =
      (quickQuoteConfig?.defaultMarginType as ProfitType) ||
      ProfitType.Percentage;
    const defaultProfit =
      defaultProfitType === ProfitType.Amount
        ? quickQuoteConfig?.defaultFlatMargin || 100
        : quickQuoteConfig?.defaultPercentMargin || 10;
    return {
      transportType: TransportType.VAN,
      stops: [EMPTY_STOP, EMPTY_STOP],
      pickupDate: new Date(),
      deliveryDate: new Date(),
      isSubmitToTMS: false,
      customerName: '',
      quoteNumber: '',
      quoteExpirationDate: new Date(),
      quoteEta: new Date(),
      profit: defaultProfit,
      profitType: defaultProfitType,
    };
  };

  const memoizedDefaultValues = useMemo<BatchQuoteFormValues>(() => {
    const defaultCustomerName = suggestions.length
      ? ((suggestions[0].suggested as QuoteChanges)?.customerExternalTMSID ??
        '')
      : '';

    if (suggestions.length === 0) {
      return { customerName: defaultCustomerName, quotes: [] };
    }

    const defaultProfitType =
      (quickQuoteConfig?.defaultMarginType as ProfitType) ||
      ProfitType.Percentage;
    const defaultProfit =
      defaultProfitType === ProfitType.Amount
        ? quickQuoteConfig?.defaultFlatMargin || 100
        : quickQuoteConfig?.defaultPercentMargin || 10;

    const defaultQuotes = suggestions.map((suggestion) => {
      const suggestedFields = suggestion.suggested as QuoteChanges;

      // Process suggested pickup location
      const pickupLocation = useHelperFunctions.parseLocation(
        suggestedFields?.pickupZip
          ? suggestedFields.pickupZip
          : `${suggestedFields?.pickupCity}, ${suggestedFields?.pickupState}`
      );

      // Process suggested delivery location
      const deliveryLocation = useHelperFunctions.parseLocation(
        suggestedFields?.deliveryZip
          ? suggestedFields.deliveryZip
          : `${suggestedFields?.deliveryCity}, ${suggestedFields?.deliveryState}`
      );

      return {
        transportType: suggestedFields?.transportType ?? '',
        stops: [
          {
            location: pickupLocation
              ? pickupLocation.zip
                ? pickupLocation.zip
                : `${pickupLocation.city}, ${pickupLocation.state}`
              : '',
            city: pickupLocation?.city ?? '',
            state: pickupLocation?.state ?? '',
            zip: pickupLocation?.zip ?? '',
            country: pickupLocation?.country ?? '',
          },
          {
            location: deliveryLocation
              ? deliveryLocation.zip
                ? deliveryLocation.zip
                : `${deliveryLocation.city}, ${deliveryLocation.state}`
              : '',
            city: deliveryLocation?.city ?? '',
            state: deliveryLocation?.state ?? '',
            zip: deliveryLocation?.zip ?? '',
            country: deliveryLocation?.country ?? '',
          },
        ],
        pickupDate: new Date(),
        deliveryDate: new Date(),
        isSubmitToTMS: false,
        customerName: defaultCustomerName,
        quoteNumber: '',
        quoteExpirationDate: new Date(),
        quoteEta: new Date(),
        profit: defaultProfit,
        profitType: defaultProfitType,
      } as BatchQuoteFormInput;
    });

    return { customerName: defaultCustomerName, quotes: defaultQuotes };
  }, [suggestions, quickQuoteConfig]);

  const formMethods = useForm<BatchQuoteFormValues>({
    defaultValues: memoizedDefaultValues,
    reValidateMode: 'onChange',
  });

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    getValues,
    formState: { errors },
  } = formMethods;

  // Watch form values to trigger draft updates when margins change
  const watchedQuotes = useWatch({
    control,
    name: 'quotes',
  });

  const { fields, remove, append } = useFieldArray({
    control,
    name: 'quotes',
  });

  // Watch for customerName changes and propagate will be handled later after fields are defined
  const watchedCustomerName = useWatch({ control, name: 'customerName' });

  // Initialize form with suggestions
  useEffect(() => {
    if (suggestions.length > 0) {
      reset(memoizedDefaultValues);
      setSelectedLanes(new Set());
      setExpandedLanes(new Set());
    }
  }, [suggestions, memoizedDefaultValues, reset]);

  // Reset form and results when email changes
  useEffect(() => {
    setResults([]);
    setLaneHistoryResults([]);
    reset(memoizedDefaultValues);
    setSelectedLanes(new Set());
    setExpandedLanes(new Set());
  }, [email, reset, memoizedDefaultValues]);

  // Propagate customer to all lanes whenever it changes
  useEffect(() => {
    if (watchedCustomerName !== undefined) {
      fields.forEach((_, idx) => {
        setValue(`quotes.${idx}.customerName`, watchedCustomerName);
      });
    }
  }, [watchedCustomerName, fields.length]);

  const createProxiedFormMethods = (
    formMethods: UseFormReturn<BatchQuoteFormValues>,
    index: number
  ) => {
    return {
      ...formMethods,
      setError: (fieldName: any, error: any, options: any) => {
        const newFieldName = `quotes.${index}.${fieldName}` as any;
        formMethods.setError(newFieldName, error, options);
      },
    };
  };

  const handleAddLane = () => {
    append(createEmptyQuote());

    // Propagate selected customer to the new lane
    const currentCustomer = getValues('customerName');
    const newIndex = fields.length; // index after append
    if (currentCustomer) {
      setValue(`quotes.${newIndex}.customerName`, currentCustomer);
    }

    // Clear any existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      if (formContainerRef.current) {
        const newLaneElements =
          formContainerRef.current.querySelectorAll('[data-lane-card]');
        const lastLane = newLaneElements[newLaneElements.length - 1];
        if (lastLane) {
          lastLane.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      }
      scrollTimeoutRef.current = null;
    }, 100);
  };

  const handleSelectLane = (index: number, isSelected: boolean) => {
    setSelectedLanes((prev) => {
      const newSelected = new Set(prev);
      if (isSelected) {
        newSelected.add(index);
      } else {
        newSelected.delete(index);
        if (newSelected.size === 0) {
          setShowBatchEdit(false);
        }
      }
      return newSelected;
    });
  };

  const handleToggleExpandLane = (index: number) => {
    setExpandedLanes((prev) => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(index)) {
        newExpanded.delete(index);
      } else {
        newExpanded.add(index);
      }
      return newExpanded;
    });
  };

  const handleSelectAll = () => {
    if (selectedLanes.size === fields.length) {
      setSelectedLanes(new Set());
      setShowBatchEdit(false);
    } else {
      setSelectedLanes(new Set(fields.map((_, index) => index)));
    }
  };

  const handleInitiateBatchEdit = () => {
    setShowBatchEdit(true);
  };

  const handleCancelBatchEdit = () => {
    setShowBatchEdit(false);
  };

  const handleApplyBatchEdit = (batchValues: BatchEditValues) => {
    setLastBatchEditMargin({
      margin: batchValues.margin,
      marginType: batchValues.marginType,
    });

    selectedLanes.forEach((index) => {
      if (batchValues.transportType) {
        setValue(
          `quotes.${index}.transportType`,
          batchValues.transportType as TransportType
        );
      }

      if (batchValues.pickupLocation) {
        const parsedPickup = useHelperFunctions.parseLocation(
          batchValues.pickupLocation
        );
        if (parsedPickup) {
          setValue(
            `quotes.${index}.stops.0.location`,
            batchValues.pickupLocation
          );
          setValue(`quotes.${index}.stops.0.city`, parsedPickup.city);
          setValue(`quotes.${index}.stops.0.state`, parsedPickup.state);
          setValue(`quotes.${index}.stops.0.zip`, parsedPickup.zip);
          setValue(`quotes.${index}.stops.0.country`, parsedPickup.country);
        }
      }

      if (batchValues.dropoffLocation) {
        const parsedDropoff = useHelperFunctions.parseLocation(
          batchValues.dropoffLocation
        );
        if (parsedDropoff) {
          setValue(
            `quotes.${index}.stops.1.location`,
            batchValues.dropoffLocation
          );
          setValue(`quotes.${index}.stops.1.city`, parsedDropoff.city);
          setValue(`quotes.${index}.stops.1.state`, parsedDropoff.state);
          setValue(`quotes.${index}.stops.1.zip`, parsedDropoff.zip);
          setValue(`quotes.${index}.stops.1.country`, parsedDropoff.country);
        }
      }

      if (batchValues.margin) {
        setValue(`quotes.${index}.profit`, parseFloat(batchValues.margin));
        setValue(`quotes.${index}.profitType`, batchValues.marginType);
      }
    });

    // Apply buy rate source to selected lanes via registered setters
    if (batchValues.buyRateSource) {
      selectedLanes.forEach((index) => {
        const setter = carrierSetterMapRef.current.get(index);
        if (setter) {
          setter(batchValues.buyRateSource as SelectedCarrierType);
        }
      });
    }

    // Apply price format to selected lanes via registered setters
    if (batchValues.priceFormat) {
      selectedLanes.forEach((index) => {
        const setter = priceFormatSetterMapRef.current.get(index);
        if (setter) {
          setter(batchValues.priceFormat as CarrierCostType | 'Both');
          // Also update our tracking ref
          laneCurrentPriceFormatsRef.current.set(
            index,
            batchValues.priceFormat as CarrierCostType | 'Both'
          );
        }
      });
    }

    setShowBatchEdit(false);
  };

  const handleRemoveQuote = useCallback(
    (index: number) => {
      remove(index);

      setResults((prev) => {
        const newResults = [...prev];
        newResults.splice(index, 1);
        return newResults;
      });

      setLaneHistoryResults((prev) => {
        const newResults = [...prev];
        newResults.splice(index, 1);
        return newResults;
      });

      // Update expanded lanes, reindexing as needed
      setExpandedLanes((prev) => {
        const newExpanded = new Set<number>();
        prev.forEach((expandedIdx) => {
          if (expandedIdx < index) {
            newExpanded.add(expandedIdx);
          } else if (expandedIdx > index) {
            newExpanded.add(expandedIdx - 1);
          }
        });
        return newExpanded;
      });

      // Clean up Map references
      carrierSetterMapRef.current.delete(index);
      laneCalculatedPricesRef.current.delete(index);
      priceFormatSetterMapRef.current.delete(index);
      laneCurrentPriceFormatsRef.current.delete(index);

      // Reindex remaining Map entries
      const carrierSetters = new Map<
        number,
        (carrier: SelectedCarrierType) => void
      >();
      const laneCalculatedPrices = new Map<
        number,
        {
          finalPrice: number;
          maxDistance: number;
          fuelEstimate: number;
        }
      >();
      const priceFormatSetters = new Map<
        number,
        (priceFormat: CarrierCostType | 'Both') => void
      >();
      const laneCurrentPriceFormats = new Map<
        number,
        CarrierCostType | 'Both'
      >();

      carrierSetterMapRef.current.forEach((setter, mapIndex) => {
        if (mapIndex > index) {
          carrierSetters.set(mapIndex - 1, setter);
        } else if (mapIndex < index) {
          carrierSetters.set(mapIndex, setter);
        }
      });

      laneCalculatedPricesRef.current.forEach((price, mapIndex) => {
        if (mapIndex > index) {
          laneCalculatedPrices.set(mapIndex - 1, price);
        } else if (mapIndex < index) {
          laneCalculatedPrices.set(mapIndex, price);
        }
      });

      priceFormatSetterMapRef.current.forEach((setter, mapIndex) => {
        if (mapIndex > index) {
          priceFormatSetters.set(mapIndex - 1, setter);
        } else if (mapIndex < index) {
          priceFormatSetters.set(mapIndex, setter);
        }
      });

      laneCurrentPriceFormatsRef.current.forEach((format, mapIndex) => {
        if (mapIndex > index) {
          laneCurrentPriceFormats.set(mapIndex - 1, format);
        } else if (mapIndex < index) {
          laneCurrentPriceFormats.set(mapIndex, format);
        }
      });

      carrierSetterMapRef.current = carrierSetters;
      laneCalculatedPricesRef.current = laneCalculatedPrices;
      priceFormatSetterMapRef.current = priceFormatSetters;
      laneCurrentPriceFormatsRef.current = laneCurrentPriceFormats;
    },
    [remove]
  );

  const onSubmit = handleSubmit(async (data: BatchQuoteFormValues) => {
    setIsSubmitting(true);
    setResults([]);
    setLaneHistoryResults([]);
    setLaneHistoryFromServiceResults([]);

    const promises = data.quotes.map(async (quoteData, index) => {
      const pickup = useHelperFunctions.parseLocation(
        quoteData.stops[0].location || ''
      );
      const delivery = useHelperFunctions.parseLocation(
        quoteData.stops[1].location || ''
      );

      if (!pickup || !delivery) {
        if (!pickup) {
          formMethods.setError(`quotes.${index}.stops.0.location` as any, {
            message: 'Invalid location',
          });
        }
        if (!delivery) {
          formMethods.setError(`quotes.${index}.stops.1.location` as any, {
            message: 'Invalid location',
          });
        }
        return { quote: null, laneHistory: null };
      }

      const updatedFormValues = {
        ...quoteData,
        stops: [
          {
            zip: pickup.zip,
            city: pickup.city,
            state: pickup.state,
            country: pickup.country,
            location: quoteData.stops[0].location,
          },
          {
            zip: delivery.zip,
            city: delivery.city,
            state: delivery.state,
            country: delivery.country,
            location: quoteData.stops[1].location,
          },
        ],
      };

      // Get quote response
      const response = await getQuickQuote({
        email: email,
        clickedSuggestion: suggestions[index],
        formValues: updatedFormValues,
        formMethods: createProxiedFormMethods(formMethods, index) as any,
        setQuoteNotConfidentHandler: () => {},
        setProfit: () => {},
        profitType: quoteData.profitType,
      });

      let quoteResult = null;
      let laneHistoryResult = null;
      let laneHistoryFromServiceResult = null;

      if (response) {
        const cards = useHelperFunctions.parseQuoteCardsFromResponse({
          newQuote: response,
          formValues: updatedFormValues,
          setDATFuelSurcharge: () => {},
        });
        quoteResult = { response, cards };

        // Fetch lane history if enabled and we have a valid quote request ID
        if (
          (isQuoteLaneHistoryEnabled || isTMSLaneHistoryEnabled) &&
          response.quoteRequestId
        ) {
          try {
            const laneHistoryResponse = await getLaneHistory({
              quoteRequestId: response.quoteRequestId,
              originCity: pickup.city,
              originState: pickup.state,
              originZip: pickup.zip,
              originCountry: pickup.country,
              destinationCity: delivery.city,
              destinationState: delivery.state,
              destinationZip: delivery.zip,
              destinationCountry: delivery.country,
              transportType: updatedFormValues.transportType,
            });

            if (laneHistoryResponse.isOk()) {
              laneHistoryResult = laneHistoryResponse.value;
            }
          } catch (error) {
            captureException(error, {
              functionName: 'batchQuote_getLaneHistory',
            });
          }
        }

        // Fetch lane history from service if enabled
        if (isGetLaneRateFromServiceEnabled && response.quoteRequestId) {
          try {
            const laneHistoryResponse =
              await useHelperFunctions.fetchLaneHistoryFromService({
                quoteRequestId: response.quoteRequestId,
                originCity: pickup.city,
                originState: pickup.state,
                originZip: pickup.zip,
                originCountry: pickup.country,
                destinationCity: delivery.city,
                destinationState: delivery.state,
                destinationZip: delivery.zip,
                destinationCountry: delivery.country,
                transportType: updatedFormValues.transportType,
              });

            if (laneHistoryResponse.isOk()) {
              laneHistoryFromServiceResult = laneHistoryResponse.value;
            }
          } catch (error) {
            captureException(error, {
              functionName: 'batchQuote_getLaneHistoryFromService',
            });
          }
        }
      }

      return {
        quote: quoteResult,
        laneHistory: laneHistoryResult,
        laneHistoryFromService: laneHistoryFromServiceResult,
      };
    });

    const settledResults = await Promise.all(promises);

    // Separate quote results and lane history results
    const quoteResults = settledResults.map((result) => result?.quote ?? null);
    const laneHistoryResults = settledResults.map(
      (result) => result?.laneHistory ?? null
    );
    const laneHistoryFromServiceResults = settledResults.map(
      (result) => result?.laneHistoryFromService ?? null
    );

    setResults(quoteResults as (BatchQuoteResult | null)[]);
    setLaneHistoryResults(laneHistoryResults);
    setLaneHistoryFromServiceResults(laneHistoryFromServiceResults);
    setIsSubmitting(false);
  });

  const isAllSelected =
    fields.length > 0 && selectedLanes.size === fields.length;
  const hasSelection = selectedLanes.size > 0;
  const isSubmitButtonDisabled = isSubmitting || fields.length === 0;

  // Number of lanes that have results and are expandable
  const expandableLanesCount = useMemo(
    () => results.filter((r) => r).length,
    [results]
  );
  const allLanesExpanded =
    expandableLanesCount > 0 && expandedLanes.size === expandableLanesCount;

  const handleToggleExpandAll = () => {
    if (allLanesExpanded) {
      setExpandedLanes(new Set());
    } else {
      const allResultIndices = results
        .map((result, index) => (result ? index : -1))
        .filter((index) => index !== -1);
      setExpandedLanes(new Set(allResultIndices));
    }
  };

  // Batch quote email template
  // TODO: use email_templates from backend, need to make batch quote email templates
  const BATCH_QUOTE_TEMPLATE = `{{#hasCustomer}}Hi {{customerName}},

{{/hasCustomer}}{{^hasCustomer}}Hi,

{{/hasCustomer}}Here are the requested quotes:

{{#quotes}}
Quote {{quoteNumber}}: {{pickupLocation}} to {{deliveryLocation}}
Transport Type: {{transportType}}
Price: {{price}}

{{/quotes}}`;

  // Helper function to decode HTML entities
  const decodeHtmlEntities = (text: string): string => {
    const textArea = document.createElement('textarea');
    textArea.innerHTML = text;
    return textArea.value;
  };

  // Format price based on individual lane's selected format
  const formatPriceForDraft = (
    finalPrice: number,
    maxDistance: number,
    fuelEstimate: number,
    priceFormat: CarrierCostType | 'Both'
  ): string => {
    if (priceFormat === CarrierCostType.Linehaul && fuelEstimate) {
      return formatCurrency(finalPrice - fuelEstimate, 'USD');
    }

    if (!maxDistance) return formatCurrency(finalPrice, 'USD');

    if (priceFormat === 'Both') {
      return `${formatCurrency(finalPrice, 'USD')} (${formatCurrency(
        finalPrice / maxDistance,
        'USD',
        2
      )}/mi)`;
    } else if (priceFormat === CarrierCostType.PerMile) {
      return `${formatCurrency(finalPrice / maxDistance, 'USD', 2)}/mi`;
    } else {
      return formatCurrency(finalPrice, 'USD');
    }
  };

  // Reset user edited flag when results change (new quotes generated)
  useEffect(() => {
    setUserEditedDraft(false);
  }, [results]);

  /* handleTerminatingAction should be called whenever user takes an action that "terminates" the quote requests,
   * i.e. updates the QR status from "inFlight" to 1 of 2 terminating statuses: "accepted" or "rejected".
   * As of 6/4/2025, this occurs when user clicks "Create Draft Reply" or one of the copy buttons.
   * This function
   * 1) Updates BE quote request with final quote and margin values (critical for metrics)
   * 2) Sends user quote to service (if enabled)
   * 3) Updates UI to show success or failure toast
   */
  const handleTerminatingAction = async () => {
    // Process each quote result that has completed
    const quotesWithResults = results
      .map((result, index) => {
        if (!result?.response) return null;

        const formData = getValues(`quotes.${index}`);
        const suggestion = suggestions[index];

        // Get the carrier cost from the cheapest card (same logic as individual cards)
        const carrierCost = result.cards?.length
          ? result.cards.reduce((prev, curr) =>
              prev.cost < curr.cost ? prev : curr
            ).cost
          : 0;

        const maxDistance =
          result.response.quotes?.reduce(
            (max, current) => (current.distance > max.distance ? current : max),
            { distance: 0 }
          ).distance || 0;

        const profit = formData.profit || 0;
        const profitType = formData.profitType || ProfitType.Percentage;

        // Use the same price calculation logic as the individual cards
        const { flatCarrierCost, finalProfit } = calculatePricing(
          carrierCost,
          CarrierCostType.Flat, // For terminating action calculation, use flat carrier cost
          profit,
          profitType,
          maxDistance
        );

        const finalPrice = flatCarrierCost + finalProfit;

        return {
          suggestion,
          result,
          formData,
          carrierCost: flatCarrierCost,
          profit: finalProfit,
          finalPrice,
          profitType,
          parentQuoteRequestId: result.response.quoteRequestId || 0,
        };
      })
      .filter(Boolean);

    // Update each quote request suggestion
    const updatePromises = quotesWithResults.map(async (quote) => {
      if (!quote) return;

      const result = await updateQuoteRequestSuggestion(
        quote.parentQuoteRequestId,
        SuggestionStatus.Accepted,
        {
          finalQuotePrice: _.round(quote.finalPrice),
          finalMargin: _.round(quote.profit),
          marginType: quote.profitType,
          finalCarrierCost: _.round(quote.carrierCost),
          carrierCostType: CarrierCostType.Flat,
          customerExternalTMSId: getValues('customerName'),
        }
      );

      if (result.isErr()) {
        captureException(result.error, {
          functionName: 'handleTerminatingAction',
        });
      }
    });

    await Promise.all(updatePromises);

    // Remove processed suggestions from the list
    setCurrentState((prevState) => ({
      ...prevState,
      curSuggestionList: prevState.curSuggestionList.filter(
        (s) =>
          !suggestions.some((batchSuggestion) => batchSuggestion.id === s.id)
      ),
    }));
  };

  const handleCopyToClipboard = async () => {
    try {
      // Decode any HTML entities before copying to ensure clean text
      const cleanedResponse = decodeHtmlEntities(draftResponse);
      const success = await copyToClipboard(cleanedResponse);
      if (success) {
        setHasCopiedDraftResponse(true);
        handleTerminatingAction();
        setTimeout(() => setHasCopiedDraftResponse(false), 2000);
      }
    } catch (error) {
      captureException(error, { functionName: 'handleCopyToClipboard' });

      toast({
        description: 'Failed to copy to clipboard.',
        variant: 'destructive',
      });
    }
  };

  // Outlook draft functionality
  let handleDraftResponse: () => void;
  if (drumkitPlatform === DrumkitPlatform.Outlook) {
    const mailClient = createMailClientInstance(drumkitPlatform);
    handleDraftResponse = async () => {
      if (!draftResponse.trim()) {
        return;
      }

      setLoadingDraftReply(true);

      try {
        // Convert newlines to HTML <br> tags since the mail client works with HTML.
        const formattedDraftBody = draftResponse.trim().replace(/\n/g, '<br>');

        await mailClient.draftReply({
          threadItemId,
          draftBody: formattedDraftBody,
        });

        handleTerminatingAction();

        // When in Read View, there's a lag between when Outlook created the draft in the backend
        // and showing it in the client so wait for a moment before showing toaster.
        setTimeout(
          () => {
            toast({
              description: 'Successfully created draft reply.',
              variant: 'success',
            });

            setLoadingDraftReply(false);
          },
          isOutlookReply ? 1 : 3500
        );
      } catch (error: unknown) {
        captureException(error, { functionName: 'handleDraftResponse' });

        toast({
          description: 'Something went wrong creating draft reply',
          variant: 'destructive',
        });

        setLoadingDraftReply(false);
      }
    };
  }

  const {
    customers,
    isLoading: customersLoading,
    refreshCustomers: handleRefreshCustomers,
    resetCustomerSearch: handleResetCustomerSearch,
    customerSearch: handleCustomerSearch,
  } = useCustomers(tmsIntegrations, {
    toast,
    suggestedCustomerId: (suggestions[0]?.suggested as QuoteChanges)
      ?.customerExternalTMSID,
  });

  const isTaiOrGlobalTranzTMS = tmsIntegrations?.some(
    (tms) => tms.name === TMS.Tai || tms.name === TMS.GlobalTranzTMS
  );

  // Generate batch draft response with proper price calculation
  const generateBatchDraftResponse = useCallback(() => {
    const quotesWithResults = results
      .map((result, index) => {
        if (!result?.response) return null;

        const formData = getValues(`quotes.${index}`);

        // Get the actual calculated price from the lane card if available
        const laneCalculatedData = laneCalculatedPricesRef.current.get(index);

        let finalPrice: number;
        let maxDistance: number;
        let fuelEstimate: number;

        if (laneCalculatedData) {
          // Use the actual calculated price from the lane card
          finalPrice = laneCalculatedData.finalPrice;
          maxDistance = laneCalculatedData.maxDistance;
          fuelEstimate = laneCalculatedData.fuelEstimate;
        } else {
          // Fallback to basic calculation if lane data not available
          const carrierCost = result.cards?.length
            ? result.cards.reduce((prev, curr) =>
                prev.cost < curr.cost ? prev : curr
              ).cost
            : 0;

          maxDistance =
            result.response.quotes?.reduce(
              (max, current) =>
                current.distance > max.distance ? current : max,
              { distance: 0 }
            ).distance || 0;

          const profit = formData.profit || 0;
          const profitType = formData.profitType || ProfitType.Percentage;

          const { flatCarrierCost, finalProfit } = calculatePricing(
            carrierCost,
            CarrierCostType.Flat,
            profit,
            profitType,
            maxDistance
          );

          finalPrice = flatCarrierCost + finalProfit;
          fuelEstimate = 0;
        }

        // Get the price format for this lane, defaulting to Flat if not found
        const lanesPriceFormat =
          laneCurrentPriceFormatsRef.current.get(index) || CarrierCostType.Flat;

        return {
          quoteNumber: index + 1,
          pickupLocation: `${useHelperFunctions.toTitleCase(result.response.stops[0].city)}, ${result.response.stops[0].state}`,
          deliveryLocation: `${useHelperFunctions.toTitleCase(result.response.stops[1].city)}, ${result.response.stops[1].state}`,
          transportType: titleCase(formData.transportType || ''),
          price: formatPriceForDraft(
            finalPrice,
            maxDistance,
            fuelEstimate,
            lanesPriceFormat
          ),
          rawPrice: finalPrice,
        };
      })
      .filter(Boolean);

    if (quotesWithResults.length === 0) return '';

    // Get the actual customer display name instead of externalTMSID
    const customerId = getValues('customerName');
    const watchedCustomer = watchedCustomerName;

    let customerDisplayName = '';
    const customerIdToLookup = customerId || watchedCustomer;

    if (customerIdToLookup && customers?.length) {
      const foundCustomer = customers.find(
        (customer) => customer.externalTMSID === customerIdToLookup
      );
      if (foundCustomer?.name) {
        customerDisplayName = foundCustomer.name;
      }
    }

    const templateData = {
      customerName: customerDisplayName || '',
      hasCustomer: !!customerDisplayName,
      quotes: quotesWithResults,
      hasTotal: quotesWithResults.length > 1,
    };

    const renderedTemplate = Mustache.render(
      BATCH_QUOTE_TEMPLATE,
      templateData
    );
    return decodeHtmlEntities(renderedTemplate);
  }, [results, customers, watchedCustomerName]);

  // Reset user edited flag when form values change (but not results)
  // This allows the draft to update when user changes margins, transport types, etc.
  useEffect(() => {
    if (results.some((result) => result !== null)) {
      setUserEditedDraft(false);
    }
  }, [watchedQuotes, watchedCustomerName, customers, results]);

  // Force draft regeneration when customers data becomes available
  useEffect(() => {
    if (
      results.some((result) => result !== null) &&
      customers?.length &&
      !userEditedDraft
    ) {
      const newDraft = generateBatchDraftResponse();
      setDraftResponse(newDraft);
    }
  }, [customers, results, generateBatchDraftResponse, userEditedDraft]);

  // Update draft response when results, price format, or form values change
  useEffect(() => {
    if (results.some((result) => result !== null)) {
      const newDraft = generateBatchDraftResponse();
      // Only update the draft if the user hasn't manually edited the textarea
      if (!userEditedDraft) {
        setDraftResponse(newDraft);
      }
    }
  }, [results, generateBatchDraftResponse, userEditedDraft]);

  // Ref to store setter functions for carrier selection per lane
  const carrierSetterMapRef = useRef(
    new Map<number, (carrier: SelectedCarrierType) => void>()
  );

  // Ref to store the actual calculated prices from each lane
  const laneCalculatedPricesRef = useRef(
    new Map<
      number,
      { finalPrice: number; maxDistance: number; fuelEstimate: number }
    >()
  );

  // Ref to store price format setter functions per lane
  const priceFormatSetterMapRef = useRef(
    new Map<number, (priceFormat: CarrierCostType | 'Both') => void>()
  );

  // Ref to store current price format for each lane
  const laneCurrentPriceFormatsRef = useRef(
    new Map<number, CarrierCostType | 'Both'>()
  );

  const registerCarrierSetter = (
    laneIndex: number,
    setter: (carrier: SelectedCarrierType) => void
  ) => {
    if (setter) {
      carrierSetterMapRef.current.set(laneIndex, setter);
    } else {
      carrierSetterMapRef.current.delete(laneIndex);
    }
  };

  const registerPriceCallback = (
    laneIndex: number,
    priceData: {
      finalPrice: number;
      maxDistance: number;
      fuelEstimate: number;
    } | null
  ) => {
    if (priceData) {
      laneCalculatedPricesRef.current.set(laneIndex, priceData);
    } else {
      laneCalculatedPricesRef.current.delete(laneIndex);
    }

    // Trigger draft regeneration when prices change
    if (results.some((result) => result !== null) && !userEditedDraft) {
      const newDraft = generateBatchDraftResponse();
      setDraftResponse(newDraft);
    }
  };

  const registerPriceFormatSetter = (
    laneIndex: number,
    setter: (priceFormat: CarrierCostType | 'Both') => void
  ) => {
    if (setter) {
      priceFormatSetterMapRef.current.set(laneIndex, setter);
      // Initialize with default format if not already set
      if (!laneCurrentPriceFormatsRef.current.has(laneIndex)) {
        laneCurrentPriceFormatsRef.current.set(laneIndex, CarrierCostType.Flat);
      }
    } else {
      priceFormatSetterMapRef.current.delete(laneIndex);
      laneCurrentPriceFormatsRef.current.delete(laneIndex);
    }
  };

  const handlePriceFormatChange = (
    laneIndex: number,
    priceFormat: CarrierCostType | 'Both'
  ) => {
    // Update the current price format tracking
    laneCurrentPriceFormatsRef.current.set(laneIndex, priceFormat);

    // Trigger draft regeneration when price format changes
    if (results.some((result) => result !== null) && !userEditedDraft) {
      const newDraft = generateBatchDraftResponse();
      setDraftResponse(newDraft);
    }
  };

  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={onSubmit} className='space-y-3' ref={formContainerRef}>
        <TooltipProvider>
          <div className='mb-3'>
            <p className='text-xs text-grayscale-content-2'>
              {suggestions.length > 1
                ? 'Multiple lanes detected. Review and get quotes for all lanes below.'
                : 'Quote multiple lanes at once.'}
            </p>
          </div>

          {/* Customer input */}
          {tmsIntegrations &&
            tmsIntegrations.length > 0 &&
            !isTaiOrGlobalTranzTMS && (
              <div className='mb-3'>
                <RHFDebounceSelect
                  required={false}
                  name='customerName'
                  label='Customer'
                  control={control}
                  errors={errors}
                  data={customers}
                  isLoading={customersLoading}
                  showSearchParamDropdown={false}
                  refreshHandler={handleRefreshCustomers}
                  resetOptionsHandler={handleResetCustomerSearch}
                  fetchOptions={handleCustomerSearch}
                  mapOptions={mapCustomerToAntdOptions}
                  disabled={results.length > 0}
                />
              </div>
            )}

          {fields.length > 0 && (
            <div className='mb-4 flex flex-col gap-1 border-t border-grayscale-border-divider pt-3'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-3'>
                  <label className='flex items-center gap-2 text-xs text-gray-600 cursor-pointer'>
                    <Checkbox
                      checked={isAllSelected}
                      onCheckedChange={handleSelectAll}
                    />
                    <span className='font-medium'>
                      {isAllSelected ? 'Deselect All' : 'Select All'}
                    </span>
                  </label>
                </div>
                {hasSelection ? (
                  <>
                    {showBatchEdit ? (
                      <span className='text-xs text-gray-500'>
                        Editing {selectedLanes.size}{' '}
                        {results.length > 0 ? 'quote' : 'lane'}
                        {selectedLanes.size > 1 ? 's' : ''}
                      </span>
                    ) : (
                      <span className='text-xs text-gray-500'>
                        {selectedLanes.size} of {fields.length} selected
                      </span>
                    )}
                  </>
                ) : (
                  <span className='text-xs text-gray-500'>
                    Select {results.length > 0 ? 'quotes' : 'lanes'} to edit
                  </span>
                )}
              </div>

              {expandableLanesCount > 0 && (
                <div className='flex items-center justify-end mt-1'>
                  <button
                    type='button'
                    onClick={handleToggleExpandAll}
                    className='flex items-center gap-0.5 text-xs text-blue-600 font-medium hover:underline focus:outline-none'
                  >
                    {allLanesExpanded ? (
                      <>
                        <ChevronUpIcon className='w-4 h-4' />
                        <p>Collapse All Quotes</p>
                      </>
                    ) : (
                      <>
                        <ChevronDownIcon className='w-4 h-4' />
                        <p>Expand All Quotes</p>
                      </>
                    )}
                  </button>
                </div>
              )}

              <BatchQuoteEdit
                selectedLanes={selectedLanes}
                showBatchEdit={showBatchEdit}
                hasResults={results.length > 0}
                transportTypeOptions={transportTypeOptions}
                lastBatchEditMargin={lastBatchEditMargin}
                onInitiateBatchEdit={handleInitiateBatchEdit}
                onCancelBatchEdit={handleCancelBatchEdit}
                onApplyBatchEdit={handleApplyBatchEdit}
              />
            </div>
          )}

          {fields.map((field, index) => (
            <div key={field.id} data-lane-card>
              <BatchQuoteInputCard
                index={index}
                control={control}
                transportTypeOptions={transportTypeOptions}
                onRemove={() => handleRemoveQuote(index)}
                result={results[index] ?? undefined}
                laneHistoryResult={laneHistoryResults[index] ?? undefined}
                laneHistoryFromServiceResult={
                  laneHistoryFromServiceResults[index] ?? undefined
                }
                setValue={setValue}
                isSelected={selectedLanes.has(index)}
                onSelect={(isSelected) => handleSelectLane(index, isSelected)}
                quickQuoteConfig={quickQuoteConfig}
                isExpanded={expandedLanes.has(index)}
                onToggleExpand={() => handleToggleExpandLane(index)}
                registerCarrierSetter={registerCarrierSetter}
                registerPriceCallback={registerPriceCallback}
                registerPriceFormatSetter={registerPriceFormatSetter}
                onPriceFormatChange={handlePriceFormatChange}
                isTMSLaneHistoryEnabled={isTMSLaneHistoryEnabled}
                isQuoteLaneHistoryEnabled={isQuoteLaneHistoryEnabled}
                isGetLaneRateFromServiceEnabled={
                  isGetLaneRateFromServiceEnabled
                }
              />
            </div>
          ))}

          {fields.length === 0 && (
            <div className='flex flex-col items-center justify-center py-8 px-4 border-2 border-gray-300 rounded-lg bg-gray-50'>
              <div className='text-center space-y-3'>
                <div className='mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100'>
                  <Truck className='h-5 w-5 text-blue-600' />
                </div>
                <div>
                  <h3 className='text-sm font-medium text-gray-900'>
                    No lanes to quote
                  </h3>
                  <p className='text-xs text-gray-500 mt-1'>
                    Add lanes to get quotes for multiple routes at once
                  </p>
                </div>
                <div className='flex gap-2 justify-center'>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={handleAddLane}
                    className='text-xs px-3 py-1.5 h-8'
                    buttonNamePosthog={null}
                  >
                    <div className='flex items-center gap-1'>
                      <PlusIcon className='w-3 h-3' />
                      Add Lane
                    </div>
                  </Button>
                </div>
                {suggestions.length > 1 && (
                  <>
                    <Button
                      className='text-sm border-blue-300 hover:border-blue-500 bg-gradient-to-br from-white to-blue-100/50 text-blue-600'
                      onClick={() => {
                        // Repopulate form with quote request suggestions
                        reset(memoizedDefaultValues);
                      }}
                      type='button'
                      buttonNamePosthog={null}
                      variant='outline'
                      size='sm'
                    >
                      <SparklesIcon className='w-3 h-3 mr-1' />
                      <p className='text-xs'>Add lanes using AI suggestions</p>
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}

          {fields.length > 0 && results.length === 0 ? (
            <>
              <div className='pb-3'>
                <div className='flex gap-2 justify-center'>
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={handleAddLane}
                    className='text-xs px-3 py-1.5 tracking-wide hover:border-grayscale-border-input hover:bg-gray-100 w-1/2'
                    buttonNamePosthog={null}
                  >
                    <div className='flex items-center gap-1'>
                      <PlusIcon className='w-3 h-3' />
                      Add Lane
                    </div>
                  </Button>
                </div>
              </div>

              <Button
                type='submit'
                className='w-full'
                disabled={isSubmitButtonDisabled}
                buttonNamePosthog={ButtonNamePosthog.GetBatchQuote}
              >
                {isSubmitting ? <ButtonLoader /> : ButtonText.GetBatchQuote}
              </Button>
            </>
          ) : (
            results.length > 0 && (
              <>
                {/* Show draft response if Drumkit running on email platform (Gmail, Outlook, Front) */}
                {isEmailPlatform(drumkitPlatform) && (
                  <div className='mt-4'>
                    <div className='mb-3 flex items-center justify-between'>
                      <h3 className='text-md font-semibold text-grayscale-content-label'>
                        Draft Response
                      </h3>
                      <div className='relative'>
                        <Button
                          buttonNamePosthog={
                            ButtonNamePosthog.CopyQuoteToClipboard
                          }
                          className={cn(
                            'h-6 w-6 p-0 border-none',
                            hasCopiedDraftResponse
                              ? 'cursor-default'
                              : 'cursor-pointer'
                          )}
                          variant='ghost'
                          type='button'
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            !hasCopiedDraftResponse && handleCopyToClipboard();
                          }}
                        >
                          {hasCopiedDraftResponse ? (
                            <Tooltip open={true}>
                              <TooltipTrigger asChild>
                                <CheckIcon className='h-4 w-4' />
                              </TooltipTrigger>
                              <TooltipContent>Copied!</TooltipContent>
                            </Tooltip>
                          ) : (
                            <CopyIcon className='h-4 w-4' />
                          )}
                        </Button>
                      </div>
                    </div>

                    <Textarea
                      name='batchEmailBody'
                      className='p-3 h-48 rounded-[4px] text-sm'
                      value={draftResponse}
                      placeholder='Quote results will appear here...'
                      onChange={(e) => {
                        setDraftResponse(e.target.value);
                        !userEditedDraft && setUserEditedDraft(true);
                      }}
                    />

                    {/** Reply drafts are only supported on Outlook for now */}
                    {drumkitPlatform === DrumkitPlatform.Outlook && (
                      <Button
                        className='w-full h-8 text-sm mt-4'
                        type='button'
                        buttonNamePosthog={
                          isOutlookReply
                            ? ButtonNamePosthog.AddReplyToCurrentDraft
                            : ButtonNamePosthog.CreateDraftReply
                        }
                        disabled={loadingDraftReply}
                        onClick={() => handleDraftResponse()}
                      >
                        {isOutlookReply ? (
                          ButtonText.AddReplyToCurrentDraft
                        ) : loadingDraftReply ? (
                          <ButtonLoader />
                        ) : (
                          ButtonText.CreateDraftReply
                        )}
                      </Button>
                    )}
                  </div>
                )}
              </>
            )
          )}
        </TooltipProvider>
      </form>
    </FormProvider>
  );
};

export default BatchQuoteForm;
