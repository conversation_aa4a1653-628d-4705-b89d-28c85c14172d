import React, { useCallback, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { Edit3 } from 'lucide-react';

import { Button } from 'components/Button';
import { Checkbox } from 'components/Checkbox';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { SelectedCarrierType } from 'lib/api/getQuickQuote';

import { BatchEditValues, CarrierCostType, ProfitType } from './types';

type BatchQuoteEditProps = {
  selectedLanes: Set<number>;
  showBatchEdit: boolean;
  hasResults: boolean;
  transportTypeOptions: Array<{ value: string; label: string }>;
  lastBatchEditMargin: {
    margin: string;
    marginType: ProfitType;
  };
  onInitiateBatchEdit: () => void;
  onCancelBatchEdit: () => void;
  onApplyBatchEdit: (values: BatchEditValues) => void;
};

const BatchEditTextInput = React.memo(
  ({
    label,
    placeholder,
    ...fieldProps
  }: {
    label: string;
    placeholder: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur: () => void;
    name: string;
  }) => (
    <div>
      <Label name={fieldProps.name} className='text-xs'>
        {label}
      </Label>
      <input
        {...fieldProps}
        type='text'
        placeholder={placeholder}
        className='w-full mt-1 px-3 py-1.5 text-xs tracking-tight bg-white border border-gray-300 hover:border-primary focus:border-primary transition-colors rounded-[4px] focus:outline-none focus:ring-2 focus:ring-primary/20'
      />
    </div>
  )
);

const BatchQuoteEdit: React.FC<BatchQuoteEditProps> = React.memo(
  ({
    selectedLanes,
    showBatchEdit,
    hasResults,
    transportTypeOptions,
    lastBatchEditMargin,
    onInitiateBatchEdit,
    onCancelBatchEdit,
    onApplyBatchEdit,
  }) => {
    const hasSelection = selectedLanes.size > 0;
    const [leaveMarginUnchanged, setLeaveMarginUnchanged] = useState(false);

    const batchEditForm = useForm<BatchEditValues>({
      defaultValues: {
        transportType: '',
        pickupLocation: '',
        dropoffLocation: '',
        margin: lastBatchEditMargin.margin,
        marginType: lastBatchEditMargin.marginType,
        buyRateSource: '',
        priceFormat: '',
      },
    });

    const handleApplyBatchEditInternal = useCallback(() => {
      const batchValues = batchEditForm.getValues();
      // Clear margin if user wants to leave it unchanged
      if (leaveMarginUnchanged) {
        batchValues.margin = '';
      }
      onApplyBatchEdit(batchValues);
      batchEditForm.reset();
      setLeaveMarginUnchanged(false);
    }, [batchEditForm, onApplyBatchEdit, leaveMarginUnchanged]);

    const handleCancelBatchEditInternal = useCallback(() => {
      onCancelBatchEdit();
      batchEditForm.reset();
      setLeaveMarginUnchanged(false);
    }, [batchEditForm, onCancelBatchEdit]);

    // Reset form when batch edit is initiated
    useEffect(() => {
      if (showBatchEdit) {
        batchEditForm.reset({
          transportType: '',
          pickupLocation: '',
          dropoffLocation: '',
          margin: lastBatchEditMargin.margin,
          marginType: lastBatchEditMargin.marginType,
          buyRateSource: '',
          priceFormat: '',
        });
        setLeaveMarginUnchanged(false);
      }
    }, [showBatchEdit, lastBatchEditMargin, batchEditForm]);

    const handleLeaveMarginUnchangedToggle = (checked: boolean) => {
      setLeaveMarginUnchanged(checked);
      if (checked) {
        // Clear the margin value when leaving unchanged
        batchEditForm.setValue('margin', '');
      } else {
        // Restore the last margin value when unchecked
        batchEditForm.setValue('margin', lastBatchEditMargin.margin);
      }
    };

    return (
      <>
        {!showBatchEdit && (
          <div className='flex gap-2 mt-2'>
            <Button
              type='button'
              variant='outline'
              size='sm'
              onClick={onInitiateBatchEdit}
              className='text-xs w-full h-8'
              buttonNamePosthog={null}
              disabled={!hasSelection}
            >
              <Edit3 className='w-3 h-3 mr-1' />
              <div className='flex items-center gap-1'>
                Edit Selected
                {selectedLanes.size > 0 && <span>({selectedLanes.size})</span>}
              </div>
            </Button>
          </div>
        )}

        {showBatchEdit && hasSelection && (
          <div className='mt-2 p-3 bg-gray-50 border-2 border-gray-300 rounded-[4px]'>
            <div className='flex flex-col mb-3'>
              <h3 className='text-sm font-medium text-grayscale-content-label'>
                Edit {selectedLanes.size} {hasResults ? 'Quote' : 'Lane'}
                {selectedLanes.size > 1 ? 's' : ''}
              </h3>
              <p className='text-xs text-gray-500'>
                Make edits to all selected {hasResults ? 'quote' : 'lane'}
                {selectedLanes.size > 1 ? 's' : ''}
              </p>
            </div>

            <div className='space-y-3'>
              {!hasResults && (
                <div className='flex flex-col gap-2'>
                  <div>
                    <Label name='batchTransportType' className='text-xs'>
                      Transport Type
                    </Label>
                    <Controller
                      name='transportType'
                      control={batchEditForm.control}
                      render={({ field }) => (
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <SelectTrigger className='mt-1 bg-white border-gray-300 hover:border-primary transition-colors text-xs'>
                            <SelectValue
                              placeholder='Leave unchanged'
                              className='text-xs'
                            />
                          </SelectTrigger>
                          <SelectContent>
                            {transportTypeOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                  </div>

                  <Controller
                    name='pickupLocation'
                    control={batchEditForm.control}
                    render={({ field }) => (
                      <BatchEditTextInput
                        {...field}
                        label='Pickup Location'
                        placeholder='ZIP or City, State'
                      />
                    )}
                  />

                  <Controller
                    name='dropoffLocation'
                    control={batchEditForm.control}
                    render={({ field }) => (
                      <BatchEditTextInput
                        {...field}
                        label='Dropoff Location'
                        placeholder='ZIP or City, State'
                      />
                    )}
                  />
                </div>
              )}

              {hasResults && (
                <div>
                  <div className='flex items-center justify-between mb-2'>
                    <Label name='batchMargin'>Margin</Label>
                    <label className='flex items-center gap-2 text-xs text-gray-600 cursor-pointer'>
                      <Checkbox
                        checked={leaveMarginUnchanged}
                        onCheckedChange={handleLeaveMarginUnchangedToggle}
                      />
                      <span>Leave unchanged</span>
                    </label>
                  </div>
                  <div className='flex items-center gap-2 mt-1'>
                    <Controller
                      name='margin'
                      control={batchEditForm.control}
                      render={({ field }) => (
                        <input
                          {...field}
                          onWheel={(e) => (e.target as HTMLElement).blur()}
                          type='number'
                          placeholder={
                            leaveMarginUnchanged ? 'Will not change' : 'e.g. 15'
                          }
                          disabled={leaveMarginUnchanged}
                          className={`px-3 py-2 text-xs tracking-tight border transition-colors rounded-[4px] focus:outline-none focus:ring-2 focus:ring-primary/20 ${
                            leaveMarginUnchanged
                              ? 'bg-gray-100 border-gray-200 text-gray-500 cursor-not-allowed'
                              : 'bg-white border-gray-300 hover:border-primary focus:border-primary'
                          }`}
                        />
                      )}
                    />
                    <Controller
                      name='marginType'
                      control={batchEditForm.control}
                      render={({ field }) => (
                        <div
                          className={`flex rounded-[4px] overflow-hidden border text-xs ${
                            leaveMarginUnchanged
                              ? 'border-gray-200'
                              : 'border-gray-300'
                          }`}
                        >
                          <button
                            type='button'
                            onClick={() => field.onChange(ProfitType.Amount)}
                            disabled={leaveMarginUnchanged}
                            className={`px-2 py-1 transition-colors ${
                              leaveMarginUnchanged
                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : field.value === ProfitType.Amount
                                  ? 'bg-primary/10 text-primary'
                                  : 'bg-white text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            $
                          </button>
                          <button
                            type='button'
                            onClick={() =>
                              field.onChange(ProfitType.Percentage)
                            }
                            disabled={leaveMarginUnchanged}
                            className={`px-2 py-1 transition-colors ${
                              leaveMarginUnchanged
                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : field.value === ProfitType.Percentage
                                  ? 'bg-primary/10 text-primary'
                                  : 'bg-white text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            %
                          </button>
                        </div>
                      )}
                    />
                  </div>
                </div>
              )}

              {/* Buy Rate Source selector when results available */}
              {hasResults && (
                <div>
                  <Label name='buyRateSource'>Buy Rate Source</Label>
                  <Controller
                    name='buyRateSource'
                    control={batchEditForm.control}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger className='w-full mt-1 bg-white border-gray-300 hover:border-primary focus:border-primary transition-colors text-xs'>
                          <SelectValue
                            placeholder='Leave unchanged'
                            className='text-xs'
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={SelectedCarrierType.DAT}>
                            DAT Rate View
                          </SelectItem>
                          <SelectItem value={SelectedCarrierType.BUYPOWER}>
                            Greenscreens Buy Power
                          </SelectItem>
                          <SelectItem value={SelectedCarrierType.NETWORK}>
                            Greenscreens Network
                          </SelectItem>
                          {/* TODO: add TMS Lane history option when TMS lane history is added for batch quote */}
                          {/* <SelectItem value={SelectedCarrierType.LANE_HISTORY}>
                          TMS Lane History
                        </SelectItem> */}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              )}

              {/* Price Format selector when results available */}
              {hasResults && (
                <div>
                  <Label name='priceFormat'>Price Format</Label>
                  <Controller
                    name='priceFormat'
                    control={batchEditForm.control}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger className='w-full mt-1 bg-white border-gray-300 hover:border-primary focus:border-primary transition-colors text-xs'>
                          <SelectValue
                            placeholder='Leave unchanged'
                            className='text-xs'
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={CarrierCostType.Flat}>
                            Flat Rate
                          </SelectItem>
                          <SelectItem value={CarrierCostType.PerMile}>
                            Per Mile
                          </SelectItem>
                          <SelectItem value='Both'>Flat & Per Mile</SelectItem>
                          <SelectItem value={CarrierCostType.Linehaul}>
                            Linehaul
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              )}

              <div className='flex justify-end gap-2 pt-1'>
                <Button
                  type='button'
                  variant='ghost'
                  size='sm'
                  onClick={handleCancelBatchEditInternal}
                  className='text-xs h-8 hover:border-none hover:text-grayscale-content-2 transition-all duration-200'
                  buttonNamePosthog={null}
                >
                  Cancel
                </Button>
                <Button
                  type='button'
                  size='sm'
                  onClick={handleApplyBatchEditInternal}
                  className='text-xs h-8'
                  buttonNamePosthog={null}
                >
                  Apply Changes
                </Button>
              </div>
            </div>
          </div>
        )}
      </>
    );
  }
);

export default BatchQuoteEdit;
