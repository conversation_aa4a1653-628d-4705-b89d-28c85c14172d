import { ChevronDown, ChevronUp } from 'lucide-react';

// Utility function for generating show more text with proper pluralization
const getShowMoreText = (
  totalCount: number,
  displayedCount: number
): string => {
  const remainingCount = totalCount - displayedCount;
  const carrierText = remainingCount === 1 ? 'carrier' : 'carriers';
  return `Show ${remainingCount} more ${carrierText}`;
};

interface ShowMoreLessButtonProps {
  isShowingAll: boolean;
  totalCount: number;
  displayedCount: number;
  onToggle: (e: React.MouseEvent<HTMLButtonElement>) => void;
}

export default function ShowMoreLessButton({
  isShowingAll,
  totalCount,
  displayedCount,
  onToggle,
}: ShowMoreLessButtonProps) {
  const buttonText = isShowingAll
    ? 'Show less'
    : getShowMoreText(totalCount, displayedCount);

  const ChevronIcon = isShowingAll ? ChevronUp : ChevronDown;

  return (
    <button
      type='button'
      onClick={onToggle}
      className='text-primary text-xs flex items-center gap-1 mt-1 cursor-pointer hover:underline focus:outline-none pb-0.5'
    >
      {buttonText}
      <ChevronIcon size={14} />
    </button>
  );
}
