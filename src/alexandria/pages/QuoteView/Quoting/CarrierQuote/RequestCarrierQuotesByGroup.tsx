import { useCallback, useEffect, useMemo, useState } from 'react';
import { Form<PERSON>rovider, SubmitHandler, useForm } from 'react-hook-form';
import { Controller } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import { ArrowRightIcon } from 'lucide-react';
import { useSWRConfig } from 'swr';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import isProd from '@utils/isProd';

import { AddDateDetailsButton } from 'components/AddDateDetailsButton';
import { Button } from 'components/Button';
import { DatePicker } from 'components/DatePicker';
import { Label } from 'components/Label';
import { Input } from 'components/input/Input';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { CarrierQuoteConfig } from 'contexts/serviceContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import {
  NewAttachment,
  emailCarriersForQuotes,
} from 'lib/api/emailCarriersForQuotes';
import { getCustomers } from 'lib/api/getCustomers';
import { CarrierGroup } from 'types/CarrierGroup';
import { Email } from 'types/Email';
import { TMSCustomer, TMSLocationWithDistance } from 'types/Load';
import {
  QuoteRequest,
  Address as QuoteRequestAddress,
} from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import { TMS } from 'types/enums/Integrations';
import captureException from 'utils/captureException';
import getEmailAddress from 'utils/getEmailAddress';
import {
  GenericCompanySearchableFields,
  customerSearchHandler,
} from 'utils/loadInfoAndBuilding';

import { ProcessedAttachment } from './AttachmentUpload';
import FindCarrierByGroup from './FindCarrierByGroup';
import LoadInformationForm from './LoadInformationForm';
import MessageCarrierForm from './MessageCarrierForm';
import { constructEmailBody, constructEmailSubject } from './emailTemplate';
import {
  handleEmailCarrierForQuoteResponse,
  parseLocationString,
} from './helperFunctions';
import { CarrierFormConfig, CarrierQuoteInputs } from './types';

interface RequestCarrierQuoteByGroupProps {
  email: Maybe<Email>;
  request: Maybe<QuoteRequest>;
  // QuoteRequest may be null if there's a transient network error getting the parsed values. In those cases, still show the form
  carrierQuoteConfig: CarrierQuoteConfig;
}

export default function RequestCarrierQuotesByGroup({
  email,
  request,
  carrierQuoteConfig,
}: RequestCarrierQuoteByGroupProps) {
  const { toast } = useToast();
  const { mutate } = useSWRConfig();
  const [customers, setCustomers] = useState<Maybe<TMSCustomer[]>>(null);
  const [initialCustomers, setInitialCustomers] =
    useState<Maybe<TMSCustomer[]>>(null);
  const [selectedCarrierGroup, setSelectedCarrierGroup] =
    useState<Maybe<CarrierGroup>>(null);

  const [loading, setLoading] = useState(false);
  const [carrierLocations, setCarrierLocations] = useState<
    TMSLocationWithDistance[]
  >([]);
  const [showMessageCarrier, setShowMessageCarrier] = useState(false);
  const [selectedCarriers, setSelectedCarriers] = useState<
    Record<string, boolean>
  >({});
  const [newAttachments, setNewAttachments] = useState<ProcessedAttachment[]>(
    []
  );
  const [areAllCarriersSelected, setAreAllCarriersSelected] =
    useState<boolean>(true);
  const { serviceID, tmsIntegrations } = useServiceFeatures();
  const [initialFromEmail, setInitialFromEmail] = useState<Maybe<string>>(
    carrierQuoteConfig.from ?? null
  );
  const [hasAddedQuoteDates, setHasAddedQuoteDates] = useState<boolean>(false);

  // Tai and GlobalTranz TMSes don't support customer fetching
  const isTaiOrGlobalTranzTMS = tmsIntegrations?.some(
    (tms) => tms.name === TMS.Tai || tms.name === TMS.GlobalTranzTMS
  );

  useEffect(() => {
    if (carrierLocations.length === 0) {
      setAreAllCarriersSelected(false);
    } else {
      const allSelected = carrierLocations.every(
        (carrier) =>
          carrier.externalTMSID && selectedCarriers[carrier.externalTMSID]
      );
      setAreAllCarriersSelected(allSelected);
    }
  }, [carrierLocations, selectedCarriers]);

  if (tmsIntegrations.length > 0) {
    if (tmsIntegrations.length > 1 && isProd()) {
      captureException(
        new Error(
          'Service with multiple TMS integrations trying to find carriers'
        ),
        { serviceID: serviceID, tmsIntegrations: tmsIntegrations }
      );
    }
  }

  const config: CarrierFormConfig = useMemo(() => {
    return {
      from: initialFromEmail || carrierQuoteConfig.from || '',
      cc: carrierQuoteConfig.cc ?? [],
      bcc: carrierQuoteConfig.bcc ?? [],
      bccCarriers: carrierQuoteConfig.bccCarriers,
      subject: carrierQuoteConfig.subject ?? '',
      emailBody: carrierQuoteConfig.emailBody ?? '',
      showCustomerSearch: carrierQuoteConfig.showCustomerSearch,
      showItemDescription: carrierQuoteConfig.showItemDescription,
      showDeliverySection: carrierQuoteConfig.showDeliverySection,
      requireDeliveryLocation: carrierQuoteConfig.requireDeliveryLocation,
      showPickupAddressLine1: carrierQuoteConfig.showPickupAddressLine1,
      showDeliveryAddressLine1: carrierQuoteConfig.showDeliveryAddressLine1,
      isFindCarrierByGroupEnabled: true,
      isFindCarrierByLocationEnabled:
        carrierQuoteConfig.isFindCarrierByLocationEnabled,
    };
  }, [initialFromEmail, carrierQuoteConfig]);

  const carrierProcessingFormMethods = useForm<CarrierQuoteInputs>({
    mode: 'onChange',
    resolver: async (values) => {
      const errors: Record<string, any> = {};

      // Validate pickup location
      if (!values.pickupLocation?.location) {
        errors.pickupLocation = {
          location: {
            type: 'required',
            message: 'Pickup location is required',
          },
        };
      }

      // Validate mile radius only if location search is enabled and group search is not solely active
      if (
        config.isFindCarrierByLocationEnabled &&
        !config.isFindCarrierByGroupEnabled &&
        !values.mileRadius
      ) {
        errors.mileRadius = {
          type: 'required',
          message: 'Search radius is required',
        };
      } else if (
        config.isFindCarrierByLocationEnabled &&
        !config.isFindCarrierByGroupEnabled &&
        values.mileRadius &&
        values.mileRadius < 1
      ) {
        errors.mileRadius = {
          type: 'min',
          message: 'Search radius must be at least 1 mile',
        };
      }

      if (
        config.requireDeliveryLocation &&
        !values.deliveryLocation?.location
      ) {
        errors.deliveryLocation = {
          location: {
            type: 'required',
            message: 'Delivery location is required',
          },
        };
      }

      return {
        values,
        errors: Object.keys(errors).length > 0 ? errors : {},
      };
    },
  });

  const messageCarrierFormMethods = useForm<CarrierQuoteInputs>({
    mode: 'onChange',
  });

  useEffect(() => {
    if (tmsIntegrations?.length > 0 && !isTaiOrGlobalTranzTMS) {
      const fetchCustomers = async () => {
        const res = await getCustomers(tmsIntegrations[0].id);
        if (res.isOk()) {
          setInitialCustomers(res.value.customerList);
          setCustomers(res.value.customerList);
        }
      };
      fetchCustomers();
    }

    if (!initialFromEmail) {
      const fetchUserEmail = async () => {
        const userEmail = await getEmailAddress();
        if (userEmail) {
          setInitialFromEmail(userEmail);
          // Update form defaults after fetching email
          carrierProcessingFormMethods.reset({
            ...carrierProcessingFormMethods.getValues(),
            from: userEmail,
          });
          messageCarrierFormMethods.reset({
            ...messageCarrierFormMethods.getValues(),
            from: userEmail,
          });
        }
      };
      fetchUserEmail();
    }
  }, [
    tmsIntegrations,
    initialFromEmail,
    carrierProcessingFormMethods,
    messageCarrierFormMethods,
  ]);

  const defaultValues = useMemo<CarrierQuoteInputs>(() => {
    const baseValues: CarrierQuoteInputs = {
      // Load details
      customerId: null,
      itemDescription: '',
      commodity: null,
      transportType: null,
      weightLbs: 0,
      weightUnit: 'lbs',

      // Pickup details
      pickupLocation: {
        name: '',
        addressLine1: '',
        addressLine2: '',
        zip: '',
        city: '',
        state: '',
        country: 'USA',
        location: '',
      },
      pickupStartDate: null,
      pickupEndDate: null,
      mileRadius: 50,

      // Delivery details
      deliveryLocation: {
        name: '',
        addressLine1: '',
        addressLine2: '',
        zip: '',
        city: '',
        state: '',
        country: 'USA',
        location: '',
      },
      deliveryStartDate: null,
      deliveryEndDate: null,

      // Email details
      from: config.from,
      cc: config.cc,
      bcc: config.bcc,
      subject: config.subject,
      emailBody: config.emailBody,

      // Carrier details
      carriers: [],
      carrierEmails: [],
      selectedExistingAttachments: [],

      carrierGroupId: undefined,
    };

    if (request) {
      return {
        ...baseValues,
        transportType: request.transportType || baseValues.transportType,
        commodity: request.commodity || baseValues.commodity,
        weightLbs: request.weightLbs || baseValues.weightLbs,
        weightUnit: request.weightUnit || baseValues.weightUnit,
        pickupStartDate: request.pickupStartDate || baseValues.pickupStartDate,
        pickupEndDate: request.pickupEndDate || baseValues.pickupEndDate,
        deliveryStartDate:
          request.deliveryStartDate || baseValues.deliveryStartDate,
        deliveryEndDate: request.deliveryEndDate || baseValues.deliveryEndDate,
        pickupLocation: {
          name: request.pickupLocation?.name || '',
          addressLine1: request.pickupLocation?.addressLine1 || '',
          addressLine2: request.pickupLocation?.addressLine2 || '',
          zip: request.pickupLocation?.zip || '',
          city: request.pickupLocation?.city || '',
          state: request.pickupLocation?.state || '',
          country: request.pickupLocation?.country || 'USA',
          location:
            request.pickupLocation?.zip ||
            (request.pickupLocation?.city && request.pickupLocation?.state
              ? `${request.pickupLocation.city}, ${request.pickupLocation.state}`
              : ''),
        },
        deliveryLocation: {
          name: request.deliveryLocation?.name || '',
          addressLine1: request.deliveryLocation?.addressLine1 || '',
          addressLine2: request.deliveryLocation?.addressLine2 || '',
          zip: request.deliveryLocation?.zip || '',
          city: request.deliveryLocation?.city || '',
          state: request.deliveryLocation?.state || '',
          country: request.deliveryLocation?.country || 'USA',
          location:
            request.deliveryLocation?.zip ||
            (request.deliveryLocation?.city && request.deliveryLocation?.state
              ? `${request.deliveryLocation.city}, ${request.deliveryLocation.state}`
              : ''),
        },
        carriers: [],
      };
    }

    return baseValues;
  }, [request, config]);

  useEffect(() => {
    carrierProcessingFormMethods.reset(defaultValues);
    messageCarrierFormMethods.reset(defaultValues);
  }, [defaultValues, carrierProcessingFormMethods, messageCarrierFormMethods]);

  const processSelectedGroup: SubmitHandler<CarrierQuoteInputs> = async (
    data
  ) => {
    setLoading(true);
    setSelectedCarriers({});
    messageCarrierFormMethods.reset();
    setCarrierLocations([]);
    setShowMessageCarrier(false);

    if (data.customerId) {
      messageCarrierFormMethods.setValue('customerId', data.customerId);
    }

    if (
      !selectedCarrierGroup ||
      !selectedCarrierGroup.carriers ||
      selectedCarrierGroup.carriers.length === 0
    ) {
      setLoading(false);
      return;
    }

    const groupCarriers = selectedCarrierGroup.carriers;
    const carrierEmails = groupCarriers
      .map((c) => c.email)
      .filter(Boolean) as string[];
    messageCarrierFormMethods.setValue('carrierEmails', carrierEmails);

    const carrierProcessingData = carrierProcessingFormMethods.getValues();

    // Parse locations
    const parsedPickupLocation = parseLocationString(
      carrierProcessingData.pickupLocation.location
    );
    const finalPickupLocation: QuoteRequestAddress & {
      location: Maybe<string>;
    } = {
      name: carrierProcessingData.pickupLocation.name || '',
      addressLine1: carrierProcessingData.pickupLocation.addressLine1 || '',
      addressLine2: carrierProcessingData.pickupLocation.addressLine2 || '',
      city: parsedPickupLocation.city || '',
      state: parsedPickupLocation.state || '',
      zip: parsedPickupLocation.zip || '',
      country: carrierProcessingData.pickupLocation.country || 'USA',
      location: carrierProcessingData.pickupLocation.location,
    };

    let finalDeliveryLocation: Maybe<
      QuoteRequestAddress & { location: Maybe<string> }
    > = null;
    if (
      carrierProcessingData.deliveryLocation?.location ||
      carrierQuoteConfig.requireDeliveryLocation
    ) {
      const parsedDeliveryLocation = parseLocationString(
        carrierProcessingData.deliveryLocation?.location ?? null
      );
      finalDeliveryLocation = {
        name: carrierProcessingData.deliveryLocation?.name || '',
        addressLine1:
          carrierProcessingData.deliveryLocation?.addressLine1 || '',
        addressLine2:
          carrierProcessingData.deliveryLocation?.addressLine2 || '',
        city: parsedDeliveryLocation.city || '',
        state: parsedDeliveryLocation.state || '',
        zip: parsedDeliveryLocation.zip || '',
        country: carrierProcessingData.deliveryLocation?.country || 'USA',
        location: carrierProcessingData.deliveryLocation?.location ?? null,
      };
    }

    messageCarrierFormMethods.setValue(
      'itemDescription',
      carrierProcessingData.itemDescription
    );
    messageCarrierFormMethods.setValue(
      'commodity',
      carrierProcessingData.commodity
    );
    messageCarrierFormMethods.setValue(
      'transportType',
      carrierProcessingData.transportType
    );
    messageCarrierFormMethods.setValue(
      'weightLbs',
      carrierProcessingData.weightLbs
    );
    messageCarrierFormMethods.setValue(
      'weightUnit',
      carrierProcessingData.weightUnit
    );
    messageCarrierFormMethods.setValue('pickupLocation', finalPickupLocation);
    messageCarrierFormMethods.setValue(
      'pickupStartDate',
      carrierProcessingData.pickupStartDate
    );
    messageCarrierFormMethods.setValue(
      'pickupEndDate',
      carrierProcessingData.pickupEndDate
    );
    messageCarrierFormMethods.setValue(
      'deliveryLocation',
      finalDeliveryLocation || {
        name: '',
        addressLine1: '',
        addressLine2: '',
        city: '',
        state: '',
        zip: '',
        country: 'USA',
        location: null,
      }
    );
    messageCarrierFormMethods.setValue(
      'deliveryStartDate',
      carrierProcessingData.deliveryStartDate
    );
    messageCarrierFormMethods.setValue(
      'deliveryEndDate',
      carrierProcessingData.deliveryEndDate
    );
    messageCarrierFormMethods.setValue(
      'selectedExistingAttachments',
      carrierProcessingData.selectedExistingAttachments
    );

    const newBody = constructEmailBody(
      {
        ...carrierProcessingData,
        pickupLocation: finalPickupLocation,
        deliveryLocation: finalDeliveryLocation ?? {
          name: '',
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          zip: '',
          country: 'USA',
          location: null,
        },
      },
      config
    );
    messageCarrierFormMethods.setValue('emailBody', newBody);

    const newSubject = constructEmailSubject(
      finalPickupLocation,
      finalDeliveryLocation,
      config
    );
    messageCarrierFormMethods.setValue('subject', newSubject);
    messageCarrierFormMethods.setValue('from', config.from);
    messageCarrierFormMethods.setValue('cc', config.cc);
    messageCarrierFormMethods.setValue(
      'bcc',
      config.bccCarriers ? [...config.bcc, ...carrierEmails] : config.bcc
    );

    const initialSelected: Record<string, boolean> = {};
    carrierEmails.forEach((email) => {
      initialSelected[email] = true;
    });
    setSelectedCarriers(initialSelected);

    setCarrierLocations(
      groupCarriers.map(
        (gc) =>
          ({
            ...gc,
            emails: gc.email ? [gc.email] : [],
            apptRequired: null,
            isShipper: undefined,
            isConsignee: undefined,
            driverLoadingResponsibility: undefined,
            carrier: gc,
            notes: undefined,
            milesDistance: undefined,
          }) as TMSLocationWithDistance
      )
    );
    setShowMessageCarrier(true);
    setLoading(false);
    toast({
      description: `Drafted email`,
      variant: 'success',
      duration: 1000,
    });
  };

  const handleGroupSelect = useCallback(
    (group: CarrierGroup | null) => {
      setSelectedCarrierGroup(group);
      setShowMessageCarrier(false);
      setCarrierLocations([]);
      if (group && group.carriers && group.carriers.length > 0) {
        carrierProcessingFormMethods.setValue(
          'carrierGroupId',
          group.id.toString()
        );
      } else if (group) {
        carrierProcessingFormMethods.setValue(
          'carrierGroupId',
          group.id.toString()
        );
      } else {
        carrierProcessingFormMethods.setValue('carrierGroupId', undefined);
      }
    },
    [
      setSelectedCarrierGroup,
      setShowMessageCarrier,
      setCarrierLocations,
      carrierProcessingFormMethods.setValue,
      toast,
    ]
  );

  const toggleSelection = (carrierId: string) => {
    setSelectedCarriers((prevSelectedCarriers) => {
      const newSelectedCarriers = {
        ...prevSelectedCarriers,
        [carrierId]: !prevSelectedCarriers[carrierId],
      };

      const selectedEmailsArray = carrierLocations
        .filter(
          (carrier) =>
            carrier.externalTMSID && newSelectedCarriers[carrier.externalTMSID]
        )
        .flatMap(
          (carrier) => carrier.emails || (carrier.email ? [carrier.email] : [])
        )
        .filter((email): email is string => !!email)
        .map((email) => email.trim());
      messageCarrierFormMethods.setValue(
        'carrierEmails',
        Array.from(new Set(selectedEmailsArray))
      );

      // Update areAllCarriersSelected state
      if (carrierLocations.length > 0) {
        const allCurrentlySelected = carrierLocations.every(
          (loc) => loc.externalTMSID && newSelectedCarriers[loc.externalTMSID]
        );
        setAreAllCarriersSelected(allCurrentlySelected);
      } else {
        setAreAllCarriersSelected(false);
      }
      return newSelectedCarriers;
    });
  };

  const handleToggleSelectAllCarriers = () => {
    const newSelectedState = !areAllCarriersSelected;
    const newSelectedCarriers: Record<string, boolean> = {};
    const allCarrierEmailsForForm: string[] = [];

    carrierLocations.forEach((carrier) => {
      if (carrier.externalTMSID) {
        // Ensure externalTMSID exists
        newSelectedCarriers[carrier.externalTMSID] = newSelectedState;
        if (newSelectedState) {
          const emails =
            carrier.emails || (carrier.email ? [carrier.email] : []);
          emails.forEach((email) => {
            if (email) allCarrierEmailsForForm.push(email.trim());
          });
        }
      }
    });

    setSelectedCarriers(newSelectedCarriers);
    messageCarrierFormMethods.setValue(
      'carrierEmails',
      Array.from(new Set(allCarrierEmailsForForm))
    );
    // setAreAllCarriersSelected will be updated by the useEffect watching selectedCarriers
  };

  const onSubmitMessageCarriers: SubmitHandler<CarrierQuoteInputs> = async (
    data
  ) => {
    setLoading(true);

    const formattedAttachments: NewAttachment[] = newAttachments.map(
      (attachment) => ({
        data: attachment.data,
        fileName: attachment.fileName,
        mimeType: attachment.mimeType,
      })
    );

    const processedCcEmails = Array.isArray(data.cc)
      ? data.cc
      : typeof data.cc === 'string'
        ? (data.cc as string)
            .split(',')
            .map((email: string) => email.trim())
            .filter((email: string) => email !== '')
        : [];

    const payloadCarriers: TMSLocationWithDistance[] =
      selectedCarrierGroup?.carriers?.map(
        (gc) =>
          ({
            ...gc,
            emails: gc.email ? [gc.email] : [],
            apptRequired: null,
            isShipper: undefined,
            isConsignee: undefined,
            driverLoadingResponsibility: undefined,
            carrier: gc,
            notes: undefined,
            milesDistance: undefined,
          }) as TMSLocationWithDistance
      ) ?? [];

    const response = await emailCarriersForQuotes(
      {
        ...data,
        weightLbs: data.weightLbs ? parseFloat(data.weightLbs.toString()) : 0,
        id: request?.id ?? 0,
        threadID: email?.threadID ?? '',
        emailID: email?.id ?? 0,
        pallets: request?.pallets ?? [],
        carrierNetworkEmails: null,
        carriers: payloadCarriers,
        carrierGroupId: selectedCarrierGroup?.id ?? undefined,
      },
      data.carrierEmails,
      processedCcEmails,
      data.subject,
      data.emailBody,
      data.selectedExistingAttachments,
      formattedAttachments
    );

    handleEmailCarrierForQuoteResponse(response, setLoading);

    // Refresh the Quote Request so the UI immediately shows the "Review Responses" section
    if (response.isOk() && email?.threadID) {
      mutate(`quote/request/thread/${encodeURIComponent(email.threadID)}`);
    }

    setLoading(false);
  };

  // Handler functions for customer search, moved inside the component body
  const handleRefreshCustomers = async () => {
    const tmsId = tmsIntegrations?.[0]?.id;
    if (!tmsId) return;
    const res = await getCustomers(tmsId, true);
    if (res.isOk()) {
      setInitialCustomers(res.value.customerList);
      setCustomers(res.value.customerList);
      toast({
        description: 'Successfully refreshed customer list.',
        variant: 'success',
      });
    } else {
      toast({
        description: 'Error while refreshing customer list.',
        variant: 'destructive',
      });
    }
  };

  const handleResetCustomerSearch = () => {
    setCustomers(initialCustomers);
  };

  const handleCustomerSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ): Promise<any[]> => {
    const tmsId = tmsIntegrations?.[0]?.id;
    if (!tmsId) {
      return [];
    }
    return customerSearchHandler({
      tmsID: tmsId,
      customers,
      setCustomers,
      field,
      value,
    });
  };

  return (
    <div className='grid grid-cols-1 mx-0 w-full gap-3'>
      <ExtendedFormProvider aiDefaultValues={true}>
        <FormProvider {...carrierProcessingFormMethods}>
          <form
            onSubmit={carrierProcessingFormMethods.handleSubmit(
              processSelectedGroup
            )}
            className='grid grid-cols-1 mx-0 w-full gap-4'
          >
            <LoadInformationForm
              config={config}
              customers={customers}
              handleRefreshCustomers={handleRefreshCustomers}
              handleResetCustomerSearch={handleResetCustomerSearch}
              handleCustomerSearch={handleCustomerSearch}
              tmsIntegrations={tmsIntegrations}
            />

            <div>
              {/* Pickup Location */}
              {config.showPickupAddressLine1 && (
                <div className='flex flex-col mb-2'>
                  <Label name='pickupLocation.addressLine1' required={true}>
                    Pickup Address Line 1
                  </Label>
                  <Input
                    {...carrierProcessingFormMethods.register(
                      'pickupLocation.addressLine1',
                      {
                        required: 'Address Line 1 is required',
                      }
                    )}
                    placeholder='e.g. 123 Main St'
                    className='mt-1'
                  />
                  <ErrorMessage
                    errors={carrierProcessingFormMethods.formState.errors}
                    name='pickupLocation.addressLine1'
                    render={({ message }: { message: string }) => (
                      <p className='text-red-500 text-xs mt-1'>{message}</p>
                    )}
                  />
                </div>
              )}
              {!config.showPickupAddressLine1 && config.showDeliverySection ? (
                <div className='flex justify-between items-start gap-2 w-full mx-0'>
                  <div className='w-full'>
                    <LocationInput
                      name='pickupLocation.location'
                      label='Pickup'
                      placeholder='ZIP or City, State'
                      required={true}
                    />
                  </div>
                  <ArrowRightIcon className='w-8 text-grayscale-icon-stroke mt-7' />
                  <div className='w-full'>
                    <LocationInput
                      name='deliveryLocation.location'
                      label='Dropoff'
                      placeholder='ZIP or City, State'
                      required={config.requireDeliveryLocation}
                    />
                  </div>
                </div>
              ) : (
                <>
                  <LocationInput
                    name='pickupLocation.location'
                    label='Pickup City, State or Zip'
                    placeholder='e.g. 02116 or Boston, MA'
                    required={true}
                  />
                  {config.showDeliverySection && (
                    <div className='mt-2'>
                      {config.showDeliveryAddressLine1 && (
                        <div className='flex flex-col mb-2'>
                          <Label
                            name='deliveryLocation.addressLine1'
                            required={true}
                          >
                            Delivery Address Line 1
                          </Label>
                          <Input
                            {...carrierProcessingFormMethods.register(
                              'deliveryLocation.addressLine1',
                              {
                                required: 'Address Line 1 is required',
                              }
                            )}
                            placeholder='e.g. 123 Main St'
                            className='mt-1'
                          />
                          <ErrorMessage
                            errors={
                              carrierProcessingFormMethods.formState.errors
                            }
                            name='deliveryLocation.addressLine1'
                            render={({ message }: { message: string }) => (
                              <p className='text-red-500 text-xs mt-1'>
                                {message}
                              </p>
                            )}
                          />
                        </div>
                      )}
                      <LocationInput
                        name='deliveryLocation.location'
                        label='Delivery City, State or Zip'
                        placeholder='e.g. 02116 or Boston, MA'
                        required={config.requireDeliveryLocation}
                      />
                    </div>
                  )}
                </>
              )}
            </div>

            {hasAddedQuoteDates && (
              <div className='grid grid-cols-2 gap-4 w-full mx-0'>
                {/* Pickup Date */}
                <div>
                  <Label name='pickupDates' className='mb-1'>
                    Pickup Date
                  </Label>
                  <Controller
                    name={'pickupStartDate'}
                    control={carrierProcessingFormMethods.control}
                    render={({ field }) => {
                      return <DatePicker field={field} />;
                    }}
                  />
                </div>

                {/* Delivery Date */}
                {config.showDeliverySection && (
                  <div>
                    <Label name='deliveryDates' className='mb-1'>
                      Delivery Date
                    </Label>
                    <Controller
                      name={'deliveryStartDate'}
                      control={carrierProcessingFormMethods.control}
                      render={({ field }) => {
                        return <DatePicker field={field} />;
                      }}
                    />
                  </div>
                )}
              </div>
            )}

            {!hasAddedQuoteDates && (
              <AddDateDetailsButton
                buttonNamePosthog={ButtonNamePosthog.AddQuoteDateDetails}
                onClick={() => setHasAddedQuoteDates(true)}
              />
            )}

            <div>
              <h3 className='font-semibold mb-2'>Find Carriers</h3>
              <FindCarrierByGroup
                name='carrierGroupId'
                onGroupSelect={handleGroupSelect}
              />
            </div>

            <Button
              type='submit'
              disabled={
                !selectedCarrierGroup ||
                selectedCarrierGroup.carriers.length === 0 ||
                loading
              }
              buttonNamePosthog={ButtonNamePosthog.DraftEmailForCarrierGroup}
              className='w-full'
            >
              {loading ? 'Processing...' : 'Draft Email for Selected Group'}
            </Button>
          </form>
        </FormProvider>

        {showMessageCarrier && carrierLocations.length > 0 && (
          <FormProvider {...messageCarrierFormMethods}>
            <form
              onSubmit={messageCarrierFormMethods.handleSubmit(
                onSubmitMessageCarriers
              )}
              className='grid grid-cols-1 mx-0 w-full'
            >
              <MessageCarrierForm
                email={email}
                loading={loading}
                carrierLocations={carrierLocations}
                selectedCarriers={selectedCarriers}
                toggleSelection={toggleSelection}
                setNewAttachments={setNewAttachments}
                config={config}
                areAllCarriersSelected={areAllCarriersSelected}
                onToggleSelectAllCarriers={handleToggleSelectAllCarriers}
              />
            </form>
          </FormProvider>
        )}
      </ExtendedFormProvider>
    </div>
  );
}

interface LocationInputProps {
  name: string;
  label: string;
  placeholder: string;
  required?: boolean;
}

const LocationInput = ({
  name,
  label,
  placeholder,
  required = false,
}: LocationInputProps) => {
  return (
    <div className='flex flex-col gap-1'>
      <RHFTextInput
        name={name}
        label={label}
        placeholder={placeholder}
        required={required}
      />
    </div>
  );
};
