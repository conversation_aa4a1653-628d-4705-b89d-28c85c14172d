import { Mail, X } from 'lucide-react';

import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';

import { SelectedCarrierData } from './types';

interface SelectedCarrierTooltipProps {
  carrier: SelectedCarrierData;
  onRemove: (carrierId: string) => void;
}

export default function SelectedCarrierTooltip({
  carrier,
  onRemove,
}: SelectedCarrierTooltipProps) {
  const handleRemove = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    onRemove(carrier.id);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      e.stopPropagation();
      onRemove(carrier.id);
    }
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div
          tabIndex={0}
          aria-label={`Selected carrier: ${carrier.name}. Click to see details or press X to remove.`}
          className='border border-gray-300 hover:bg-gray-100 text-grayscale-content-label px-2.5 py-1 rounded-md text-xs cursor-default truncate flex items-center gap-1.5'
        >
          <span className='truncate'>{carrier.name}</span>
          <button
            type='button'
            aria-label={`Remove ${carrier.name}`}
            onClick={handleRemove}
            onKeyDown={handleKeyDown}
            className='text-grayscale-content-secondary hover:text-grayscale-content-primary focus:outline-none'
          >
            <X size={14} />
          </button>
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <div className='max-w-[280px] p-0.5'>
          <p className='text-grayscale-content-label font-semibold truncate'>
            {carrier.name}
          </p>

          {carrier.city && carrier.state && (
            <div className='flex gap-4 items-center justify-between mb-1'>
              <p className='text-xs text-grayscale-content-tertiary'>
                {carrier.city}, {carrier.state}
              </p>
              {carrier.milesDistance && carrier.milesDistance > 0 && (
                <p className='text-xs text-grayscale-content-label'>
                  ~{carrier.milesDistance.toFixed(1)} mi from pickup
                </p>
              )}
            </div>
          )}

          {carrier.emails.length > 0 && (
            <div className='py-1 mt-1 border-t border-gray-200 flex flex-col gap-0.5'>
              {carrier.emails.map((email, index) => (
                <p key={index} className='text-xs flex items-center gap-1'>
                  <Mail size={10} />
                  {email}
                </p>
              ))}
            </div>
          )}

          {carrier.notes && (
            <div className='py-1 border-t border-gray-200'>
              <p className='text-xs font-medium text-grayscale-content-secondary mb-0.5'>
                Note:
              </p>
              <div className='text-xs text-grayscale-content-input italic'>
                {carrier.notes}
              </div>
            </div>
          )}
        </div>
      </TooltipContent>
    </Tooltip>
  );
}
