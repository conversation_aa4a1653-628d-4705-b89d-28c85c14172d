import { useState } from 'react';

import { CarrierGroup } from 'types/CarrierGroup';
import { TMSCarrier } from 'types/Load';

type DisplayCarrierGroupProps = {
  group: CarrierGroup;
  carriers: TMSCarrier[];
};

const MAX_CARRIERS_DISPLAYED = 6;

export default function DisplayCarrierGroup({
  group,
  carriers,
}: DisplayCarrierGroupProps) {
  const [showAllCarriers, setShowAllCarriers] = useState(false);

  const handleToggleShowAllCarriers = () => {
    setShowAllCarriers((prev) => !prev);
  };

  const carriersToDisplay = showAllCarriers
    ? carriers
    : carriers.slice(0, MAX_CARRIERS_DISPLAYED);

  return (
    <div className='mt-4 p-3 border border-primary rounded bg-white'>
      <h3 className='text-md font-semibold mb-0.5'>
        {group.name || 'Unnamed Group'}
      </h3>

      {group.email && (
        <div className='mb-2 text-sm'>
          <span className='text-xs text-grayscale-content-2'>Group Email:</span>{' '}
          {group.email}
        </div>
      )}

      {carriers.length > 0 ? (
        <>
          <p className='text-xs'>Contacts: {carriers.length}</p>
          <ul className='list-disc pl-3.5 space-y-1'>
            {carriersToDisplay.map((carrier: TMSCarrier, index: number) => (
              <li
                key={
                  carrier.externalTMSID || carrier.email || `carrier-${index}`
                }
                className='text-xs'
              >
                <div className='flex flex-col'>
                  <span className='text-grayscale-content-1'>
                    {carrier.name || 'Unknown Carrier'}
                  </span>
                  {carrier.email && (
                    <span className='text-grayscale-content-2'>
                      {carrier.email}
                    </span>
                  )}
                </div>
              </li>
            ))}
          </ul>
          {carriers.length > MAX_CARRIERS_DISPLAYED && (
            <button
              onClick={handleToggleShowAllCarriers}
              className='mt-2 text-xs text-primary hover:underline focus:outline-none'
              aria-label={
                showAllCarriers ? 'Hide extra carriers' : 'Show all carriers'
              }
              type='button'
            >
              {showAllCarriers
                ? 'Hide'
                : `Show more (${carriers.length - MAX_CARRIERS_DISPLAYED} more)`}
            </button>
          )}
        </>
      ) : (
        <div className='text-sm text-gray-500 mb-4'>
          No carriers found in this group.
        </div>
      )}
    </div>
  );
}
