import {
  CarrierFormConfig,
  CarrierQuoteInputs,
} from 'pages/QuoteView/Quoting/CarrierQuote/types';
import { Address, TransportType } from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import { titleCase } from 'utils/formatStrings';

const formatAddress = (
  address: Maybe<Address>,
  showAddressLine1: boolean
): string => {
  if (!address) return '';

  const { addressLine1, city, state, zip } = address;
  const addressOutputParts: string[] = [];

  if (showAddressLine1 && addressLine1) {
    addressOutputParts.push(addressLine1);
  }

  const cityStr = city ? titleCase(city) : null;
  const stateStr = state || null;
  const zipStr = zip || null;

  let locationString = '';
  if (cityStr && stateStr) {
    locationString = `${cityStr}, ${stateStr}`;
    if (zipStr) {
      locationString += ` ${zipStr}`;
    }
  } else if (cityStr && zipStr) {
    locationString = `${cityStr} ${zipStr}`;
  } else if (stateStr && zipStr) {
    locationString = `${stateStr} ${zipStr}`;
  } else if (cityStr) {
    locationString = cityStr;
  } else if (stateStr) {
    locationString = stateStr;
  } else if (zipStr) {
    locationString = zipStr;
  }

  if (locationString) {
    addressOutputParts.push(locationString);
  }

  return addressOutputParts.join(', ');
};

const formatTransportType = (type: TransportType): string => {
  return type.toLowerCase();
};

export const constructEmailBody = (
  data: CarrierQuoteInputs,
  config: CarrierFormConfig
): string => {
  if (config.isFindCarrierByLocationEnabled) {
    // Use toLocaleTimeString with options to format the time without seconds
    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    };

    const timeAvailability = data.pickupStartDate
      ? `between ${new Date(data.pickupStartDate).toLocaleTimeString('en-US', timeOptions)} and ${data.pickupEndDate ? new Date(data.pickupEndDate).toLocaleTimeString('en-US', timeOptions) : 'TBD'}`
      : `
Please let me know your availability`;

    return `Please see the info below and let me know your quote.

Pickup in ${formatAddress(data.pickupLocation, config.showPickupAddressLine1)} ${data.pickupStartDate ? `on ${new Date(data.pickupStartDate).toLocaleDateString('en-US')}` : ''} ${timeAvailability}

Job Description:
${data.itemDescription}

>>> We would need you to recover the loose material listed above to be palletized & securely shrink wrapped. Once the pallet/s are back at your warehouse we will send in a truck to recover the freight from your dock.
 
Thank you`;
  }

  return `Hello, I am scheduling a pickup in ${formatAddress(data.pickupLocation, config.showPickupAddressLine1)} ${data.deliveryLocation ? `to be dropped off at ${formatAddress(data.deliveryLocation, config.showPickupAddressLine1)}` : ''}. Please see the info below and let me know if you can help and your quote.

Pickup Time: ${data.pickupStartDate ? new Date(data.pickupStartDate).toLocaleDateString('en-US') : 'TBD'}

Dropoff Time: ${data.deliveryStartDate ? new Date(data.deliveryStartDate).toLocaleDateString('en-US') : 'TBD'}

Equipment: ${data.transportType ? formatTransportType(data.transportType) : 'TBD'}`;
};

export const constructEmailSubject = (
  pickup: Address,
  dropoff: Maybe<Address>,
  config: CarrierFormConfig
): string => {
  // use subject line in CQ config to determine subject line
  const isSDS = config.subject === 'SDS';

  if (isSDS) {
    return `[Carrier Name] - Quote Needed - ${formatAddress(pickup, false)} for SDS Logistics`;
  }

  return `Quote Pick-up ${formatAddress(pickup, false)} ${dropoff ? `to ${formatAddress(dropoff, false)}` : ''}`;
};
