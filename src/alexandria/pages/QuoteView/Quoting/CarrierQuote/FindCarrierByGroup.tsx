import { useEffect, useMemo, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { ValueType } from 'components/DebounceSelect';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { searchCarrierGroups } from 'lib/api/searchCarrierGroups';
import { CarrierGroup } from 'types/CarrierGroup';
import { TMSCarrier } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';

import DisplayCarrierGroup from './DisplayCarrierGroup';
import {
  carrierGroupSearchHandler,
  mapCarrierGroupsToAntdOptions,
} from './helperFunctions';

interface FindCarrierByGroupProps {
  name?: string;
  onGroupSelect?: (group: CarrierGroup | null) => void;
}

export default function FindCarrierByGroup({
  name = 'carrierGroupId',
  onGroupSelect,
}: FindCarrierByGroupProps) {
  const [carrierGroups, setCarrierGroups] = useState<Maybe<CarrierGroup[]>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const selectedGroupRef = useRef<CarrierGroup | null>(null);
  const [displayGroup, setDisplayGroup] = useState<CarrierGroup | null>(null);

  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext();
  const watchedGroupId = watch(name);

  // Memoize the initial set of mapped options to prevent re-mapping on every render
  const initialMappedOptions = useMemo(() => {
    return mapCarrierGroupsToAntdOptions(carrierGroups);
  }, [carrierGroups]);

  // Initial load of carrier groups
  useEffect(() => {
    fetchAllCarrierGroups();
  }, []);

  // Update display group when form value changes
  useEffect(() => {
    const groupId = Number(watchedGroupId);
    if (!watchedGroupId) {
      setDisplayGroup(null);
      selectedGroupRef.current = null;
      onGroupSelect?.(null);
      return;
    }

    const group = carrierGroups?.find((g) => g.id === groupId) ?? null;
    if (group) {
      selectedGroupRef.current = group;
      setDisplayGroup(group);
      onGroupSelect?.(group);
    } else {
      setDisplayGroup(null);
      selectedGroupRef.current = null;
      onGroupSelect?.(null);
    }
  }, [watchedGroupId, carrierGroups, onGroupSelect]);

  const fetchAllCarrierGroups = async () => {
    setIsLoading(true);
    try {
      const groups = await searchCarrierGroups('');
      // Sort groups alphabetically by name
      const sortedGroups = groups.sort((a, b) => {
        if (a.name && b.name) {
          return a.name.localeCompare(b.name);
        }
        return 0; // Keep original order if names are missing
      });
      setCarrierGroups(sortedGroups);
    } catch (error) {
      console.error('Error loading carrier groups:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefreshGroups = async () => {
    fetchAllCarrierGroups();
  };

  const handleCarrierGroupSearch = async (_field: string, value: string) => {
    return carrierGroupSearchHandler({
      carrierGroups,
      value,
    });
  };

  const handleGroupSelection = (option: ValueType | any) => {
    const selectedId =
      typeof option === 'object' &&
      option !== null &&
      option.value !== undefined
        ? option.value
        : option;

    let groupForSelection: CarrierGroup | null = null;

    if (selectedId != null) {
      const foundGroup = carrierGroups?.find(
        (group) => group.id === Number(selectedId)
      );

      if (foundGroup) {
        groupForSelection = {
          ...foundGroup,
          carriers: foundGroup.carriers || [], // Ensure carriers is always an array
        };
      }
    }

    if (groupForSelection) {
      setValue(name, groupForSelection.id, {
        shouldValidate: false,
        shouldDirty: true,
      });
      selectedGroupRef.current = groupForSelection;
      setDisplayGroup(groupForSelection);
      onGroupSelect?.(groupForSelection);
    } else {
      setValue(name, '');
      selectedGroupRef.current = null;
      setDisplayGroup(null);
      onGroupSelect?.(null);
    }
  };

  const carriers = useMemo((): TMSCarrier[] => {
    if (!displayGroup) {
      return [];
    }
    if (!displayGroup.carriers) {
      return [];
    }
    if (Array.isArray(displayGroup.carriers)) {
      return displayGroup.carriers;
    }
    return [];
  }, [displayGroup]);

  return (
    <div className='w-full max-w-xl mx-auto'>
      <RHFDebounceSelect
        key={String(watchedGroupId ?? 'no-group-selected')}
        required
        name={name}
        label='Search Carrier Groups'
        control={control}
        errors={errors}
        data={initialMappedOptions}
        mapOptions={(option: ValueType) => option}
        fetchOptions={handleCarrierGroupSearch}
        isLoading={isLoading}
        refreshHandler={handleRefreshGroups}
        placeholder='Enter group name'
        parentOnChange={handleGroupSelection}
        showSearchParamDropdown={false}
      />

      {displayGroup && (
        <DisplayCarrierGroup group={displayGroup} carriers={carriers} />
      )}
    </div>
  );
}
