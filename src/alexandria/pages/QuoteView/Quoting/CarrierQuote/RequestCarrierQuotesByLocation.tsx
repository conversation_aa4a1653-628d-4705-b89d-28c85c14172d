import { useEffect, useMemo, useState } from 'react';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';

import { useSWRConfig } from 'swr';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import isProd from '@utils/isProd';

import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { CarrierQuoteConfig } from 'contexts/serviceContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import {
  NewAttachment,
  emailCarriersForQuotes,
} from 'lib/api/emailCarriersForQuotes';
import { getCustomers } from 'lib/api/getCustomers';
import { searchLocations } from 'lib/api/searchLocations';
import { ProcessedAttachment } from 'pages/QuoteView/Quoting/CarrierQuote/AttachmentUpload';
import FindCarrierForm from 'pages/QuoteView/Quoting/CarrierQuote/FindCarrierForm';
import MessageCarrierForm from 'pages/QuoteView/Quoting/CarrierQuote/MessageCarrierForm';
import {
  CarrierFormConfig,
  CarrierQuoteInputs,
} from 'pages/QuoteView/Quoting/CarrierQuote/types';
import { Email } from 'types/Email';
import { TMSCustomer, TMSLocationWithDistance } from 'types/Load';
import { QuoteRequest } from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import captureException from 'utils/captureException';
import {
  GenericCompanySearchableFields,
  customerSearchHandler,
} from 'utils/loadInfoAndBuilding';

import { constructEmailBody, constructEmailSubject } from './emailTemplate';
import {
  handleEmailCarrierForQuoteResponse,
  parseLocation,
} from './helperFunctions';

interface RequestCarrierQuoteByLocationProps {
  email: Maybe<Email>;
  request: Maybe<QuoteRequest>;
  // QuoteRequest may be null if there's a transient network error getting the parsed values. In those cases, still show the form
  carrierQuoteConfig: CarrierQuoteConfig;
}

export interface CarrierSelection {
  email: string;
  isSelected: boolean;
}

const hasDrumkitKeyword = (carrier: TMSLocationWithDistance): boolean => {
  if (!carrier) return false;

  const name = (carrier.carrier?.name || carrier.name || '').toLowerCase();
  const mainEmail = (carrier.email || '').toLowerCase();
  const otherEmails = (carrier.emails || [])
    .map((e) => (e || '').toLowerCase())
    .filter((e) => e);

  return (
    name.includes('drumkit') ||
    mainEmail.includes('drumkit') ||
    otherEmails.some((e) => e.includes('drumkit'))
  );
};

export default function RequestCarrierQuotesByLocation({
  email,
  request,
  carrierQuoteConfig,
}: RequestCarrierQuoteByLocationProps) {
  const { toast } = useToast();
  const { mutate } = useSWRConfig();
  const [customers, setCustomers] = useState<Maybe<TMSCustomer[]>>(null);
  const [initialCustomers, setInitialCustomers] =
    useState<Maybe<TMSCustomer[]>>(null);

  const [loading, setLoading] = useState(false);
  const [carrierLocations, setCarrierLocations] = useState<
    TMSLocationWithDistance[]
  >([]);
  const [showEmptyState, setShowEmptyState] = useState(false);
  const [showMessageCarrier, setShowMessageCarrier] = useState(false);
  const [selectedCarriers, setSelectedCarriers] = useState<
    Record<string, boolean>
  >({});
  const [newAttachments, setNewAttachments] = useState<ProcessedAttachment[]>(
    []
  );
  const [areAllCarriersSelected, setAreAllCarriersSelected] =
    useState<boolean>(false);
  const [lastSearchedLocation, setLastSearchedLocation] = useState<{
    city: string;
    state: string;
    zip: string;
  }>({ city: '', state: '', zip: '' });
  const { serviceID, tmsIntegrations } = useServiceFeatures();

  useEffect(() => {
    if (carrierLocations.length === 0) {
      setAreAllCarriersSelected(false);
      return;
    }

    const selectableCarriers = carrierLocations.filter(
      (carrier) => carrier.externalTMSID && !hasDrumkitKeyword(carrier)
    );

    if (selectableCarriers.length === 0) {
      setAreAllCarriersSelected(false);
      return;
    }

    const allActuallySelected = selectableCarriers.every(
      (carrier) => selectedCarriers[carrier.externalTMSID!] === true
    );

    setAreAllCarriersSelected(allActuallySelected);
  }, [carrierLocations, selectedCarriers]);

  // Early return if no TMS integrations are available
  useEffect(() => {
    if (!tmsIntegrations?.length) {
      return;
    }

    // Log warning if multiple TMS integrations are found in production
    if (tmsIntegrations.length > 1 && isProd()) {
      captureException(
        new Error(
          'Service with multiple TMS integrations trying to find carriers'
        ),
        { serviceID: serviceID, tmsIntegrations: tmsIntegrations }
      );
    }
  }, [tmsIntegrations, serviceID, toast]);

  // Define form conditions based on carrierQuoteConfig - since we've verified carrierQuoteConfig exists
  // we can safely use its properties directly
  const config: CarrierFormConfig = {
    from: carrierQuoteConfig.from ?? '',
    cc: carrierQuoteConfig.cc ?? [],
    bcc: carrierQuoteConfig.bcc ?? [],
    bccCarriers: carrierQuoteConfig.bccCarriers,
    subject: carrierQuoteConfig.subject ?? '',
    emailBody: carrierQuoteConfig.emailBody ?? '',
    showCustomerSearch: carrierQuoteConfig.showCustomerSearch,
    showItemDescription: carrierQuoteConfig.showItemDescription,
    showDeliverySection: carrierQuoteConfig.showDeliverySection,
    requireDeliveryLocation: carrierQuoteConfig.requireDeliveryLocation,
    showPickupAddressLine1: carrierQuoteConfig.showPickupAddressLine1,
    showDeliveryAddressLine1: carrierQuoteConfig.showDeliveryAddressLine1,
    isFindCarrierByGroupEnabled: carrierQuoteConfig.isFindCarrierByGroupEnabled,
    isFindCarrierByLocationEnabled:
      carrierQuoteConfig.isFindCarrierByLocationEnabled,
  };

  const handleRefreshCustomers = async () => {
    if (!tmsIntegrations?.length) {
      toast({
        description: 'No TMS integration available.',
        variant: 'destructive',
      });
      return;
    }
    const res = await getCustomers(tmsIntegrations[0].id, true);
    if (res.isOk()) {
      setInitialCustomers(res.value.customerList);
      setCustomers(res.value.customerList);
      toast({
        description: 'Successfully refreshed customer list.',
        variant: 'success',
      });
    } else {
      toast({
        description: 'Error while refreshing customer list.',
        variant: 'destructive',
      });
    }
  };

  const handleResetCustomerSearch = () => {
    setCustomers(initialCustomers);
  };

  const handleCustomerSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    if (!tmsIntegrations?.length) return [];
    return customerSearchHandler({
      tmsID: tmsIntegrations[0].id,
      customers,
      setCustomers,
      field,
      value,
    });
  };

  useEffect(() => {
    if (tmsIntegrations?.length) {
      const fetchCustomers = async () => {
        const res = await getCustomers(tmsIntegrations[0].id);
        if (res.isOk()) {
          setInitialCustomers(res.value.customerList);
          setCustomers(res.value.customerList);
        }
      };
      fetchCustomers();
    }
  }, [tmsIntegrations]);

  const defaultValues = useMemo<CarrierQuoteInputs>(() => {
    const baseValues: CarrierQuoteInputs = {
      // Load details
      customerId: null,
      itemDescription: '',
      commodity: null,
      transportType: null,
      weightLbs: 0,
      weightUnit: 'lbs',

      // Pickup details
      pickupLocation: {
        name: '',
        addressLine1: '',
        addressLine2: '',
        zip: '',
        city: '',
        state: '',
        country: 'USA',
        location: '',
      },
      pickupStartDate: null,
      pickupEndDate: null,
      mileRadius: 50,

      // Delivery details
      deliveryLocation: {
        name: '',
        addressLine1: '',
        addressLine2: '',
        zip: '',
        city: '',
        state: '',
        country: 'USA',
        location: '',
      },
      deliveryStartDate: null,
      deliveryEndDate: null,

      // Email details
      from: config.from,
      cc: config.cc,
      bcc: config.bcc,
      subject: config.subject,
      emailBody: config.emailBody,

      // Carrier details
      carriers: [],
      carrierEmails: [],
      selectedExistingAttachments: [],
    };

    if (request) {
      return {
        ...baseValues,
        transportType: request.transportType || baseValues.transportType,
        commodity: request.commodity || baseValues.commodity,
        weightLbs: request.weightLbs || baseValues.weightLbs,
        weightUnit: request.weightUnit || baseValues.weightUnit,
        pickupStartDate: request.pickupStartDate || baseValues.pickupStartDate,
        pickupEndDate: request.pickupEndDate || baseValues.pickupEndDate,
        deliveryStartDate:
          request.deliveryStartDate || baseValues.deliveryStartDate,
        deliveryEndDate: request.deliveryEndDate || baseValues.deliveryEndDate,
        pickupLocation: {
          name: request.pickupLocation?.name || '',
          addressLine1: request.pickupLocation?.addressLine1 || '',
          addressLine2: request.pickupLocation?.addressLine2 || '',
          zip: request.pickupLocation?.zip || '',
          city: request.pickupLocation?.city || '',
          state: request.pickupLocation?.state || '',
          country: request.pickupLocation?.country || 'USA',
          location:
            request.pickupLocation?.zip ||
            (request.pickupLocation?.city && request.pickupLocation?.state
              ? `${request.pickupLocation.city}, ${request.pickupLocation.state}`
              : ''),
        },
        deliveryLocation: {
          name: request.deliveryLocation?.name || '',
          addressLine1: request.deliveryLocation?.addressLine1 || '',
          addressLine2: request.deliveryLocation?.addressLine2 || '',
          zip: request.deliveryLocation?.zip || '',
          city: request.deliveryLocation?.city || '',
          state: request.deliveryLocation?.state || '',
          country: request.deliveryLocation?.country || 'USA',
          location:
            request.deliveryLocation?.zip ||
            (request.deliveryLocation?.city && request.deliveryLocation?.state
              ? `${request.deliveryLocation.city}, ${request.deliveryLocation.state}`
              : ''),
        },
        carriers: [],
      };
    }

    return baseValues;
  }, [request, config]);

  // Define forms and state for both search methods
  // Form for finding carriers by location
  const findCarrierFormMethods = useForm<CarrierQuoteInputs>({
    defaultValues,
    mode: 'onChange',
    resolver: async (values) => {
      const errors: Record<string, any> = {};

      // Validate pickup location
      if (!values.pickupLocation?.location) {
        errors.pickupLocation = {
          location: {
            type: 'required',
            message: 'Pickup location is required',
          },
        };
      }

      // Validate mile radius
      if (!values.mileRadius) {
        errors.mileRadius = {
          type: 'required',
          message: 'Search radius is required',
        };
      } else if (values.mileRadius < 1) {
        errors.mileRadius = {
          type: 'min',
          message: 'Search radius must be at least 1 mile',
        };
      }

      if (
        config.requireDeliveryLocation &&
        !values.deliveryLocation?.location
      ) {
        errors.deliveryLocation = {
          location: {
            type: 'required',
            message: 'Delivery location is required',
          },
        };
      }

      return {
        values,
        errors: Object.keys(errors).length > 0 ? errors : {},
      };
    },
  });

  // Form for messaging carriers
  const messageCarrierFormMethods = useForm<CarrierQuoteInputs>({
    defaultValues,
    mode: 'onChange',
  });

  // Find carriers handler for FindCarrierForm button
  const onSubmitFindCarriers = async (data: CarrierQuoteInputs) => {
    if (!tmsIntegrations?.length) {
      toast({
        description: 'No TMS integration available.',
        variant: 'destructive',
      });
      return;
    }
    setLoading(true);
    // Reset carrier selections
    setSelectedCarriers({});
    messageCarrierFormMethods.setValue('carrierEmails', []);

    if (data.customerId) {
      messageCarrierFormMethods.setValue('customerId', data.customerId);
    }

    // Parse the pickup location input to extract city, state, and zip
    const pickupLocationInput = data.pickupLocation.location || '';
    const parsedPickupLocation = parseLocation(pickupLocationInput);

    // Store the last searched location
    if (!parsedPickupLocation) {
      toast({
        description: 'Invalid pickup location',
        variant: 'destructive',
      });
      return;
    }
    setLastSearchedLocation(parsedPickupLocation);

    // Update both the form values and the data object with the parsed pickup location data
    messageCarrierFormMethods.setValue(
      'pickupLocation.city',
      parsedPickupLocation.city
    );
    messageCarrierFormMethods.setValue(
      'pickupLocation.state',
      parsedPickupLocation.state
    );
    messageCarrierFormMethods.setValue(
      'pickupLocation.zip',
      parsedPickupLocation.zip
    );
    data.pickupLocation.city = parsedPickupLocation.city;
    data.pickupLocation.state = parsedPickupLocation.state;
    data.pickupLocation.zip = parsedPickupLocation.zip;

    // Parse the delivery location input to extract city, state, and zip
    const deliveryLocationInput = data.deliveryLocation?.location || '';
    const parsedDeliveryLocation = parseLocation(deliveryLocationInput);

    // Delivery is optional
    if (parsedDeliveryLocation) {
      // Update both the form values and the data object with the parsed delivery location data
      messageCarrierFormMethods.setValue(
        'deliveryLocation.city',
        parsedDeliveryLocation.city
      );
      messageCarrierFormMethods.setValue(
        'deliveryLocation.state',
        parsedDeliveryLocation.state
      );
      messageCarrierFormMethods.setValue(
        'deliveryLocation.zip',
        parsedDeliveryLocation.zip
      );
      if (data.deliveryLocation) {
        data.deliveryLocation.city = parsedDeliveryLocation.city;
        data.deliveryLocation.state = parsedDeliveryLocation.state;
        data.deliveryLocation.zip = parsedDeliveryLocation.zip;
      }
    }

    const searchRes = await searchLocations(
      tmsIntegrations[0].id,
      undefined, // key required as part of interface (for name and addressLine1 searches)
      undefined, // value also required to maintain backwards compatibility with existing searchLocations API
      data.mileRadius ?? 0,
      parsedPickupLocation.city,
      parsedPickupLocation.state,
      parsedPickupLocation.zip
    );

    let searchedLocations: TMSLocationWithDistance[] = [];
    if (searchRes.isOk()) {
      searchedLocations = searchRes.value.locationList;
      setCarrierLocations(searchedLocations);
      messageCarrierFormMethods.setValue('carriers', searchedLocations);

      // Initialize selectedCarriers: select the 3 closest non-drumkit carriers by default
      const initialSelected: Record<string, boolean> = {};
      const initialEmails: string[] = [];

      const eligibleCarriers = searchedLocations
        .filter(
          (carrier) => carrier.externalTMSID && !hasDrumkitKeyword(carrier)
        )
        .sort(
          (a, b) =>
            (a.milesDistance ?? Infinity) - (b.milesDistance ?? Infinity)
        )
        .slice(0, 3);

      // First, mark all as false
      searchedLocations.forEach((carrier) => {
        if (carrier.externalTMSID) {
          initialSelected[carrier.externalTMSID] = false;
        }
      });

      // Then, mark the top 3 (or fewer) eligible as true and collect their emails
      eligibleCarriers.forEach((carrier) => {
        if (carrier.externalTMSID) {
          // Should always be true due to filter above
          initialSelected[carrier.externalTMSID] = true;
          const emails =
            carrier.emails || (carrier.email ? [carrier.email] : []);

          emails.forEach((email) => {
            if (email) initialEmails.push(email.trim());
          });
        }
      });

      setSelectedCarriers(initialSelected);
      messageCarrierFormMethods.setValue(
        'carrierEmails',
        Array.from(new Set(initialEmails))
      );

      // If we searched with ZIP code, update city and state from the API response
      if (parsedPickupLocation.zip && searchRes.value.location) {
        const resolvedCity = searchRes.value.location.city;
        const resolvedState = searchRes.value.location.state;

        messageCarrierFormMethods.setValue('pickupLocation.city', resolvedCity);
        messageCarrierFormMethods.setValue(
          'pickupLocation.state',
          resolvedState
        );

        // Update the pickup location with resolved values for email subject
        data.pickupLocation.city = resolvedCity;
        data.pickupLocation.state = resolvedState;
      }

      if (searchedLocations.length > 0) {
        toast({
          description: `Found ${searchedLocations.length} 
          carrier${searchedLocations.length === 1 ? '' : 's'} near 
          ${messageCarrierFormMethods.getValues('pickupLocation.city') || lastSearchedLocation.zip}, ${messageCarrierFormMethods.getValues('pickupLocation.state')}`,
          variant: 'success',
        });
        setShowEmptyState(false);
        setShowMessageCarrier(true);
      } else {
        setShowEmptyState(true);
        toast({
          description: `No carriers found near ${messageCarrierFormMethods.getValues('pickupLocation.city') || lastSearchedLocation.zip}, ${messageCarrierFormMethods.getValues('pickupLocation.state')}`,
          variant: 'destructive',
        });
      }
    }

    if (searchRes.isErr()) {
      toast({
        description: searchRes.error.message,
        variant: 'destructive',
      });
      setLoading(false);
      return;
    }

    // Construct email after carriers are found and location is resolved
    const newBody = constructEmailBody(
      {
        ...data,
        deliveryLocation: data.deliveryLocation ?? {
          name: '',
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          zip: '',
          country: 'USA',
          location: null,
        },
      },
      config
    );
    messageCarrierFormMethods.setValue('emailBody', newBody);

    // Now construct the subject with the resolved location data
    const newSubject = constructEmailSubject(
      {
        ...data.pickupLocation,
        city: messageCarrierFormMethods.getValues('pickupLocation.city'),
        state: messageCarrierFormMethods.getValues('pickupLocation.state'),
      },
      data.deliveryLocation,
      config
    );
    messageCarrierFormMethods.setValue('subject', newSubject);

    setLoading(false);
  };

  const toggleSelection = (carrierId: string) => {
    setSelectedCarriers((prevSelectedCarriers) => {
      const newSelectedCarriers = {
        ...prevSelectedCarriers,
        [carrierId]: !prevSelectedCarriers[carrierId],
      };

      const selectedEmailsArray = carrierLocations
        .filter(
          (carrier) =>
            carrier.externalTMSID && newSelectedCarriers[carrier.externalTMSID]
        )
        .flatMap(
          (carrier) => carrier.emails || (carrier.email ? [carrier.email] : [])
        )
        .filter((email): email is string => !!email)
        .map((email) => email.trim());
      messageCarrierFormMethods.setValue(
        'carrierEmails',
        Array.from(new Set(selectedEmailsArray))
      );
      // areAllCarriersSelected will be updated by its useEffect
      return newSelectedCarriers;
    });
  };

  const handleToggleSelectAllCarriers = () => {
    const newGlobalSelectedState = !areAllCarriersSelected;
    const newSelectedCarriersMap: Record<string, boolean> = {};
    const newCarrierEmailsList: string[] = [];

    carrierLocations.forEach((carrier) => {
      if (carrier.externalTMSID) {
        if (!hasDrumkitKeyword(carrier)) {
          // Only affect non-drumkit carriers for selection
          newSelectedCarriersMap[carrier.externalTMSID] =
            newGlobalSelectedState;

          if (newGlobalSelectedState) {
            const emails =
              carrier.emails || (carrier.email ? [carrier.email] : []);
            emails.forEach((email) => {
              if (email) newCarrierEmailsList.push(email.trim());
            });
          }
        } else {
          // Ensure drumkit carriers are explicitly deselected by this action
          newSelectedCarriersMap[carrier.externalTMSID] = false;
        }
      }
    });

    setSelectedCarriers(newSelectedCarriersMap);
    messageCarrierFormMethods.setValue(
      'carrierEmails',
      Array.from(new Set(newCarrierEmailsList)) // Emails are derived from newCarrierEmailsList populated above
    );
    // areAllCarriersSelected will be updated by its useEffect
  };

  // onSubmitMessageCarriers calls backend endpoint that sends emails to selected carriers
  const onSubmitMessageCarriers: SubmitHandler<CarrierQuoteInputs> = async (
    data
  ) => {
    setLoading(true);

    const formattedAttachments: NewAttachment[] = newAttachments.map(
      (attachment) => ({
        data: attachment.data,
        fileName: attachment.fileName,
        mimeType: attachment.mimeType,
      })
    );

    // Process cc emails - convert from comma-separated string to array of strings if needed
    const processedCcEmails = Array.isArray(data.cc)
      ? data.cc
      : typeof data.cc === 'string'
        ? (data.cc as string)
            .split(',')
            .map((email: string) => email.trim())
            .filter((email: string) => email !== '')
        : [];

    const response = await emailCarriersForQuotes(
      {
        ...data,
        id: request?.id ?? 0,
        threadID: email?.threadID ?? '',
        emailID: email?.id ?? 0,
        pallets: request?.pallets ?? [],
        carrierNetworkEmails: null,
        carriers: data.carriers,
        from: data.from,
        carrierGroupId: undefined,
      },
      data.carrierEmails,
      processedCcEmails,
      data.subject,
      data.emailBody,
      data.selectedExistingAttachments,
      formattedAttachments
    );

    handleEmailCarrierForQuoteResponse(response, setLoading);

    // Refresh the Quote Request so the UI immediately shows the "Review Responses" section
    if (response.isOk() && email?.threadID) {
      mutate(`quote/request/thread/${encodeURIComponent(email.threadID)}`);
    }

    setLoading(false);
  };

  return (
    <div className='grid gap-6 grid-cols-1 mx-0 w-full'>
      <ExtendedFormProvider aiDefaultValues={true}>
        {/* Form for finding carriers */}
        <FormProvider {...findCarrierFormMethods}>
          <form
            onSubmit={findCarrierFormMethods.handleSubmit(onSubmitFindCarriers)}
            className='grid grid-cols-1 mx-0 w-full'
          >
            <FindCarrierForm
              loading={loading}
              config={config}
              customers={customers}
              handleRefreshCustomers={handleRefreshCustomers}
              handleResetCustomerSearch={handleResetCustomerSearch}
              handleCustomerSearch={handleCustomerSearch}
              tmsIntegrations={tmsIntegrations}
            />
          </form>
        </FormProvider>

        {/* Empty state message when no carriers found */}
        {!loading && carrierLocations.length === 0 && showEmptyState && (
          <div className='flex flex-col items-center justify-center mt-4 p-3 rounded-lg border border-orange-main'>
            <div className='text-md text-grayscale-content-label font-semibold mb-2 text-center'>
              No carriers found
            </div>
            <p className='text-sm text-grayscale-content-secondary'>
              We couldn't find any carrier locations near{' '}
              {messageCarrierFormMethods.getValues('pickupLocation.city') ||
                lastSearchedLocation.zip}
              , {messageCarrierFormMethods.getValues('pickupLocation.state')}.
              Try adjusting your search radius or location.
            </p>
            <p className='text-xs text-grayscale-content-secondary italic mt-2'>
              If you recently updated locations in Turvo you may need to refresh
              carrier locations.
            </p>
          </div>
        )}

        {/* Form for messaging carriers, only shown after carriers are found */}
        {showMessageCarrier && carrierLocations.length > 0 && (
          <FormProvider {...messageCarrierFormMethods}>
            <form
              onSubmit={messageCarrierFormMethods.handleSubmit(
                onSubmitMessageCarriers
              )}
              className='grid grid-cols-1 mx-0 w-full'
            >
              <MessageCarrierForm
                loading={loading}
                carrierLocations={carrierLocations}
                selectedCarriers={selectedCarriers}
                toggleSelection={toggleSelection}
                email={email}
                setNewAttachments={setNewAttachments}
                config={config}
                areAllCarriersSelected={areAllCarriersSelected}
                onToggleSelectAllCarriers={handleToggleSelectAllCarriers}
              />
            </form>
          </FormProvider>
        )}
      </ExtendedFormProvider>
    </div>
  );
}
