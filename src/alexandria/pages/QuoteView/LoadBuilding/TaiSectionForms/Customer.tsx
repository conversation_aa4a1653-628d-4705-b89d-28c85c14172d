import { useEffect } from 'react';
import { FieldPath, UseFormReturn } from 'react-hook-form';

import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { getCustomers } from 'lib/api/getCustomers';
import { NormalizedLoad, TMSCustomer } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import {
  GenericCompanySearchableFields,
  customerSearchHandler,
  mapCustomerToAntdOptions,
} from 'utils/loadInfoAndBuilding';

import { LoadBuildingTextInput } from '../McleodLoadBuildingForm';

export type CustomerSectionFormProps = {
  formMethods: UseFormReturn<NormalizedLoad>;
  customers: Maybe<TMSCustomer[]>;
  setCustomers: React.Dispatch<React.SetStateAction<Maybe<TMSCustomer[]>>>;
  isLoadingCustomers: boolean;
  setIsLoadingCustomers: React.Dispatch<React.SetStateAction<boolean>>;
};

export function CustomerSectionForm({
  formMethods,
  customers,
  setCustomers,
  isLoadingCustomers,
  setIsLoadingCustomers,
}: CustomerSectionFormProps): JSX.Element {
  const {
    control,
    formState: { errors },
    watch,
    setValue,
  } = formMethods;
  const { tmsIntegrations } = useServiceFeatures();
  const { toast } = useToast();

  const watchedCustomer = watch('customer');
  const watchedAddress2 = watch('customer.addressLine2');

  const handleRefreshCustomers = async () => {
    setIsLoadingCustomers(true);
    const res = await getCustomers(tmsIntegrations?.[0]?.id);
    if (res.isOk()) {
      setCustomers(res.value.customerList);
      toast({
        description: 'Successfully refreshed customer list.',
        variant: 'success',
      });
    } else {
      toast({
        description: 'Error while refreshing customer list.',
        variant: 'destructive',
      });
    }
    setIsLoadingCustomers(false);
  };

  const handleCustomerSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    return customerSearchHandler({
      tmsID: tmsIntegrations?.[0]?.id,
      customers,
      setCustomers,
      field,
      value,
    });
  };

  useEffect(() => {
    if (watchedCustomer?.externalTMSID) {
      const selectedCustomer = customers?.find(
        (c) => c.externalTMSID === watchedCustomer?.externalTMSID
      );
      if (!selectedCustomer) {
        return;
      }

      Object.entries(selectedCustomer).forEach(([key, value]) => {
        setValue(`customer.${key}` as FieldPath<NormalizedLoad>, value, {
          shouldDirty: true,
        });
      });
    }
  }, [watchedCustomer?.externalTMSID]);

  return (
    <>
      <RHFDebounceSelect
        required
        name='customer.externalTMSID'
        label='Name'
        control={control}
        errors={errors}
        data={customers}
        isLoading={isLoadingCustomers}
        refreshHandler={handleRefreshCustomers}
        fetchOptions={handleCustomerSearch}
        mapOptions={mapCustomerToAntdOptions}
      />

      {watchedCustomer && (
        <>
          <div className='text-sm text-gray-500 mb-2'>Customer Information</div>
          <div className='space-y-2'>
            <div className='text-sm'>
              <span className='font-medium'>Address:</span>{' '}
              {watchedCustomer.addressLine1}
            </div>
            {watchedAddress2 && (
              <div className='text-sm'>
                <span className='font-medium'>Address Line 2:</span>{' '}
                {watchedAddress2}
              </div>
            )}
            <div className='text-sm'>
              <span className='font-medium'>City:</span> {watchedCustomer.city}
            </div>
            <div className='text-sm'>
              <span className='font-medium'>State:</span>{' '}
              {watchedCustomer.state}
            </div>
            <div className='text-sm'>
              <span className='font-medium'>Zip Code:</span>{' '}
              {watchedCustomer.zipCode}
            </div>
          </div>

          <div className='mt-4'>
            <LoadBuildingTextInput
              name='customer.refNumber'
              label='Ref #/ BOL'
              placeholder='LD12345'
            />

            <LoadBuildingTextInput
              name='poNums'
              label='PO #'
              placeholder='123456'
            />
          </div>
        </>
      )}
    </>
  );
}
