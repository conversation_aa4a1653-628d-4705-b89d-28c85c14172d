export const fetchFreightTransportTypeOptions = [
  { value: 'AC', label: 'AC - Auto Carrier (DAT)' },
  { value: 'CN', label: 'CN - Conestoga (DAT)' },
  { value: 'C', label: 'C - Container (DAT)' },
  { value: 'CR', label: 'CR - Container Refrigerated (DAT)' },
  { value: 'CI', label: 'CI - Container, Insulated (DAT)' },
  { value: 'CV', label: 'CV - Conveyor (DAT)' },
  { value: 'DD', label: 'DD - Double Drop (DAT)' },
  { value: 'DDT', label: 'DDT - Drop Deck Trailers' },
  { value: 'LA', label: 'LA - Drop Deck, Landoll (DAT)' },
  { value: 'DT', label: 'DT - Dump Trailer (DAT)' },
  { value: 'FR', label: 'FR - Flat/Van/Reefer (DAT)' },
  { value: 'F', label: 'F - Flatbed (DAT)' },
  { value: 'FA', label: 'FA - Flatbed Airride (DAT)' },
  { value: 'BT', label: 'BT - Flatbed B-Train (DAT)' },
  { value: 'FN', label: 'FN - Flatbed Conestoga (DAT)' },
  { value: 'FZ', label: 'FZ - Flatbed Hazmat (DAT)' },
  { value: 'FH', label: 'FH - Flatbed Hotshot (DAT)' },
  { value: 'MX', label: 'MX - Flatbed Maxi (DAT)' },
  { value: 'FD', label: 'FD - Flatbed or Step Deck (DAT)' },
  { value: 'FO', label: 'FO - Flatbed Over-dimension' },
  { value: 'FM', label: 'FM - Flatbed w/ Team (DAT)' },
  { value: 'FT', label: 'FT - Flatbed w/Tarps (DAT)' },
  { value: 'FC', label: 'FC - Flatbed with Chains (DAT)' },
  { value: 'LOG', label: 'LOG - Forestry Trailers' },
  { value: 'HB', label: 'HB - Hopper Bottom (DAT)' },
  { value: 'IM10', label: "IM10 - IMDL Container 10'" },
  { value: 'IM20', label: "IM20 - IMDL Container 20'" },
  { value: 'IM40', label: "IM40 - IMDL Container 40'" },
  { value: 'IM45', label: "IM45 - IMDL Container 45'" },
  { value: 'IM48', label: "IM48 - IMDL Container 48'" },
  { value: 'IM53', label: "IM53 - IMDL Container 53'" },
  { value: 'IR', label: 'IR - Insulated Van or Reefer (DAT)' },
  { value: 'LB', label: 'LB - Lowboy (DAT)' },
  { value: 'LR', label: 'LR - Lowboy or RGN (DAT)' },
  { value: 'LO', label: 'LO - Lowboy Overdimension (DAT)' },
  { value: 'MV', label: 'MV - Moving Van (DAT)' },
  { value: 'O', label: 'O - Other' },
  { value: 'NU', label: 'NU - Pneumatic (DAT)' },
  { value: 'EPO', label: 'EPO - Power Only (DAT)' },
  { value: 'R', label: 'R - Reefer (DAT)' },
  { value: 'RA', label: 'RA - Reefer Airride (DAT)' },
  { value: 'R2', label: 'R2 - Reefer Doubles (DAT)' },
  { value: 'RZ', label: 'RZ - Reefer Hazmat (DAT)' },
  { value: 'RN', label: 'RN - Reefer Intermodal (DAT)' },
  { value: 'RL', label: 'RL - Reefer Logistics (DAT)' },
  { value: 'RV', label: 'RV - Reefer or Vented Van' },
  { value: 'RP', label: 'RP - Reefer Pallet Exchange (DAT)' },
  { value: 'RM', label: 'RM - Reefer w/ Team (DAT)' },
  { value: 'RG', label: 'RG - Removable Gooseneck (DAT)' },
  { value: 'SD', label: 'SD - Step Deck (DAT)' },
  { value: 'SR', label: 'SR - Step Deck or RGN' },
  { value: 'SN', label: 'SN - Stepdeck Conestoga (DAT)' },
  { value: 'SB', label: 'SB - Straight Box Truck' },
  { value: 'SBT', label: 'SBT - Straight Box Trucks' },
  { value: 'ST', label: 'ST - Stretch Trailer (DAT)' },
  { value: 'TA', label: 'TA - Tanker Aluminum (DAT)' },
  { value: 'TN', label: 'TN - Tanker Intermodal (DAT)' },
  { value: 'TS', label: 'TS - Tanker Steel (DAT)' },
  { value: 'TT', label: 'TT - Truck and Trailer (DAT)' },
  { value: 'V', label: 'V - Van (DAT)' },
  { value: 'VA', label: 'VA - Van Airride (DAT)' },
  { value: 'VW', label: 'VW - Van Blanket Wrap' },
  { value: 'VS', label: 'VS - Van Conestoga (DAT)' },
  { value: 'VC', label: 'VC - Van Curtain (DAT)' },
  { value: 'V2', label: 'V2 - Van Double (DAT)' },
  { value: 'VZ', label: 'VZ - Van Hazmat (DAT)' },
  { value: 'VH', label: 'VH - Van Hotshot (DAT)' },
  { value: 'VI', label: 'VI - Van Insulated (DAT)' },
  { value: 'VN', label: 'VN - Van Intermodal (DAT)' },
  { value: 'VG', label: 'VG - Van Lift-Gate (DAT)' },
  { value: 'VL', label: 'VL - Van Logistics (DAT)' },
  { value: 'VLTL', label: 'VLTL - Van LTL' },
  { value: 'OT', label: 'OT - Van Opentop (DAT)' },
  { value: 'VF', label: 'VF - Van or Flatbed (DAT)' },
  { value: 'VT', label: 'VT - Van or Flats w/Tarps (DAT)' },
  { value: 'VR', label: 'VR - Van or Reefer (DAT)' },
  { value: 'VP', label: 'VP - Van Pallet Exchange (DAT)' },
  { value: 'VB', label: 'VB - Van Roller Bed (DAT)' },
  { value: 'VV', label: 'VV - Van Vented (DAT)' },
  { value: 'VM', label: 'VM - Van w/ Team (DAT)' },
];

// trident transport, not trident logistics
export const tridentTransportTypeOptions = [
  { value: '26B', label: "26'" },
  { value: '53F', label: '53ft Flatbed' },
  { value: 'AC', label: 'Auto Carrier' },
  { value: 'B', label: 'Beam Trailer' },
  { value: 'CN', label: 'Conestoga (DAT)' },
  { value: 'C', label: 'Container' },
  { value: 'CR', label: 'Container Refrigerated (DAT)' },
  { value: 'CI', label: 'Container, Insulated (DAT)' },
  { value: 'CV', label: 'Conveyor (DAT)' },
  { value: 'DD', label: 'Double Drop (DAT)' },
  { value: 'DDT', label: 'Drop Deck Trailers (DAT)' },
  { value: 'DL', label: 'Drop Deck, Landoll (DAT)' },
  { value: 'DT', label: 'Dump Trailer (DAT)' },
  { value: 'FR', label: 'Flat/Van/Reefer (DAT)' },
  { value: 'F', label: 'Flatbed (DAT)' },
  { value: 'FA', label: 'Flatbed Airride (DAT)' },
  { value: 'BT', label: 'Flatbed B-Train (DAT)' },
  { value: 'FN', label: 'Flatbed Conestoga (DAT)' },
  { value: 'FZ', label: 'Flatbed Hazmat (DAT)' },
  { value: 'FH', label: 'Flatbed Hotshot (DAT)' },
  { value: 'MX', label: 'Flatbed Maxi (DAT)' },
  { value: 'FMO', label: 'Flatbed Moffett (DAT)' },
  { value: 'FD', label: 'Flatbed or Step Deck (DAT)' },
  { value: 'FO', label: 'Flatbed Over-dimension (DAT)' },
  { value: 'FTE', label: 'Flatbed Tanker Endorsed (DAT)' },
  { value: 'FM', label: 'Flatbed w/ Team (DAT)' },
  { value: 'FT', label: 'Flatbed w/Tarps (DAT)' },
  { value: 'FC', label: 'Flatbed with Chains (DAT)' },
  { value: 'LOG', label: 'Forestry Trailers' },
  { value: 'HB', label: 'Hopper Bottom (DAT)' },
  { value: 'IR', label: 'Insulated Van or Reefer (DAT)' },
  { value: 'LB', label: 'Lowboy (DAT)' },
  { value: 'LR', label: 'Lowboy or RGN (DAT)' },
  { value: 'LO', label: 'Lowboy Overdimension (DAT)' },
  { value: 'MV', label: 'Moving Van (DAT)' },
  { value: 'O', label: 'Other' },
  { value: 'NU', label: 'Pneumatic (DAT)' },
  { value: 'PO', label: 'Power Only (DAT)' },
  { value: 'R', label: 'Reefer (DAT)' },
  { value: 'RA', label: 'Reefer Airride (DAT)' },
  { value: 'R2', label: 'Reefer Doubles (DAT)' },
  { value: 'RZ', label: 'Reefer Hazmat (DAT)' },
  { value: 'RI', label: 'Reefer Intermodal (DAT)' },
  { value: 'RN', label: 'Reefer Logistics (DAT)' },
  { value: 'RV', label: 'Reefer or Vented Van (DAT)' },
  { value: 'RP', label: 'Reefer Pallet Exchange (DAT)' },
  { value: 'RTE', label: 'Reefer Tanker Endorsed (DAT)' },
  { value: 'RTEH', label: 'Reefer Tanker Endorsed Hazmat (DAT)' },
  { value: 'RM', label: 'Reefer w/ Team (DAT)' },
  { value: 'RG', label: 'Removable Gooseneck (DAT)' },
  { value: 'SPR', label: 'Sprinter' },
  { value: 'SD', label: 'Step Deck (DAT)' },
  { value: 'SR', label: 'Step Deck or RGN (DAT)' },
  { value: 'SN', label: 'Stepdeck Conestoga (DAT)' },
  { value: 'SB', label: 'Straight Box Truck' },
  { value: 'SBT', label: 'Straight Box Trucks' },
  { value: 'ST', label: 'Stretch Trailer (DAT)' },
  { value: 'TA', label: 'Tanker Aluminum (DAT)' },
  { value: 'TN', label: 'Tanker Intermodal (DAT)' },
  { value: 'TS', label: 'Tanker Steel (DAT)' },
  { value: 'TT', label: 'Truck and Trailer (DAT)' },
  { value: 'V', label: 'Van (DAT)' },
  { value: 'VA', label: 'Van Airride (DAT)' },
  { value: 'VW', label: 'Van Blanket Wrap' },
  { value: 'VS', label: 'Van Conestoga (DAT)' },
  { value: 'VC', label: 'Van Curtain (DAT)' },
  { value: 'V2', label: 'Van Double (DAT)' },
  { value: 'VZ', label: 'Van Hazmat (DAT)' },
  { value: 'VH', label: 'Van Hotshot (DAT)' },
  { value: 'VI', label: 'Van Insulated (DAT)' },
  { value: 'VN', label: 'Van Intermodal (DAT)' },
  { value: 'VG', label: 'Van Lift-Gate (DAT)' },
  { value: 'VL', label: 'Van Logistics (DAT)' },
  { value: 'OT', label: 'Van Opentop (DAT)' },
  { value: 'VF', label: 'Van or Flatbed (DAT)' },
  { value: 'VT', label: 'Van or Flats w/Tarps (DAT)' },
  { value: 'VR', label: 'Van or Reefer (DAT)' },
  { value: 'VP', label: 'Van Pallet Exchange (DAT)' },
  { value: 'VB', label: 'Van Roller Bed (DAT)' },
  { value: 'VTE', label: 'Van Tanker Endorsed (DAT)' },
  { value: 'VTEH', label: 'Van Tanker Endorsed Hazmat (DAT)' },
  { value: 'VV', label: 'Van Vented (DAT)' },
  { value: 'VM', label: 'Van w/ Team (DAT)' },
];

// Unfortunately Mcleod API doesn't enumerate revenue codes, so we have to hardcode them
// To do so, log into their Mcleod app -> Dispatch -> Customer Service -> Order entry ->
// Click Add (but do NOT actually add a record unless in dev) -> Click on magnifying glass next to Revenue Code ->
// Screenshot all of the rows -> Click "Abort" -->
// Ask LLM generate TS list. More info here https://www.notion.so/drumkitai/McLeod-1052b16b087a80098bdfe6b44934ca6b?pvs=4

// trident transport, not trident logistics
export const tridentRevenueCodes = [
  { value: 'BRADE', label: 'Bradenton' },
  { value: 'CHATT', label: 'Chattanooga' },
  { value: 'DRAY', label: 'Drayage' },
  { value: 'ENT', label: 'Enterprise' },
  { value: 'MINNE', label: 'Minneapolis' },
  { value: 'TAMPA', label: 'Tampa' },
  { value: 'CHSC', label: 'Charleston' },
];

export const fetchRevenueCodes = [
  { value: 'FETCH', label: 'Fetch Freight' },
  { value: 'SALES', label: 'Sales Team' },
];

export const fetchCommodityOptions = [
  {
    code: 'N-BUTYL',
    description: '2-Propenoic Acid / N-Butyl Acrylate',
    hazmat: 'N',
    un: 'UN2348',
  },
  { code: 'VTRAILER', description: "53' Van Trailer", hazmat: 'N', un: '' },
  { code: 'DRUMS', description: '55gal Poly Drum', hazmat: 'N', un: '' },
  {
    code: 'CHAIRS',
    description: 'ACMA Chairs in Racking',
    hazmat: 'N',
    un: '',
  },
  { code: 'ACRYLICAC', description: 'Acrylic acid', hazmat: 'N', un: 'UN2218' },
  { code: 'AERATOR', description: 'AERATOR', hazmat: 'N', un: '' },
  { code: 'AEROSOLS', description: 'Aerosol Cans', hazmat: 'N', un: 'UN1950' },
  {
    code: 'AGCHEM',
    description: 'Agricultural Chemicals Hazardous',
    hazmat: 'N',
    un: 'UN3266',
  },
  {
    code: 'ACHEMNONH',
    description: 'Agricultural Chemicals Non-Hazardous',
    hazmat: 'N',
    un: '',
  },
  { code: 'AG', description: 'Agricultural Products', hazmat: 'N', un: '' },
  { code: 'ALCOHOL', description: 'Alcohol Products', hazmat: 'N', un: '' },
  {
    code: 'ALCOHOLS',
    description: 'ALCOHOLS, N.O.S',
    hazmat: 'N',
    un: 'UN1987',
  },
  {
    code: 'ALKYLSULF',
    description: 'Alkyl sulfonic acids',
    hazmat: 'N',
    un: 'UN2586',
  },
  {
    code: 'ALUDOCK',
    description: 'ALuminum Dock Walkway',
    hazmat: 'N',
    un: '',
  },
  { code: 'ALUMPAN', description: 'aluminum panels', hazmat: 'N', un: '' },
  { code: 'ALURIMS', description: 'Aluminum Rims', hazmat: 'N', un: '' },
  {
    code: 'ALS70',
    description: 'Ammonium Lauryl Sulfate',
    hazmat: 'N',
    un: '',
  },
  { code: 'TURF', description: 'Artificial Turf', hazmat: 'N', un: '' },
  { code: 'AUTOPARTS', description: 'Auto Parts', hazmat: 'N', un: '' },
  { code: 'CHARCOAL', description: 'Bags of Charcoal', hazmat: 'N', un: '' },
  { code: 'BALEALUM', description: 'Baled Aluminum', hazmat: 'N', un: '' },
  { code: 'BATHTUB', description: 'BATHTUBS', hazmat: 'N', un: '' },
  { code: 'BATTERIES', description: 'BATTERIES', hazmat: 'N', un: '' },
  {
    code: 'BATTWET',
    description: 'Batteries - wet filled with acid',
    hazmat: 'N',
    un: 'UN2794',
  },
  { code: 'BATTPART', description: 'Battery Parts', hazmat: 'N', un: '' },
  {
    code: 'BATCAR',
    description: 'battery powered vehicle',
    hazmat: 'N',
    un: 'UN3171',
  },
  { code: 'BEDDING', description: 'bedding products', hazmat: 'N', un: '' },
  { code: 'BEVERAGES', description: 'Beverages', hazmat: 'N', un: '' },
  { code: 'BLEACHER', description: 'bleacher parts', hazmat: 'N', un: '' },
  {
    code: 'METALS',
    description: 'Boxed/Baled Non Ferrous Metals',
    hazmat: 'N',
    un: '',
  },
  { code: 'STRUCTURE', description: 'Building Structure', hazmat: 'N', un: '' },
  { code: 'BUILDSUP', description: 'building supplies', hazmat: 'N', un: '' },
  { code: 'CABLE', description: 'Cable Reels', hazmat: 'N', un: '' },
  {
    code: 'CALCAR',
    description: 'Calcium Carbide 4.3 / PG1',
    hazmat: 'N',
    un: 'UN1402',
  },
  { code: 'CAR', description: 'car tires', hazmat: 'N', un: '' },
  { code: 'TIRES', description: 'car tires', hazmat: 'N', un: '' },
  { code: 'CARPET', description: 'Carpet', hazmat: 'N', un: '' },
  { code: 'CARPETTIL', description: 'Carpet Tile', hazmat: 'N', un: '' },
  { code: 'CHEMICALS', description: 'chemicals', hazmat: 'N', un: 'UN2924' },
  {
    code: 'HAZUN1268',
    description: 'Chemicals (liquid)',
    hazmat: 'N',
    un: 'UN1268',
  },
  { code: 'UN1751', description: 'class 8 chemical', hazmat: 'N', un: '' },
  { code: 'APPAREL', description: 'clothing', hazmat: 'N', un: '' },
  { code: 'COAL', description: 'COAL', hazmat: 'N', un: '' },
  { code: 'COALTAR', description: 'Coal Tar Pitch', hazmat: 'N', un: '3077' },
  { code: 'COMPAIR', description: 'Compressed Air', hazmat: 'N', un: 'UN1956' },
  { code: 'CONCRECBAR', description: 'Concrete Barriers', hazmat: 'N', un: '' },
  { code: 'CONTAINER', description: 'CONTAINERS', hazmat: 'N', un: '' },
  { code: 'COPIER', description: 'copiers', hazmat: 'N', un: '' },
  {
    code: 'CORROSIVE',
    description: 'CORROSIVE LIQUID, BASIC, INORGANIC, N.O.S',
    hazmat: 'N',
    un: 'UN3266',
  },
  {
    code: 'HAZCOR',
    description: 'Corrosive liquids, flammable, n.o.s.',
    hazmat: 'N',
    un: 'UN2920',
  },
  { code: 'CRANE', description: 'CRANE', hazmat: 'N', un: '' },
  { code: 'CRANERAIL', description: 'crane rails', hazmat: 'N', un: '' },
  { code: 'DAIRY', description: 'Dairy Food Products', hazmat: 'N', un: '' },
  {
    code: 'DISINFECT',
    description: 'Didecyldimethylammonium chloride',
    hazmat: 'N',
    un: 'UN1903',
  },
  { code: 'DOCKEQUIP', description: 'Dock Equipment', hazmat: 'N', un: '' },
  { code: 'DOGVEST', description: 'Dog Vests', hazmat: 'N', un: '' },
  { code: 'SPREADER', description: 'DROP SPREADER', hazmat: 'N', un: '' },
  { code: 'DRYFOOD', description: 'Dry Food Goods', hazmat: 'N', un: '' },
  { code: 'DRYGOODS', description: 'Dry Nonfood Goods', hazmat: 'N', un: '' },
  { code: 'DRYWALL', description: 'Dry Wall', hazmat: 'N', un: '' },
  { code: 'DUMPSTER', description: 'Dumpster', hazmat: 'N', un: '' },
  { code: 'DYE', description: 'dye material in gaylord', hazmat: 'N', un: '' },
  {
    code: 'UN2801',
    description: 'Dyes, liquid, corrosive, N.O.S.(Glycolic acid)',
    hazmat: 'N',
    un: 'UN2801',
  },
  { code: 'EGGS', description: 'Eggs and Egg Products', hazmat: 'N', un: '' },
  {
    code: 'BIKES',
    description: 'electric bicycles or scooters, no batteries',
    hazmat: 'N',
    un: '',
  },
  { code: 'ELECTRON', description: 'Electronic Items', hazmat: 'N', un: '' },
  { code: 'EMPTYBAG', description: 'Empty bags to fill', hazmat: 'N', un: '' },
  {
    code: 'EMPTYBATT',
    description: 'empty plastic battery containers',
    hazmat: 'N',
    un: '',
  },
  { code: 'ENCLOS', description: 'Enclosures', hazmat: 'N', un: '' },
  {
    code: 'ENVHAZ',
    description: 'Environmentally Haz Substance',
    hazmat: 'N',
    un: 'UN3077',
  },
  { code: 'EQUIPMENT', description: 'equipment', hazmat: 'N', un: '' },
  {
    code: 'EROSION',
    description: 'Erosion Control Products',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'ETHANOL',
    description: 'Ethanol or Ethyl alcohol',
    hazmat: 'N',
    un: 'UN1170',
  },
  { code: 'EXCAVATOR', description: 'EXCAVATOR', hazmat: 'N', un: '' },
  { code: 'FAB', description: 'fabricated materials', hazmat: 'N', un: '' },
  { code: 'FAKFURNIT', description: 'FAK Furniture', hazmat: 'N', un: '' },
  {
    code: 'FAKFLOOR',
    description: 'FAK Furniture - Floor Loaded',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'FIREEXTG',
    description: 'Fire Extinguishers',
    hazmat: 'N',
    un: 'UN1044',
  },
  { code: 'FISHEQP', description: 'Fishing Equipment', hazmat: 'N', un: '' },
  { code: 'FITTINGS', description: 'Fittings', hazmat: 'N', un: '' },
  {
    code: 'FLALIQUID',
    description: 'FLAMMABLE LIQUIDS, N.O.S. (CONTAINS METHANOL)',
    hazmat: 'N',
    un: 'UN1993',
  },
  { code: 'CARROTS', description: 'Floor loaded carrots', hazmat: 'N', un: '' },
  {
    code: 'FLFURN',
    description: 'Floor Loaded Furniture',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'MATTRESS',
    description: 'Floor loaded mattresses',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'SPRINGS',
    description: 'FLOOR LOADED METAL SPRINGS',
    hazmat: 'N',
    un: '',
  },
  { code: 'FLOORING', description: 'Flooring', hazmat: 'N', un: '' },
  { code: 'FOAMTRAYS', description: 'Foam Trays', hazmat: 'N', un: '' },
  { code: 'FORKLIFT', description: 'Forklift', hazmat: 'N', un: '' },
  { code: 'FAK', description: 'Freight All Kinds', hazmat: 'N', un: '' },
  { code: 'BEEF', description: 'Fresh Beef', hazmat: 'N', un: '' },
  { code: 'FRESHFOOD', description: 'Fresh Food Items', hazmat: 'N', un: '' },
  {
    code: 'BAKING',
    description: 'Frozen Baking / Yeast Products',
    hazmat: 'N',
    un: '',
  },
  { code: 'FOFBRE', description: 'Frozen Bread', hazmat: 'N', un: '' },
  { code: 'FRZFOOD', description: 'Frozen Food', hazmat: 'N', un: '' },
  {
    code: 'FROZENFOO',
    description: 'Frozen Food Ships at -10',
    hazmat: 'N',
    un: '',
  },
  { code: 'ICECREAM', description: 'Frozen Ice Cream', hazmat: 'N', un: '' },
  {
    code: 'HAZGLUT',
    description: 'GLUT; Corrosive Liquids, toxic, n.o.s.',
    hazmat: 'N',
    un: 'UN2922',
  },
  { code: 'GOLFCART', description: 'Golfcarts', hazmat: 'N', un: '' },
  { code: 'GREENROLL', description: 'GREENROLLERS', hazmat: 'N', un: '' },
  { code: 'SPRAYER', description: 'greens sprayer', hazmat: 'N', un: '' },
  {
    code: 'HAZCHEM32',
    description: 'Hazardous chems',
    hazmat: 'N',
    un: 'UN3267',
  },
  {
    code: 'HAZGAS',
    description: 'Hazardous Gasses',
    hazmat: 'N',
    un: 'UN1077',
  },
  {
    code: 'HAZFLAME',
    description: 'Hazardous Liquids - Flammable',
    hazmat: 'N',
    un: 'UN1993',
  },
  { code: 'HAZCHEM', description: 'Hazardous Product', hazmat: 'N', un: '' },
  {
    code: 'HAZREC',
    description: 'Hazardous Reclosers',
    hazmat: 'N',
    un: 'UN3091',
  },
  {
    code: 'SUR',
    description: 'Hazmat Chemicals (Surfactant)',
    hazmat: 'N',
    un: 'UN2586',
  },
  {
    code: 'RESIN',
    description: 'Hazmat Resin Solution',
    hazmat: 'N',
    un: 'UN1866',
  },
  {
    code: 'AMINE2735',
    description: 'Hazmat UN2735',
    hazmat: 'N',
    un: 'UN2735',
  },
  { code: 'UNIOUAT', description: 'HAZQUAT', hazmat: 'N', un: 'UN2280' },
  {
    code: 'HVAC',
    description: 'Heating and Air Conditioning Items',
    hazmat: 'N',
    un: '',
  },
  { code: 'HOPS', description: 'HOPS', hazmat: 'N', un: '' },
  {
    code: 'HOTRUSH',
    description: 'Hot Rush Industrial Supplies',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'HYCA',
    description: 'Hydrochloric Acid Solution',
    hazmat: 'N',
    un: 'UN1789',
  },
  {
    code: 'INDUSTRIA',
    description: 'Industrial Supplies',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'IBC',
    description: 'Intermediate Bulk Container',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'HAZALC',
    description: 'Isopropanol or Isopropyl alcohol',
    hazmat: 'N',
    un: 'UN1219',
  },
  { code: 'LOADER', description: 'John Deere 844L', hazmat: 'N', un: '' },
  { code: 'LABTRIAL', description: 'labtrial', hazmat: 'N', un: '' },
  { code: 'LADD', description: 'LADDERS', hazmat: 'N', un: '' },
  { code: 'LADDERS', description: 'Ladders', hazmat: 'N', un: '' },
  { code: 'LANDTIE', description: 'Landscape Ties', hazmat: 'N', un: '' },
  { code: 'LAWN', description: 'LAWN EQUIPMENT', hazmat: 'N', un: '' },
  {
    code: 'LIGHTING',
    description: 'LIGHTS AND LIGHTING SUPPLIES',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'LABSA',
    description: 'Linear alkyl benzene sulphonic acid',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'LABSAID',
    description: 'Linear alkyl benzene sulphonic acid',
    hazmat: 'N',
    un: 'UN2586',
  },
  {
    code: 'UN3082',
    description: 'Liquid, n.o.s. (Diethanolamine) 9,III, Diethanolamine, NS 67',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'HAZBATT',
    description: 'Lithium Batteries',
    hazmat: 'N',
    un: 'UN3480',
  },
  {
    code: 'LITHBAT',
    description: 'Lithium-Ion Batteries',
    hazmat: 'N',
    un: 'UN3480',
  },
  { code: 'LOCKERS', description: 'LOCKERS', hazmat: 'N', un: '' },
  { code: 'LUMBER', description: 'Lumber Products', hazmat: 'N', un: '' },
  { code: 'MACHINERY', description: 'Machinery', hazmat: 'N', un: '' },
  { code: 'HMMAN', description: 'Mandrel', hazmat: 'N', un: '' },
  { code: 'METAL', description: 'METAL', hazmat: 'N', un: '' },
  { code: 'BUILDING', description: 'metal building', hazmat: 'N', un: '' },
  { code: 'TANK', description: 'Metal Tank', hazmat: 'N', un: '' },
  { code: 'TRASHCANS', description: 'METAL TRASH CANS', hazmat: 'N', un: '' },
  { code: 'METALTRAY', description: 'Metal Trays', hazmat: 'N', un: '' },
  { code: 'METALSDR', description: 'Metals in Drums', hazmat: 'N', un: '' },
  { code: 'MINPROD', description: 'Mineral Products', hazmat: 'N', un: '' },
  {
    code: 'MISCEMBED',
    description: 'Misc Embeds Industrial Supplies',
    hazmat: 'N',
    un: '',
  },
  { code: 'CLASS9', description: 'Misc Haz', hazmat: 'N', un: 'UN3295' },
  {
    code: 'MISC',
    description: 'Misc Industrial Supplies',
    hazmat: 'N',
    un: '',
  },
  { code: 'UN1750', description: 'monoCHLOROACETIC ACID', hazmat: 'N', un: '' },
  { code: 'MOWER', description: 'mower', hazmat: 'N', un: '' },
  { code: 'MULCH', description: 'Mulch', hazmat: 'N', un: '' },
  {
    code: 'NONHAZLC',
    description: 'NON - Hazardous Liquid Chemicals',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'NONSPBATT',
    description: 'Non Spillable Batteries',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'NON-HAZ',
    description: 'NON-Hazardous Chemicals',
    hazmat: 'N',
    un: '',
  },
  { code: 'NHBATT', description: 'Non-Hazmat Batteries', hazmat: 'N', un: '' },
  { code: 'FABRIC', description: 'Nonwoven Fabrics', hazmat: 'N', un: '' },
  { code: 'OFFICEEQU', description: 'Office Equipment', hazmat: 'N', un: '' },
  { code: 'ONIONS', description: 'Onions', hazmat: 'N', un: '' },
  {
    code: 'PACKAGING',
    description: 'Packaging and Dunnage',
    hazmat: 'N',
    un: '',
  },
  { code: 'PACKSUPP', description: 'Packaging Supplies', hazmat: 'N', un: '' },
  {
    code: 'POLYMER',
    description: 'Palletized Polymer Product',
    hazmat: 'N',
    un: '',
  },
  { code: 'PLIFT', description: 'Pallet Lift', hazmat: 'N', un: '' },
  {
    code: 'APPLIANCE',
    description: 'Palletized Appliances',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'PALART',
    description: 'Palletized Art Supplies',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'FEED',
    description: 'Palletized Bagged Livestock Feed',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'GROUT',
    description: 'Palletized Bags of Grout',
    hazmat: 'N',
    un: '',
  },
  { code: 'BEARINGS', description: 'Palletized Bearings', hazmat: 'N', un: '' },
  {
    code: 'CANDLES',
    description: 'Palletized Candle Products',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'COFFEE',
    description: 'Palletized Coffee Products',
    hazmat: 'N',
    un: '',
  },
  { code: 'PALDRUMS', description: 'Palletized Drums', hazmat: 'N', un: '' },
  { code: 'PENV', description: 'Palletized Envelopes', hazmat: 'N', un: '' },
  {
    code: 'FASTNERS',
    description: 'Palletized Fasteners',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'STARTERLO',
    description: 'Palletized Fire Starter Logs',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'PFLOORING',
    description: 'Palletized Flooring',
    hazmat: 'N',
    un: '',
  },
  { code: 'PFOOD', description: 'Palletized Food', hazmat: 'N', un: '' },
  { code: 'PFURN', description: 'Palletized Furniture', hazmat: 'N', un: '' },
  { code: 'ICEMELT', description: 'Palletized Ice Melt', hazmat: 'N', un: '' },
  {
    code: 'REEFER',
    description: 'Palletized Machinery',
    hazmat: 'N',
    un: 'UN2857',
  },
  { code: 'MALT', description: 'Palletized Malt', hazmat: 'N', un: '' },
  {
    code: 'PLASTICPE',
    description: 'Palletized Plastic Pellets',
    hazmat: 'N',
    un: '',
  },
  { code: 'PSU', description: 'palletized server units', hazmat: 'N', un: '' },
  {
    code: 'FLOORSUP',
    description: 'palletized subfloor mix',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'TIRELUBE',
    description: 'Palletized Tire Lubricant',
    hazmat: 'N',
    un: '',
  },
  { code: 'PALLETS', description: 'Pallets', hazmat: 'N', un: '' },
  {
    code: 'CUPSNLIDS',
    description: 'Paper Cups and Lids',
    hazmat: 'N',
    un: '',
  },
  { code: 'PAPER', description: 'Paper Products', hazmat: 'N', un: '' },
  { code: 'PARTITION', description: 'partitions', hazmat: 'N', un: '' },
  {
    code: 'PAVEREP',
    description: 'Pavement Repair Product',
    hazmat: 'N',
    un: '',
  },
  { code: 'PAVER', description: 'PAVER', hazmat: 'N', un: '' },
  {
    code: 'PHOSPEN',
    description: 'Phosphorus pentoxide',
    hazmat: 'N',
    un: 'UN1807',
  },
  {
    code: 'UN1831',
    description: 'Phosphorus Pentoxide',
    hazmat: 'N',
    un: 'UN1831',
  },
  { code: 'BARK', description: 'Pine Bark Nuggets', hazmat: 'N', un: '' },
  { code: 'PIPE', description: 'Pipe', hazmat: 'N', un: '' },
  { code: 'BOTTLE', description: 'plastic bottles', hazmat: 'N', un: '' },
  { code: 'PLABOT', description: 'Plastic Bottles/Caps', hazmat: 'N', un: '' },
  { code: 'AGCOIL', description: 'Plastic Coil', hazmat: 'N', un: '' },
  { code: 'CRATES', description: 'Plastic Crates', hazmat: 'N', un: '' },
  { code: 'PLASTICS', description: 'Plastics', hazmat: 'N', un: '' },
  { code: 'PLUMBING', description: 'plumbing supplies', hazmat: 'N', un: '' },
  { code: 'POULTRY', description: 'Poultry Products', hazmat: 'N', un: '' },
  { code: 'PWRSUPP', description: 'Power Supplies', hazmat: 'N', un: '' },
  { code: 'TOOLS', description: 'power tools', hazmat: 'N', un: '' },
  { code: 'PRODUCE', description: 'Produce', hazmat: 'N', un: '' },
  { code: 'PROACID', description: 'Propionic Acid', hazmat: 'N', un: 'UN3463' },
  { code: 'PUMP', description: 'Pump and Related Parts', hazmat: 'N', un: '' },
  { code: 'RAILCAR', description: 'Rail Car', hazmat: 'N', un: '' },
  { code: 'WHEELSET', description: 'Railcar Wheel Sets', hazmat: 'N', un: '' },
  { code: 'RAILTIES', description: 'Railroad Ties', hazmat: 'N', un: '' },
  { code: 'REELGRIND', description: 'REELGRINDER', hazmat: 'N', un: '' },
  {
    code: 'RPG',
    description: 'Refrigerated Packaged Goods',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'RESONNHAZ',
    description: 'Resin class 55 non haz',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'UN1866',
    description: 'Resin solution, flammable',
    hazmat: 'N',
    un: '',
  },
  { code: 'ROLSTCK', description: 'Roll Stock', hazmat: 'N', un: '' },
  { code: 'RFRAMES', description: 'Roof Frames', hazmat: 'N', un: '' },
  { code: 'SCAFFOLD', description: 'scaffolding', hazmat: 'N', un: '' },
  { code: 'SHEETMETA', description: 'sheet metal', hazmat: 'N', un: '' },
  { code: 'SHELVING', description: 'SHELVING', hazmat: 'N', un: '' },
  { code: 'PLASTIC', description: 'Shredded Plastic', hazmat: 'N', un: '' },
  { code: 'SCOILS', description: 'Skidded Coils', hazmat: 'N', un: '' },
  { code: 'SLINKCOI', description: 'Slinky Coils', hazmat: 'N', un: '' },
  {
    code: 'HAZMCHEM2',
    description: 'Sodium chloroacetate',
    hazmat: 'N',
    un: 'UN2659',
  },
  {
    code: 'UN1384',
    description: 'Sodium dithionite',
    hazmat: 'N',
    un: 'UN1384',
  },
  {
    code: 'UN2949',
    description: 'Sodium hydrosulfide',
    hazmat: 'N',
    un: 'UN2949',
  },
  {
    code: 'SODHYD',
    description: 'SODIUM HYDROXIDE',
    hazmat: 'N',
    un: 'UN1824',
  },
  { code: 'SOAD', description: 'Soil Additive', hazmat: 'N', un: '' },
  { code: 'SPORTS', description: 'Sporting Equipment', hazmat: 'N', un: '' },
  { code: 'STARCH', description: 'Starch', hazmat: 'N', un: '' },
  { code: 'SCREENS', description: 'STATIC SCREENS', hazmat: 'N', un: '' },
  { code: 'STATBAT', description: 'Stationary Batteries', hazmat: 'N', un: '' },
  {
    code: 'BRIDGE',
    description: 'Stay-In-Place Bridge plates',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'STEELBATT',
    description: 'Steel Cased Batteries',
    hazmat: 'N',
    un: '',
  },
  { code: 'COILS', description: 'Steel Coils', hazmat: 'N', un: '' },
  {
    code: 'STCONT',
    description: 'Steel Containers 24×96"x5\'',
    hazmat: 'N',
    un: '',
  },
  { code: 'DIES', description: 'steel dies', hazmat: 'N', un: '' },
  { code: 'STEELMES', description: 'steel mesh', hazmat: 'N', un: '' },
  { code: 'PLATES', description: 'Steel Plates', hazmat: 'N', un: '' },
  { code: 'STEELPOLE', description: 'Steel Poles', hazmat: 'N', un: '' },
  { code: 'STEEL', description: 'Steel Products', hazmat: 'N', un: '' },
  { code: 'REBAR', description: 'Steel Rebar', hazmat: 'N', un: '' },
  { code: 'STANKS', description: 'Steel Tanks', hazmat: 'N', un: '' },
  { code: 'STEELT', description: 'Steel Ties', hazmat: 'N', un: '' },
  { code: 'STEELTUBE', description: 'steel tubing', hazmat: 'N', un: '' },
  {
    code: 'STOREFIX',
    description: 'Store Shelving and Fixtures',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'STRUCTURA',
    description: 'Structural Steel Sequence',
    hazmat: 'N',
    un: '',
  },
  { code: 'SULFACID', description: 'Sulfamic Acid', hazmat: 'N', un: 'UN2967' },
  { code: 'SUPERSACK', description: 'Super Sacks', hazmat: 'N', un: '' },
  { code: 'SWITCH', description: 'Switch Phases', hazmat: 'N', un: '' },
  {
    code: 'SYNRFUND',
    description: 'Synthetic Roof Underlayment',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'TEXTILES',
    description: 'Textiles and Apparel',
    hazmat: 'N',
    un: '',
  },
  { code: 'TILT', description: 'Tilt Angels', hazmat: 'N', un: '' },
  {
    code: 'TOTES',
    description: 'Totes of Liquid Animal Feed Supplement',
    hazmat: 'N',
    un: '',
  },
  { code: 'TOWER', description: 'Tower Stand', hazmat: 'N', un: '' },
  { code: 'TRACTOR', description: 'TRACTOR', hazmat: 'N', un: '' },
  { code: 'TRAILERS', description: 'TRAILERS', hazmat: 'N', un: '' },
  { code: 'TRNSFRMR', description: 'Transformers', hazmat: 'N', un: '' },
  {
    code: 'TREXCODC',
    description: 'Trex - composite decking',
    hazmat: 'N',
    un: '',
  },
  { code: 'TRUCK', description: 'TRUCK', hazmat: 'N', un: '' },
  { code: 'TRUCKB', description: 'Truck Bodies', hazmat: 'N', un: '' },
  { code: 'PROCORE64', description: 'Turf Cutter', hazmat: 'N', un: '' },
  {
    code: 'HAZLQ',
    description: 'UN1760 Hazmat Liq',
    hazmat: 'N',
    un: 'UN1760',
  },
  { code: 'HAZCHEM1', description: 'UN2659', hazmat: 'N', un: '' },
  { code: 'HAZCHEM2', description: 'UN2967(sulfamic)', hazmat: 'N', un: '' },
  { code: 'RESIDUE', description: 'unwashed IBC totes', hazmat: 'N', un: '' },
  { code: 'VACUUM', description: 'VACUUM', hazmat: 'N', un: '' },
  { code: 'FOAM', description: 'variety of foams', hazmat: 'N', un: '' },
  { code: 'VEHICLE', description: 'Vehicle', hazmat: 'N', un: '' },
  {
    code: 'ACCESSORI',
    description: 'washroom accessories',
    hazmat: 'N',
    un: '',
  },
  { code: 'WATER', description: 'Water Bottles', hazmat: 'N', un: '' },
  {
    code: 'CHILLER',
    description: 'Water Chiller on Skids',
    hazmat: 'N',
    un: 'UN2857',
  },
  { code: 'WATTANK', description: 'Water Tank', hazmat: 'N', un: '' },
  { code: 'WETCELL', description: 'Wet Cell Batteries', hazmat: 'N', un: '' },
  { code: 'WINDOW', description: 'Window Materials', hazmat: 'N', un: '' },
  { code: 'MESH', description: 'Wire Mesh', hazmat: 'N', un: '' },
  { code: 'WIRE', description: 'Wire Products', hazmat: 'N', un: '' },
  {
    code: 'SUBFLOOR',
    description: 'wrapped/palletized bags',
    hazmat: 'N',
    un: '',
  },
  { code: 'YARN', description: 'yarn', hazmat: 'N', un: '' },
  { code: 'ZIMATE', description: 'Zimate Powder', hazmat: 'N', un: '' },
];

// trident transport, not trident logistics
export const tridentCommodityOptions = [
  { code: 'UTILITY', description: 'Utility Trailer', hazmat: 'N', un: '' },
  { code: 'UTVPARTSN', description: 'UTV Parts New', hazmat: 'N', un: '' },
  { code: 'UVPIPELIN', description: 'UV PIPE LINERS', hazmat: 'N', un: '' },
  { code: 'VEHICLES', description: 'vehicles', hazmat: 'N', un: '' },
  {
    code: 'WSTEMGMT',
    description: 'Waste Management Sysytems',
    hazmat: 'N',
    un: '',
  },
  { code: 'WAT', description: 'WATER', hazmat: 'N', un: '' },
  { code: 'H2OTRUCK', description: 'Water Truck', hazmat: 'N', un: '' },
  { code: 'WATRMELN', description: 'Watermelons', hazmat: 'N', un: '' },
  { code: 'WHEELS', description: 'Wheels', hazmat: 'N', un: '' },
  { code: 'WINDOW', description: 'Windows', hazmat: 'N', un: '' },
  { code: 'WIREMESH', description: 'WIRE MESH', hazmat: 'N', un: '' },
  { code: 'REELS', description: 'Wire reels', hazmat: 'N', un: '' },
  { code: 'PELLETS', description: 'WOOD PELLETS', hazmat: 'N', un: '' },
  { code: 'WOODWALL', description: 'wooden wall panels', hazmat: 'N', un: '' },
  { code: 'YARDGOAT', description: 'Yard Goat', hazmat: 'N', un: '' },
  { code: 'YARN', description: 'Yarn', hazmat: 'N', un: '' },
  { code: 'SUGAR', description: 'SUGAR', hazmat: 'N', un: '' },
  {
    code: 'GEOFILL',
    description: 'Supersacks of Geofill',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'SUPSACKS',
    description: 'Supersacks of Raw Material',
    hazmat: 'N',
    un: '',
  },
  { code: 'SUPPLEMEN', description: 'supplements', hazmat: 'N', un: '' },
  { code: 'TANK', description: 'Tank', hazmat: 'N', un: '' },
  { code: 'TEA', description: 'tea/sugar', hazmat: 'N', un: '' },
  { code: 'TH', description: 'Telehandler', hazmat: 'N', un: '' },
  {
    code: 'CHILLED',
    description: 'Temp Controlled Foods',
    hazmat: 'N',
    un: '',
  },
  { code: 'PPE', description: 'TENNIS', hazmat: 'N', un: '' },
  { code: 'TENT', description: 'Tent equipment', hazmat: 'N', un: '' },
  { code: 'THANKSGIV', description: 'Thanksgiving', hazmat: 'N', un: '' },
  {
    code: 'RESIN',
    description: 'Thermoplastic Resin (Non-Hazmat)',
    hazmat: 'N',
    un: '',
  },
  { code: 'TILE', description: 'TILE', hazmat: 'N', un: '' },
  { code: 'TIRES', description: 'Tires', hazmat: 'N', un: '' },
  { code: 'TISSUE', description: 'TISSUE PAPER', hazmat: 'N', un: '' },
  { code: 'TOPSOIL', description: 'Topsoil', hazmat: 'N', un: '' },
  {
    code: 'TOTHAZ',
    description: 'Totes of HAZARDOUS liquid',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'BINDER',
    description: 'Totes of non hazardous binder',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'GLUE',
    description: 'Totes of Non-Hazmat Glue',
    hazmat: 'N',
    un: '',
  },
  { code: 'SOAP', description: 'Totes of Soap', hazmat: 'N', un: '' },
  { code: 'TOWAWAYT', description: 'tow away tanker', hazmat: 'N', un: '' },
  { code: 'TOWAWAYVA', description: 'Tow Away Van', hazmat: 'N', un: '' },
  { code: 'TOYS', description: 'TOYS & GAMES', hazmat: 'N', un: '' },
  { code: 'TRACT', description: 'Tractors', hazmat: 'N', un: '' },
  { code: 'TRLFRAM', description: 'Trailer Frames', hazmat: 'N', un: '' },
  { code: 'TRASHBAGS', description: 'TRASH BAGS', hazmat: 'N', un: '' },
  { code: 'TRAY', description: 'Trayliners', hazmat: 'N', un: '' },
  { code: 'TROPHIE', description: 'Trophies', hazmat: 'N', un: '' },
  { code: 'TTM', description: 'TTM', hazmat: 'N', un: '' },
  { code: 'TURFEQUIP', description: 'Turf Equipment', hazmat: 'N', un: '' },
  { code: 'UBOX', description: 'U-HAUL U-BOX', hazmat: 'N', un: '' },
  { code: 'MAT', description: 'Used Mattresses', hazmat: 'N', un: '' },
  {
    code: 'FOILPAN',
    description: 'Utility Foil Pan w/Lid',
    hazmat: 'N',
    un: '',
  },
  { code: 'ROOFING', description: 'Roof & Insulation', hazmat: 'N', un: '' },
  { code: 'RUBBER', description: 'Rubber', hazmat: 'N', un: '' },
  { code: 'SAND', description: 'Sand', hazmat: 'N', un: '' },
  { code: 'SCAFFOLD', description: 'Scaffolding', hazmat: 'N', un: '' },
  { code: 'SEED', description: 'SEED', hazmat: 'N', un: '' },
  { code: 'TRUCK', description: 'Service Truck', hazmat: 'N', un: '' },
  {
    code: 'CONTAINER',
    description: 'Sheet Steel Containers',
    hazmat: 'N',
    un: '',
  },
  { code: 'SHEETMTL', description: 'SHEETMETAL PARTS', hazmat: 'N', un: '' },
  { code: 'SHEL', description: 'SHELVING', hazmat: 'N', un: '' },
  { code: 'SHINGLES', description: 'SHINGLES', hazmat: 'N', un: '' },
  { code: 'SHOCK', description: 'Shockpad', hazmat: 'N', un: '' },
  { code: 'S/C', description: 'Shopping Carts', hazmat: 'N', un: '' },
  { code: 'SHORT', description: 'Shortening', hazmat: 'N', un: '' },
  {
    code: 'DOORASSEM',
    description: 'SLIDING DOOR ASSEMBLIES',
    hazmat: 'N',
    un: '',
  },
  { code: 'SP', description: 'SMOKELESS POWDER', hazmat: 'N', un: '' },
  { code: 'SNACKFOOD', description: 'SNACK FOODS', hazmat: 'N', un: '' },
  { code: 'SNACKS', description: 'Snack Foods', hazmat: 'N', un: '' },
  { code: 'SOLAR', description: 'Solar', hazmat: 'N', un: '' },
  { code: 'SOLVENT', description: 'SOLVENT', hazmat: 'N', un: 'UN1993' },
  {
    code: 'SO',
    description: 'SOUTHERN HOSPITALITY TRAILER',
    hazmat: 'N',
    un: '',
  },
  { code: 'SPONGES', description: 'SPONGES', hazmat: 'N', un: '' },
  { code: 'SPRTEQPMT', description: 'Sports Equipment', hazmat: 'N', un: '' },
  { code: 'SPRAY', description: 'Sprayer Tanks', hazmat: 'N', un: '' },
  {
    code: 'FLATSTACK',
    description: 'Stack of Flatbeds - Straps/Chains/Binders Required',
    hazmat: 'N',
    un: '',
  },
  { code: 'STAGEE', description: 'Staging Equipment', hazmat: 'N', un: '' },
  { code: 'STEEL', description: 'Steel', hazmat: 'N', un: '' },
  { code: 'STEELCAGE', description: 'Steel Cages', hazmat: 'N', un: '' },
  { code: 'STEELCOIL', description: 'Steel Coil', hazmat: 'N', un: '' },
  { code: 'STEELDUMP', description: 'Steel Dumpsters', hazmat: 'N', un: '' },
  { code: 'SF', description: 'STEEL FORGINGS', hazmat: 'N', un: '' },
  { code: 'STONE', description: 'Stone', hazmat: 'N', un: '' },
  { code: 'STORCONT', description: 'Storage Containers', hazmat: 'N', un: '' },
  { code: 'STUDS', description: 'STUDS', hazmat: 'N', un: '' },
  { code: 'P', description: 'Pump', hazmat: 'N', un: '' },
  {
    code: '##',
    description: 'Quartz Slabs on A Frames / palletized tiles',
    hazmat: 'N',
    un: '',
  },
  { code: 'RACK', description: 'RACK', hazmat: 'N', un: '' },
  { code: 'RAILCAR', description: 'Rail Car', hazmat: 'N', un: '' },
  { code: 'RAILPARTS', description: 'RAILROAD PARTS', hazmat: 'N', un: '' },
  { code: 'REBAR', description: 'Rebar', hazmat: 'N', un: '' },
  { code: 'RECYLED', description: 'Recycled Goods', hazmat: 'N', un: '' },
  { code: 'REEFER10', description: 'Reefer -10', hazmat: 'N', un: '' },
  {
    code: 'REEF2432',
    description: 'REEFER 24 - 32 DEGREES',
    hazmat: 'N',
    un: '',
  },
  { code: 'REEFER26', description: 'Reefer 26 Degrees', hazmat: 'N', un: '' },
  { code: 'REEFER28', description: 'Reefer 28', hazmat: 'N', un: '' },
  { code: 'REEF3868', description: 'Reefer 38 -68', hazmat: 'N', un: '' },
  { code: 'REEFER38', description: 'Reefer 38 degrees', hazmat: 'N', un: '' },
  { code: 'REEFER42', description: 'Reefer 42', hazmat: 'N', un: '' },
  { code: 'REEFER60', description: 'Reefer 60', hazmat: 'N', un: '' },
  { code: 'REEFER65', description: 'REEFER 65 DEGREES', hazmat: 'N', un: '' },
  { code: 'REEFER54', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER55', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEF5060', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEF5565', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER32', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER33', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER34', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER35', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  { code: 'REEFER36', description: 'Refridgerated Foods', hazmat: 'N', un: '' },
  {
    code: 'REFGROCER',
    description: 'Refrigerated Grocery',
    hazmat: 'N',
    un: '',
  },
  { code: 'RESEQP', description: 'Resturant Equipment', hazmat: 'N', un: '' },
  { code: 'RICE', description: 'RICE', hazmat: 'N', un: '' },
  { code: 'ROBOT', description: 'ROBOTIC EQUIPMENT', hazmat: 'N', un: '' },
  { code: 'BELT', description: 'role', hazmat: 'N', un: '' },
  { code: 'PAD', description: 'Rolled Foam Padding', hazmat: 'N', un: '' },
  { code: 'ROLLPAPER', description: 'Rolls of Paper', hazmat: 'N', un: '' },
  { code: 'ROLLTURF', description: 'Rolls of Turf', hazmat: 'N', un: '' },
  { code: 'PALLETS', description: 'Pallets', hazmat: 'N', un: '' },
  { code: 'PARPRPROD', description: 'Paper Products', hazmat: 'N', un: '' },
  { code: 'PT', description: 'Paper Towel', hazmat: 'N', un: '' },
  { code: 'PASTA', description: 'PASTA', hazmat: 'N', un: '' },
  { code: 'PERMIT', description: 'PERMITS', hazmat: 'N', un: '' },
  { code: 'PETFILM', description: 'Pet Film', hazmat: 'N', un: '' },
  { code: 'PETFOOD', description: 'PET FOOD', hazmat: 'N', un: '' },
  { code: 'PETTREATS', description: 'PET TREATS', hazmat: 'N', un: '' },
  {
    code: 'PETROLEUM',
    description: 'PETROLEUM lubricants',
    hazmat: 'N',
    un: '',
  },
  { code: 'PHARM', description: 'Pharmaceuticals', hazmat: 'N', un: '' },
  { code: 'PIGGUP', description: 'Pig Guppy Trailer', hazmat: 'N', un: '' },
  { code: 'PIPE', description: 'Pipe', hazmat: 'N', un: '' },
  { code: 'PISTACHIO', description: 'Pistachios', hazmat: 'N', un: '' },
  { code: 'CUTLERY', description: 'Plastic Cutlery', hazmat: 'N', un: '' },
  { code: 'PLASTICPA', description: 'PLASTIC PANELS', hazmat: 'N', un: '' },
  { code: 'TUBING', description: 'Plastic Tubing', hazmat: 'N', un: '' },
  { code: 'PLASTICS', description: 'Plastics', hazmat: 'N', un: '' },
  { code: 'PLATES', description: 'PLATES', hazmat: 'N', un: '' },
  { code: 'PLAY', description: 'Play structure', hazmat: 'N', un: '' },
  { code: 'CARDS', description: 'PLAYING CARDS', hazmat: 'N', un: '' },
  { code: 'POL', description: 'Poles', hazmat: 'N', un: '' },
  { code: 'POOLPRO', description: 'POOL PRODUCTS', hazmat: 'N', un: '' },
  { code: 'PO', description: 'pools', hazmat: 'N', un: '' },
  { code: 'POPCORN', description: 'Popcorn', hazmat: 'N', un: '' },
  { code: '1.3C', description: 'Powder, Smokeless', hazmat: 'Y', un: '' },
  { code: 'PRECAST', description: 'PreCast Concrete', hazmat: 'N', un: '' },
  {
    code: 'PS',
    description: 'Prefabricated Steel Products',
    hazmat: 'N',
    un: '',
  },
  { code: 'PRELOAD', description: 'PRELOADED TRAILERS', hazmat: 'N', un: '' },
  { code: 'PRINTER', description: 'printer', hazmat: 'N', un: '' },
  { code: 'PROD', description: 'Produce', hazmat: 'N', un: '' },
  { code: 'PRODUCE', description: 'Produce', hazmat: 'N', un: '' },
  {
    code: 'PRODEQUIP',
    description: 'Production Equipment',
    hazmat: 'N',
    un: '',
  },
  { code: 'PROMO', description: 'Promotional Products', hazmat: 'N', un: '' },
  { code: 'MACHINERY', description: 'Machinery', hazmat: 'N', un: '' },
  { code: 'MAGGIE', description: 'maggie', hazmat: 'N', un: '' },
  { code: 'MARSH', description: 'MARSHMELLOWS', hazmat: 'N', un: '' },
  { code: 'MED', description: 'Medical Equip', hazmat: 'N', un: '' },
  { code: 'METLPROD', description: 'Metal Products', hazmat: 'N', un: '' },
  { code: 'MILITARY', description: 'Military MISC', hazmat: 'N', un: '' },
  { code: 'MISC', description: 'Miscellaneous', hazmat: 'N', un: '' },
  { code: 'NUTS', description: 'Mixed Nuts', hazmat: 'N', un: '' },
  { code: 'MHOME', description: 'Mobile Homes', hazmat: 'N', un: '' },
  {
    code: 'MOVEMANU',
    description: 'Motor Vehicle Parts Manufacturing',
    hazmat: 'N',
    un: '',
  },
  { code: 'MOTORS', description: 'motors', hazmat: 'N', un: '' },
  { code: 'MULCH', description: 'MULCH', hazmat: 'N', un: '' },
  {
    code: 'NONALBEV',
    description: 'NON-ALCOHOLIC BEVERAGES',
    hazmat: 'N',
    un: '',
  },
  { code: 'BATTNOHAZ', description: 'NON-HAZ BATTERIES', hazmat: 'N', un: '' },
  { code: 'PLANTS', description: 'Nursery Plants', hazmat: 'N', un: '' },
  { code: 'OFFICESUP', description: 'OFFICE SUPPLIES', hazmat: 'N', un: '' },
  { code: 'OLIVE/OEIL', description: 'OLIVE OIL', hazmat: 'N', un: '' },
  { code: 'OJ', description: 'ORANGE JUICE', hazmat: 'N', un: '' },
  { code: 'ORANGES', description: 'Oranges', hazmat: 'N', un: '' },
  { code: 'PACKAGING', description: 'Packaging Material', hazmat: 'N', un: '' },
  {
    code: 'PAINT',
    description: 'paint related material,',
    hazmat: 'N',
    un: 'UN1263',
  },
  { code: 'POOLHS', description: 'PALLETISED HOSE', hazmat: 'N', un: '' },
  { code: 'PA', description: 'Palletized Axles', hazmat: 'N', un: '' },
  { code: 'PB', description: 'PALLETIZED BRICK', hazmat: 'N', un: '' },
  {
    code: 'COCOCOIR',
    description: 'Palletized Coco Coir',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'PALFLUM',
    description: 'Palletized Finished Lumber',
    hazmat: 'N',
    un: '',
  },
  { code: 'FLOORING', description: 'Palletized Flooring', hazmat: 'N', un: '' },
  { code: 'PF', description: 'Palletized Footwear', hazmat: 'N', un: '' },
  { code: 'PALCA', description: 'palletized metal cages', hazmat: 'N', un: '' },
  { code: 'DOGFOOD', description: 'palletized pet food', hazmat: 'N', un: '' },
  { code: 'RUGS', description: 'Palletized Rugs', hazmat: 'N', un: '' },
  {
    code: 'PALLSOLAR',
    description: 'Palletized Solar Equipment',
    hazmat: 'N',
    un: '',
  },
  { code: 'PALSTO', description: 'PALLETIZED STONE', hazmat: 'N', un: '' },
  { code: 'GD', description: 'GARAGE DOORS', hazmat: 'N', un: '' },
  { code: 'SOIL', description: 'GARDEN SOIL', hazmat: 'N', un: '' },
  { code: 'GENERAL', description: 'General Freight', hazmat: 'N', un: '' },
  { code: 'LIFT', description: 'Genie S -65 XC', hazmat: 'N', un: '' },
  { code: 'GLA', description: 'Glass', hazmat: 'N', un: '' },
  { code: 'GLASS', description: 'Glass Beads', hazmat: 'N', un: '' },
  { code: 'GC', description: 'Golf Carts', hazmat: 'N', un: '' },
  { code: 'GRAVEL', description: 'Gravel', hazmat: 'N', un: '' },
  { code: 'PARTS', description: 'Grinding Rings', hazmat: 'N', un: '' },
  {
    code: 'CREAMERS',
    description: 'half and half / heavy whipping cream',
    hazmat: 'N',
    un: '',
  },
  { code: 'HARDWARE', description: 'HARDWARE', hazmat: 'N', un: '' },
  { code: 'CAR', description: 'Hauling Car(s)', hazmat: 'N', un: '' },
  { code: 'EQUI', description: 'Heavy Equipment', hazmat: 'N', un: '' },
  { code: 'HERBICIDE', description: 'HERBICIDE', hazmat: 'N', un: 'UN2922' },
  { code: 'HIPSRG', description: 'HIPS Regrind', hazmat: 'N', un: '' },
  { code: 'TOWAWAY', description: 'Hook and Drop', hazmat: 'N', un: '' },
  { code: 'HOTTUBS', description: 'Hot Tubs', hazmat: 'N', un: '' },
  { code: 'HV', description: 'HVAC UNIT', hazmat: 'N', un: '' },
  { code: 'HYDRO', description: 'Hydrochill', hazmat: 'N', un: '' },
  { code: 'HYGIENE', description: 'Hygiene Products', hazmat: 'N', un: '' },
  { code: 'ICECREAM', description: 'ICE CREAM -20', hazmat: 'N', un: '' },
  { code: 'INFL', description: 'INFILL', hazmat: 'N', un: '' },
  {
    code: 'INSULATIO',
    description: 'Insulation Material',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'LAWNGARD',
    description: 'LAWN & GARDEN PRODUCTS',
    hazmat: 'N',
    un: '',
  },
  { code: 'LIGHTERF', description: 'LIGHTER FLUID', hazmat: 'N', un: 'UN1993' },
  { code: 'LIGHTERS', description: 'LIGHTERS', hazmat: 'N', un: 'UN1057' },
  { code: 'LIGHTING', description: 'Lighting', hazmat: 'N', un: '' },
  { code: 'LINERS', description: 'liners', hazmat: 'N', un: '' },
  {
    code: 'LIQUIDWHO',
    description: 'Liquidation Wholesale',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'LOADOUT',
    description: 'LOADOUT TRAILER - CAN USE',
    hazmat: 'N',
    un: '',
  },
  { code: 'LTL', description: 'LTL Default', hazmat: 'N', un: '' },
  { code: 'LUMBER', description: 'Lumber', hazmat: 'N', un: '' },
  {
    code: 'ATTACHMEN',
    description: 'Machine Attachments',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'TOWTRALRS',
    description: 'EMPTY TRAILER HOOK AND DROP',
    hazmat: 'N',
    un: '',
  },
  { code: 'EWT', description: 'EMPTY WATER TRUCK', hazmat: 'N', un: '' },
  { code: 'ENG', description: 'ENGINES', hazmat: 'N', un: '' },
  {
    code: 'ENTER',
    description: 'ENTERTAINMENT EQUIPMENT',
    hazmat: 'N',
    un: '',
  },
  { code: 'EXHIBITS', description: 'Exhibits', hazmat: 'N', un: '' },
  { code: 'FABRIC', description: 'Fabric', hazmat: 'N', un: '' },
  { code: 'FERT', description: 'Fertilizer', hazmat: 'N', un: '' },
  { code: 'FILTRAT', description: 'Filtration System', hazmat: 'N', un: '' },
  {
    code: 'FIRESAF',
    description: 'FIRST SAFETY PRODUCTS',
    hazmat: 'N',
    un: '',
  },
  { code: 'FLEX', description: 'FLEXI FLOATS', hazmat: 'N', un: '' },
  { code: 'FLOODBAR', description: 'Flood Barrier', hazmat: 'N', un: '' },
  { code: 'FLOORMATS', description: 'FLOOR MATS', hazmat: 'N', un: '' },
  { code: 'FLOOR', description: 'Flooring', hazmat: 'N', un: '' },
  {
    code: 'FLOWBIN',
    description: 'Flow Bins - Straps Only',
    hazmat: 'N',
    un: '',
  },
  { code: 'FOAM', description: 'Foam', hazmat: 'N', un: '' },
  { code: 'FP', description: 'Foil Pack', hazmat: 'N', un: '' },
  {
    code: 'FOOD',
    description: 'Food Disturbution or Wholesale',
    hazmat: 'N',
    un: '',
  },
  { code: 'F1', description: 'Ford Truck', hazmat: 'N', un: '' },
  { code: 'FORKLFT', description: 'Forklift', hazmat: 'N', un: '' },
  { code: 'FT', description: 'FRAC TANK', hazmat: 'N', un: '' },
  { code: 'FAK', description: 'Freight of All Kinds', hazmat: 'N', un: '' },
  { code: 'FRSHCHKN', description: 'Fresh Chicken', hazmat: 'N', un: '' },
  { code: 'FRZCHICKE', description: 'FROZEN CHICKEN', hazmat: 'N', un: '' },
  { code: 'FRZNCHKN', description: 'Frozen Chicken', hazmat: 'N', un: '' },
  { code: 'FROZEN28', description: 'FROZEN FOODS', hazmat: 'N', un: '' },
  { code: 'FROZEN', description: 'FROZEN FOODS', hazmat: 'N', un: '' },
  { code: 'FROZEN5', description: 'FROZEN FOODS (-5)', hazmat: 'N', un: '' },
  { code: 'FROZEN10', description: 'FROZEN FOODS -10', hazmat: 'N', un: '' },
  { code: 'FROZEN18', description: 'FROZEN FOODS -18', hazmat: 'N', un: '' },
  { code: 'FROZEN0', description: 'FROZEN FOODS 0', hazmat: 'N', un: '' },
  { code: 'FRZGROCER', description: 'Frozen Grocery', hazmat: 'N', un: '' },
  {
    code: 'FRZPROD',
    description: 'Frozen Produce/ Grocery',
    hazmat: 'N',
    un: '',
  },
  { code: 'FURNITURE', description: 'Furniture', hazmat: 'N', un: '' },
  { code: 'CCM', description: 'CONSTRUCTION MATERIALS', hazmat: 'N', un: '' },
  { code: 'CM', description: 'CONSTRUCTION MATERIALS', hazmat: 'N', un: '' },
  { code: 'CPG', description: 'CONSUMER PACKAGED GOODS', hazmat: 'N', un: '' },
  { code: 'CONVEYOR', description: 'CONVEYOR', hazmat: 'N', un: '' },
  { code: 'COOKOIL', description: 'COOKING OIL', hazmat: 'N', un: '' },
  { code: 'COOLING', description: 'Cooling Parts', hazmat: 'N', un: '' },
  { code: 'COPPER', description: 'Copper', hazmat: 'N', un: '' },
  { code: 'CORK', description: 'CORK', hazmat: 'N', un: '' },
  { code: 'COUPLERS', description: 'Couplers & Knuckles', hazmat: 'N', un: '' },
  { code: 'CRACKERS', description: 'CRACKERS', hazmat: 'N', un: '' },
  { code: 'CRANEMATS', description: 'Crane Mats', hazmat: 'N', un: '' },
  { code: 'MARBLE', description: 'CRATED MARBLE', hazmat: 'N', un: '' },
  { code: 'CRA', description: 'Crates', hazmat: 'N', un: '' },
  { code: 'DAIRY', description: 'DAIRY PRODUCTS', hazmat: 'N', un: '' },
  { code: 'DETERGENT', description: 'Detergent', hazmat: 'N', un: '' },
  { code: 'DIRT', description: 'Dirt/Organics', hazmat: 'N', un: '' },
  { code: 'DISRELIEF', description: 'Disaster Relief', hazmat: 'N', un: '' },
  { code: 'DISPLAY', description: 'display equipment', hazmat: 'N', un: '' },
  { code: 'D', description: 'DOORS', hazmat: 'N', un: '' },
  { code: 'MUD', description: 'Drilling Mud', hazmat: 'N', un: '' },
  { code: 'GROCERY', description: 'Dry Food Goods', hazmat: 'N', un: '' },
  { code: 'DRYGROCER', description: 'Dry Grocery', hazmat: 'N', un: '' },
  { code: 'DRYVAN', description: 'DRY VAN TRAILER', hazmat: 'N', un: '' },
  { code: 'DUNNAGE', description: 'Dunnage', hazmat: 'N', un: '' },
  { code: 'DUSTCO', description: 'DUST COVERS', hazmat: 'N', un: '' },
  { code: 'TEST', description: 'EDI Test Commodity', hazmat: 'N', un: '' },
  { code: 'EGGS', description: 'EGGS', hazmat: 'N', un: '' },
  {
    code: 'ELECCOMP',
    description: 'Electrical Components',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'ELECWHOL',
    description: 'Electronics Wholesale',
    hazmat: 'N',
    un: '',
  },
  { code: 'MTDRUMS', description: 'EMPTY DRUMS', hazmat: 'N', un: 'UN1090' },
  {
    code: 'EMPPROP',
    description: 'Empty Propane Cylinders',
    hazmat: 'N',
    un: '',
  },
  { code: 'EMPTYTANK', description: 'Empty Tanker', hazmat: 'N', un: '' },
  { code: 'EMPTOTES', description: 'Empty Totes', hazmat: 'N', un: '' },
  { code: 'BO', description: 'Boats', hazmat: 'N', un: '' },
  { code: 'TUBES', description: 'BOILER TUBING', hazmat: 'N', un: '' },
  { code: 'WATER', description: 'Bottled Water', hazmat: 'N', un: '' },
  { code: 'BOX', description: 'box', hazmat: 'N', un: '' },
  { code: 'BREAD', description: 'BREAD', hazmat: 'N', un: '' },
  { code: 'B', description: 'Brick/Stone', hazmat: 'N', un: '' },
  { code: 'BRICK', description: 'Bricks', hazmat: 'N', un: '' },
  { code: 'BM', description: 'Building Materials', hazmat: 'N', un: '' },
  { code: 'CABELTRAY', description: 'CABEL TRAY', hazmat: 'N', un: '' },
  { code: 'CABINETS', description: 'Cabinets', hazmat: 'N', un: '' },
  { code: 'CABLE', description: 'Cable Reels', hazmat: 'N', un: '' },
  { code: 'CANDLE', description: 'CANDLES', hazmat: 'N', un: '' },
  { code: 'CANDY', description: 'CANDY', hazmat: 'N', un: '' },
  { code: 'CANFOOD', description: 'CANNED FOODS', hazmat: 'N', un: '' },
  { code: 'CARPART', description: 'Car Hood', hazmat: 'N', un: '' },
  { code: 'CARWASH', description: 'CAR WASH MATERIALS', hazmat: 'N', un: '' },
  { code: 'CARPET', description: 'Carpet', hazmat: 'N', un: '' },
  { code: 'CHEESE', description: 'Cheese', hazmat: 'N', un: '' },
  { code: 'CHEMICALS', description: 'Chemicals', hazmat: 'N', un: '' },
  { code: 'CHIPS', description: 'Chips', hazmat: 'N', un: '' },
  {
    code: 'CRAN/CHER',
    description: 'Choc Cov Cran or Cherries',
    hazmat: 'N',
    un: '',
  },
  { code: 'CHOCOLATE', description: 'Chocolate', hazmat: 'N', un: '' },
  { code: 'XMASTREES', description: 'Christmas trees', hazmat: 'N', un: '' },
  { code: 'CLEANPR', description: 'CLEANING PRODUCTS', hazmat: 'N', un: '' },
  { code: 'CLOTHING', description: 'Clothing', hazmat: 'N', un: '' },
  { code: 'COCOIL', description: 'Coconut Oil', hazmat: 'N', un: '' },
  { code: 'COFFEE', description: 'Coffee', hazmat: 'N', un: '' },
  { code: 'COMPP', description: 'COMPACTOR & PARTS', hazmat: 'N', un: '' },
  { code: 'COMPEQIP', description: 'Computer Equipment', hazmat: 'N', un: '' },
  { code: 'CONCRETE', description: 'Concrete', hazmat: 'N', un: '' },
  { code: 'CC', description: 'Conex Container', hazmat: 'N', un: '' },
  {
    code: 'CONEQUIP',
    description: 'Construction Equipment',
    hazmat: 'N',
    un: '',
  },
  {
    code: 'CONSTRUCT',
    description: 'CONSTRUCTION MATERIAL',
    hazmat: 'N',
    un: '',
  },
  { code: '-', description: '-', hazmat: 'N', un: '' },
  { code: '20CONTAIN', description: "20' CONTAINER", hazmat: 'N', un: '' },
  { code: 'LO', description: '53 Load out', hazmat: 'N', un: '' },
  { code: 'DRUM', description: '55 Gallon Drum', hazmat: 'N', un: '' },
  { code: 'AFRAME', description: 'A Frames', hazmat: 'N', un: '' },
  { code: 'AC', description: 'AC Units', hazmat: 'N', un: '' },
  { code: 'ADDITIVE', description: 'Additive', hazmat: 'N', un: '' },
  { code: 'ADHESIVE', description: 'Adhesive', hazmat: 'N', un: '' },
  { code: 'AEROARPL', description: 'Aerospace/ Aircraft', hazmat: 'N', un: '' },
  { code: 'AGRI', description: 'AGRICULTURE PARTS', hazmat: 'N', un: '' },
  {
    code: 'PREHEATER',
    description: 'Air preheater baskets',
    hazmat: 'N',
    un: '',
  },
  { code: 'SKIDS', description: 'AIR PRESSURE VESSELS', hazmat: 'N', un: '' },
  { code: 'ALUMIN', description: 'aluminum', hazmat: 'N', un: '' },
  { code: 'APPLES', description: 'apples', hazmat: 'N', un: '' },
  { code: 'APPL', description: 'Appliances', hazmat: 'N', un: '' },
  {
    code: 'HAZCLEAN',
    description: 'ARMAKLEEN - WATER BASED CLEANERS',
    hazmat: 'N',
    un: 'UN1760',
  },
  { code: 'ARTTURF', description: 'Artificial Turf', hazmat: 'N', un: '' },
  { code: 'SILO', description: 'Asphalt Silo', hazmat: 'N', un: '' },
  { code: 'AE', description: 'Audio Equipment', hazmat: 'N', un: '' },
  { code: 'AUGER', description: 'AUGER', hazmat: 'N', un: '' },
  { code: 'AUTOMOTI', description: 'Automotive Parts', hazmat: 'N', un: '' },
  { code: 'BAGS', description: 'BAGS', hazmat: 'N', un: '' },
  { code: 'ICEMELT', description: 'Bags of Ice Melt', hazmat: 'N', un: '' },
  { code: 'BALED', description: 'Baled Plastic', hazmat: 'N', un: '' },
  { code: 'CARDBOARD', description: 'BALES OF CARDBOARD', hazmat: 'N', un: '' },
  { code: 'BB', description: 'Ballast Blocks', hazmat: 'N', un: '' },
  { code: 'BANDSAW', description: 'BAND SAW ON PALLET', hazmat: 'N', un: '' },
  {
    code: 'BATT2794',
    description: 'BATTERIES, WET, FILLED WITH ACID',
    hazmat: 'N',
    un: 'UN2794',
  },
  { code: 'BEAMS', description: 'BEAMS', hazmat: 'N', un: '' },
  { code: 'BEDPROD', description: 'Bedding Products', hazmat: 'N', un: '' },
  { code: 'BEER', description: 'BEER', hazmat: 'N', un: '' },
  { code: 'BVGC0NT', description: 'Beverage Containers', hazmat: 'N', un: '' },
  { code: 'BEVERAGE', description: 'Beverages', hazmat: 'N', un: '' },
];

export const tridentReferenceNumberOptions = [
  { description: 'Accounts Receivable Number', code: 'AP' },
  { description: 'Bill of Lading Number', code: 'BM' },
  { description: "Buyer's Credit Memo", code: 'CM' },
  { description: "Consignee's Order Number", code: 'CG' },
  { description: 'Customer Order Number', code: 'CO' },
  { description: 'Delivery Reference', code: 'KK' },
  { description: 'Diversion Authority Number', code: 'DV' },
  { description: 'Line Quanitity', code: 'QL' },
  { description: 'Master Bill of Lading', code: 'MB' },
  { description: 'Mutally Defined', code: 'ZZ' },
  { description: 'Order Number', code: 'OR' },
  { description: 'Original Invoice Number', code: 'OI' },
  { description: 'Original Return Request Reference Number', code: 'OD' },
  { description: 'Pickset Number', code: 'PS' },
  { description: 'Pickup Number', code: 'PU' },
  { description: 'Pickup Reference Number', code: 'P8' },
  { description: 'Price List Change or Issue Number', code: 'PI' },
  { description: 'Purchase Order Number', code: 'PO' },
  { description: 'Seal Number', code: 'SN' },
  { description: "Shipper's Identifying Number (SID)", code: 'SI' },
  { description: "Shipper's Order (Invoice Number)", code: 'SO' },
];

export const fetchReferenceNumberOptions = [
  { description: 'Pickup Number', code: 'PU' },
  { description: 'Price List Change or Issue Number', code: 'PI' },
  { description: 'Price List Number', code: 'PL' },
  { description: 'Price Quote Number', code: 'PR' },
  { description: 'Product Period for which Labor Costs are Firm', code: 'LM' },
  { description: 'Purchase Order Number', code: 'PO' },
  { description: 'Release invoice number for prior bill and hold', code: 'RI' },
  { description: 'Release Number', code: 'RE' },
  { description: 'Repetitive Booking Number', code: 'RO' },
  {
    description:
      'Repetitive Waybill Code (Origin Carrier, Standard Point Location Code,',
    code: 'RW',
  },
  { description: 'Run Number', code: 'RN' },
  { description: 'Sample Number', code: 'XO' },
  { description: 'Scan Line', code: 'SP' },
  { description: "Seller's Debit Memo", code: 'DL' },
  { description: "Seller's Sale Number", code: 'SW' },
  { description: 'Serial Number', code: 'SE' },
  { description: "Shipper's Identifying Number (SID)", code: 'SI' },
  { description: "Shipper's Order (Invoice Number)", code: 'SO' },
  { description: 'Special Processing Code', code: 'SU' },
  { description: 'Standard Carrier Alpha Code (SCAC)', code: 'SCA' },
  { description: 'Store Number', code: 'ST' },
  { description: 'Tariff Supplement Number', code: 'OU' },
  { description: 'Terminal Operator Number', code: 'TO' },
  { description: 'Title XIX Identifier Number', code: 'XN' },
  { description: 'Unit Number', code: 'QQ' },
  { description: 'Vendor Order Number', code: 'VN' },
  { description: 'Ward', code: 'VM' },
  { description: 'Work Order Number', code: 'WO' },
  { description: 'Accounts Receivable Number', code: 'AP' },
  { description: "Agent's Shipment Number", code: 'AG' },
  { description: 'Appointment Number', code: 'AO' },
  { description: 'Appropriation Number', code: 'AT' },
  { description: 'Associated Invoices', code: 'AI' },
  { description: 'Authorization to Meet Competition Number', code: 'AU' },
  { description: 'Beginning Serial Number', code: 'BG' },
  { description: 'Bill of Lading Number', code: 'BM' },
  { description: 'Bin Location Number', code: 'BO' },
  {
    description:
      'Bonded Carrier Internal Revenue Service Identification Number',
    code: 'BI',
  },
  { description: 'Booking Number', code: 'BN' },
  { description: 'Broker or Sales Office Number', code: 'BR' },
  { description: "Buyer's Credit Memo", code: 'CM' },
  { description: 'Carrier Assigned Code', code: 'AAO' },
  { description: "Carrier's Reference Number (PRO/Invoice)", code: 'CN' },
  { description: 'Class of Contract Code', code: 'CE' },
  { description: 'Clear Text Clause', code: 'CU' },
  { description: 'Collocation Indicator', code: 'COL' },
  { description: 'Condition of Purchase Document Number', code: 'CP' },
  { description: 'Condition of Sale Document Number', code: 'CS' },
  { description: "Consignee's Order Number", code: 'CG' },
  { description: 'Container or Equipment Receipt Number', code: 'ER' },
  { description: 'Customer catalog number', code: 'CH' },
  { description: 'Customer Order Number', code: 'CO' },
  { description: 'Customer Reference Number', code: 'CR' },
  { description: 'Dealer Order Number', code: 'ON' },
  { description: 'Dealer purchase order number', code: 'DC' },
  { description: 'Delivery Order Number', code: 'DO' },
  {
    description:
      'Department of Defense Transportation Service Code Number (Household Goods)',
    code: 'DY',
  },
  { description: 'Dependents Information', code: 'DU' },
  { description: 'Depositor Number', code: 'DE' },
  { description: 'Discounter Registration Number', code: 'NM' },
  { description: 'Domicile Branch Number', code: 'DA' },
  { description: 'Drug Formulary Number', code: 'FO' },
  { description: 'Export Reference Number', code: 'RF' },
  { description: 'Government Bill of Lading', code: 'BL' },
  { description: 'Government Transportation Request', code: 'TR' },
  { description: 'Grain Order Reference Number', code: 'GR' },
  { description: 'Heat Code', code: 'HC' },
  { description: 'Institution Loan Number', code: 'VO' },
  { description: 'Insurance Certificate Number', code: 'ID' },
  { description: 'Job (Project) Number', code: 'JB' },
  { description: 'Lease Schedule Number - Blanket', code: 'BH' },
  { description: "Line Item Identifier (Seller's)", code: 'LI' },
  { description: 'Line Quanitity', code: 'QL' },
  { description: 'Loan Acquisition Number', code: 'GM' },
  { description: 'Major System Affected Code', code: 'QV' },
  { description: 'Mammography Certification Number', code: 'EW' },
  { description: 'Manufacturing Operation Number', code: 'MO' },
  { description: 'Master Bill of Lading', code: 'MB' },
  { description: 'Merchandise Type Code', code: 'MR' },
  { description: 'Message Address or ID', code: 'ME' },
  { description: 'Microfilm Number', code: 'MC' },
  { description: 'Mill Order Number', code: 'MI' },
  { description: 'Multiple Listing Number', code: 'JO' },
  { description: 'Multiple Listing Service Map Y Coordinate', code: 'JN' },
  { description: 'Mutally Defined', code: 'ZZ' },
  { description: 'Non pickup Limited Tariff Number', code: 'LN' },
  { description: 'Ocean Manifest', code: 'OM' },
  { description: 'Open and Prepaid Station List Number', code: 'OE' },
  { description: 'Order Number', code: 'OR' },
  { description: 'Original Invoice Number', code: 'OI' },
  { description: 'Part Number', code: 'PM' },
  {
    description:
      "Payee's Financial Institution Account Number for Check, Draft or Wire Payments",
    code: 'PY',
  },
  { description: 'Permit Number', code: 'PN' },
  { description: 'Pickset Number', code: 'PS' },
];
