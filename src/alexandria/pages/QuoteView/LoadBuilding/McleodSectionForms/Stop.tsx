import { useEffect, useRef, useState } from 'react';
import {
  <PERSON>,
  FieldErrors,
  FieldPath,
  UseFormReturn,
  useFieldArray,
} from 'react-hook-form';

import { Button } from 'components/Button';
import { Checkbox } from 'components/Checkbox';
import { ReferenceNumberCard } from 'components/ReferenceNumberCard';
import { ReferenceNumberModal } from 'components/ReferenceNumberModal';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { LoadDateTimeInput } from 'pages/LoadView/LoadInformationTab';
import { AdditionalReference, NormalizedLoad, TMSLocation } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import { TMS } from 'types/enums/Integrations';
import captureException from 'utils/captureException';
import {
  GenericCompanySearchableFields,
  locationSear<PERSON><PERSON><PERSON><PERSON>,
  mapLocationsToAntdOptions,
} from 'utils/loadInfoAndBuilding';

import { LoadBuildingTextInput } from '../McleodLoadBuildingForm';
import {
  fetchReferenceNumberOptions,
  tridentReferenceNumberOptions,
} from './constants';

export function StopForm({
  stop,
  formMethods,
  isLoadingLocations,
  locations,
  handleRefreshLocations,
  setLocations,
}: {
  stop: 'pickup' | 'consignee';
  formMethods: UseFormReturn<NormalizedLoad>;
  isLoadingLocations: boolean;
  locations: Maybe<TMSLocation[]>;
  handleRefreshLocations: () => void;
  setLocations: React.Dispatch<React.SetStateAction<Maybe<TMSLocation[]>>>;
}): JSX.Element {
  const [isEditingLocation] = useState(false);
  const [isRefNumberModalOpen, setIsRefNumberModalOpen] = useState(false);
  const [editingRefIndex, setEditingRefIndex] = useState<number | null>(null);
  const [tempRefNumber, setTempRefNumber] = useState<AdditionalReference>({
    qualifier: '',
    number: '',
    weight: 0,
    pieces: 0,
    shouldSendToDriver: false,
  });
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  // const [isCreatingLocation, setIsCreatingLocation] = useState(false); // placeholder to demo loading state
  // const { toast } = useToast();

  const { tmsIntegrations, serviceID } = useServiceFeatures();
  const tms = tmsIntegrations?.find((tms) => tms.name === TMS.McleodEnterprise);
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = formMethods;

  const { fields, append, remove, update } = useFieldArray({
    control,
    name: `${stop}.additionalReferences`,
  });

  const getQualifierCodes = () => {
    if (tms?.tenant.includes('trident')) {
      return tridentReferenceNumberOptions;
    }
    if (tms?.tenant.includes('fcfm')) {
      return fetchReferenceNumberOptions;
    }

    captureException(
      `No qualifier codes configured for Mcleod tenant ${tms?.tenant}`,
      {
        tmsID: tms?.id,
        tenant: tms?.tenant,
        serviceID,
      }
    );
    return [];
  };

  const qualifierCodes = getQualifierCodes();

  // Helper function to determine if a field is required based on tenant
  const getFieldValidation = (fieldName: string) => {
    // For FCFM, only city and state are required
    if (
      tms?.featureFlags.isOnlyCityStateRequired === true &&
      !['city', 'state'].includes(fieldName)
    ) {
      return {};
    }

    // For all other tenants (including Trident), use existing validation
    return !watchedLocationID
      ? { required: 'Required if existing ID not selected' }
      : isEditingLocation
        ? { required: 'Required' }
        : {};
  };

  // const isPickup = stop === 'pickup';
  const watchedLocationID = watch(`${stop}.externalTMSID`);
  const watchedLocObj = watch(stop);

  const handleRefNumberModalOk = () => {
    // Validate required fields
    if (!tempRefNumber.qualifier?.trim() || !tempRefNumber.number?.trim()) {
      return; // Don't proceed if required fields are missing
    }

    const newRef: AdditionalReference = { ...tempRefNumber };

    if (editingRefIndex !== null) {
      // Update existing reference
      update(editingRefIndex, newRef);
    } else {
      // Add new reference
      append(newRef);
    }

    // Clear temporary values
    setTempRefNumber({
      qualifier: '',
      number: '',
      weight: 0,
      pieces: 0,
      shouldSendToDriver: false,
    });

    setIsRefNumberModalOpen(false);
    setEditingRefIndex(null);
  };

  const handleRefNumberModalCancel = () => {
    // Clear temporary values
    setTempRefNumber({
      qualifier: '',
      number: '',
      weight: 0,
      pieces: 0,
      shouldSendToDriver: false,
    });

    setIsRefNumberModalOpen(false);
    setEditingRefIndex(null);
  };

  const handleEditReference = (index: number) => {
    const reference = fields[index] as AdditionalReference;
    // Pre-populate the modal with existing values
    setTempRefNumber({
      qualifier: reference.qualifier,
      number: reference.number,
      weight: reference.weight,
      pieces: reference.pieces,
      shouldSendToDriver: reference.shouldSendToDriver,
    });
    setEditingRefIndex(index);
    setIsRefNumberModalOpen(true);
  };

  const handleAddNewReference = () => {
    // Clear any existing temporary values
    setTempRefNumber({
      qualifier: '',
      number: '',
      weight: 0,
      pieces: 0,
      shouldSendToDriver: false,
    });
    setEditingRefIndex(null);
    setIsRefNumberModalOpen(true);
  };

  // Update the temp state when modal form changes
  const handleTempRefChange = (
    field: keyof AdditionalReference,
    value: any
  ) => {
    setTempRefNumber((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  {
    /* TODO: Finalize manaul location creation UI/UX */
  }
  // const handleCreateLocation = async () => {
  //   const isValid = await validateNewLocation(watchedLocObj);
  //   if (!isValid) {
  //     return;
  //   }

  //   setIsCreatingLocation(true);
  //   const res = await createLocation({
  //     tmsID: tmsIntegrations[0]?.id,
  //     location: {
  //       ...(getValues(`${stop}`) as TMSLocation),
  //       isShipper: isPickup,
  //       isConsignee: !isPickup,
  //     },
  //   });

  //   if (res.isOk()) {
  //     setIsEditingLocation(false);
  //     toast({
  //       description: `Location ${res.value.location.externalTMSID} created in McLeod`,
  //       variant: 'success',
  //     });
  //     setLocations((prevLocations) =>
  //       injectSelectedObject(res.value.location, prevLocations ?? [])
  //     );
  //     for (const [key, value] of Object.entries(res.value.location)) {
  //       setValue(`${stop}.${key}` as FieldPath<NormalizedLoad>, value, {
  //         shouldDirty: true,
  //       });
  //     }
  //   } else {
  //     toast({
  //       description: res.error.message,
  //       variant: 'destructive',
  //     });
  //   }

  //   setIsCreatingLocation(false);
  // };

  // const handleCancelEditingLocation = () => {
  //   setValue(`${stop}.externalTMSID`, '');
  //   setValue(`${stop}.name`, '');
  //   setValue(`${stop}.addressLine1`, '');
  //   setValue(`${stop}.addressLine2`, '');
  //   setValue(`${stop}.city`, '');
  //   setValue(`${stop}.state`, '');
  //   setValue(`${stop}.zipCode`, '');
  //   setValue(`${stop}.apptRequired`, false);

  //   clearErrors(`${stop}`);
  //   setIsEditingLocation(false);
  // };

  // const handleEditlocation = () => {
  //   setIsEditingLocation(true);
  //   setValue(`${stop}.externalTMSID`, '');
  // };

  const handleLocationSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    return locationSearchHandler({
      tmsID: tmsIntegrations?.[0]?.id,
      locations,
      setLocations,
      field,
      value,
    });
  };
  // const validateNewLocation = async (
  //   data: NormalizedPickup | NormalizedDropoff
  // ): Promise<boolean> => {
  //   const tmsLocationKeys: Array<keyof TMSLocation> = [
  //     'name',
  //     'addressLine1',
  //     'addressLine2',
  //     'city',
  //     'state',
  //     'zipCode',
  //     'apptRequired',
  //   ];

  //   // array.forEach does not handle async/await properly
  //   const results = await Promise.all(
  //     tmsLocationKeys.map(async (key) => {
  //       if (key in data) {
  //         const valid = await trigger(
  //           `${stop}.${key}` as FieldPath<NormalizedLoad>
  //         );
  //         return valid;
  //       }
  //       return true; // If the key is not in data, consider it valid.
  //     })
  //   );

  //   const isSectionValid = results.every((result) => result);
  //   return isSectionValid;
  // };

  useEffect(() => {
    if (watchedLocationID) {
      const selectedLoc = locations?.find(
        (loc) => loc.externalTMSID === watchedLocationID
      );
      if (!selectedLoc) {
        return;
      }

      Object.entries(selectedLoc).forEach(([key, value]) => {
        setValue(`${stop}.${key}` as FieldPath<NormalizedLoad>, value, {
          shouldDirty: true, // this ensures the AI-label is handled correctly
        });
      });
    }
  }, [watchedLocationID]);

  useEffect(() => {
    if (watchedLocationID) {
      // Remove ID if address is write-in
      const selectedLoc = locations?.find(
        (loc) => loc.externalTMSID === watchedLocationID
      );
      if (!selectedLoc) {
        return;
      }

      if (
        (selectedLoc?.name &&
          watchedLocObj?.name.toLowerCase() !==
            selectedLoc?.name.toLowerCase()) ||
        (selectedLoc?.addressLine1 &&
          watchedLocObj?.addressLine1.toLowerCase() !==
            selectedLoc?.addressLine1.toLowerCase()) ||
        (selectedLoc?.addressLine2 &&
          watchedLocObj?.addressLine2?.toLowerCase() !==
            selectedLoc?.addressLine2?.toLowerCase()) ||
        (selectedLoc?.city &&
          watchedLocObj?.city?.toLowerCase() !==
            selectedLoc?.city?.toLowerCase()) ||
        (selectedLoc?.state &&
          watchedLocObj?.state?.toLowerCase() !==
            selectedLoc?.state?.toLowerCase()) ||
        (selectedLoc?.zipCode &&
          watchedLocObj?.zipCode?.toLowerCase() !==
            selectedLoc?.zipCode?.toLowerCase())
      ) {
        setValue(`${stop}.externalTMSID` as FieldPath<NormalizedLoad>, '');
      }
    }
  }, [
    watchedLocationID,
    watchedLocObj?.name,
    watchedLocObj?.addressLine1,
    watchedLocObj?.addressLine2,
    watchedLocObj?.city,
    watchedLocObj?.state,
    watchedLocObj?.zipCode,
  ]);

  // Reset scroll position when going from multiple cards to single card
  useEffect(() => {
    if (fields.length === 1 && scrollContainerRef.current) {
      scrollContainerRef.current.scrollLeft = 0;
    }
  }, [fields.length]);

  return (
    <>
      <RHFDebounceSelect
        // required={isPickup && !isEditingLocation}
        required={false}
        disabled={isEditingLocation}
        name={`${stop}.externalTMSID`}
        label='Location'
        control={control}
        placeholder='Search by name, address, or city'
        errors={errors}
        data={locations}
        isLoading={isLoadingLocations}
        refreshHandler={handleRefreshLocations}
        fetchOptions={handleLocationSearch}
        mapOptions={mapLocationsToAntdOptions}
        showSearchParamDropdown={false}
        searchFieldDefault='nameAddress'
        valueRenderer={(selectedOption) => {
          // Show the location name instead of external TMS ID in the input
          return {
            label: selectedOption.name || selectedOption.label,
            value: selectedOption.value,
          };
        }}
      />

      {isEditingLocation && <hr className='my-2' />}

      <LoadBuildingTextInput
        name={`${stop}.name`}
        label='Name'
        // The Mcleod location objects *probably* have all the address fields we required, but in case it doesn't conform to our expectations,
        // we don't require it when the user selects an existing location so the form isn't blocked.
        // This happened before where we required zipcodes to be 5 digits, but the existing TMS location had 6 digits and the input was read-only
        // so the user couldn't submit the form.
        options={getFieldValidation('name')}
      />

      <LoadBuildingTextInput
        name={`${stop}.addressLine1`}
        label='Address Line 1'
        options={getFieldValidation('addressLine1')}
      />

      <div className='grid grid-cols-2 w-full m-0 gap-2'>
        <LoadBuildingTextInput
          name={`${stop}.addressLine2`}
          label='Address Line 2'
        />

        <LoadBuildingTextInput
          name={`${stop}.city`}
          label='City'
          options={getFieldValidation('city')}
        />
      </div>
      <div className='grid grid-cols-2 w-full m-0 gap-2'>
        <LoadBuildingTextInput
          name={`${stop}.state`}
          label='State'
          options={getFieldValidation('state')}
        />

        {/* <div className='grid items-end grid-cols-2 w-full m-0 gap-2'> */}
        <LoadBuildingTextInput
          name={`${stop}.zipCode`}
          label='Zip Code'
          placeholder='12345'
          options={getFieldValidation('zipCode')}
        />
      </div>

      {/* Reference Number Section */}
      <div className='flex flex-col gap-2 mt-1'>
        <div className='flex items-center justify-between mr-[1px]'>
          <h4 className='text-grayscale-content-label text-sm font-medium'>
            Reference Numbers
          </h4>
          <Button
            type='button'
            variant='ghost'
            size='sm'
            onClick={handleAddNewReference}
            className='h-8 px-3 text-xs text-orange-500 hover:mx-[-1px]'
            buttonNamePosthog={
              ButtonNamePosthog.LoadBuildingAddMcleodReferenceNumber
            }
          >
            + Add Reference
          </Button>
        </div>

        {/* Display existing reference numbers */}
        {fields.length > 0 && (
          <div className='relative'>
            <div
              ref={scrollContainerRef}
              className={`flex gap-3 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 items-start ${
                fields.length === 1 ? 'justify-center' : ''
              }`}
            >
              {fields.map((field, index) => {
                const ref = field as AdditionalReference;

                return (
                  <ReferenceNumberCard
                    key={field.id}
                    reference={ref}
                    index={index}
                    onRemove={remove}
                    onEdit={handleEditReference}
                  />
                );
              })}
            </div>
            {/* Scroll hint for multiple cards */}
            {fields.length > 1 && (
              <div className='text-xs text-gray-500 mt-1 text-center'>
                ← Scroll to view more references →
              </div>
            )}
          </div>
        )}

        {/* Custom Reference Modal */}
        <ReferenceNumberModal
          qualifierOptions={qualifierCodes}
          isOpen={isRefNumberModalOpen}
          onCancel={handleRefNumberModalCancel}
          onOk={handleRefNumberModalOk}
          editingRefIndex={editingRefIndex}
          tempRefNumber={tempRefNumber}
          onTempRefChange={handleTempRefChange}
          stop={stop}
        />
      </div>

      {/* TODO: Finalize manaul location creation UI/UX */}
      {/* {isEditingLocation ? (
          <div className='flex flex-col gap-2'>
            <AntdButton
              type='link'
              className='h-0 text-grayscale-content-description'
              onClick={handleCancelEditingLocation}
            >
              Cancel
            </AntdButton>
            <Button size='sm' type='button' onClick={handleCreateLocation}>
              {isCreatingLocation ? <ButtonLoader /> : 'Submit'}
            </Button>
          </div>
        ) : (
          <Button size='sm' type='button' onClick={handleEditlocation}>
            Add Location
          </Button>
        )} */}
      {/* </div> */}
      {/* Not supported rn. Buggy duplication behavior on update */}
      {/* <LoadBuildingTextInput
        name={`${stop}.refNumber`}
        label='Ref #'
        placeholder='Comma-separated list (e.g. 123,456,789)'
      /> */}

      {(watchedLocObj?.apptRequired ||
        isEditingLocation ||
        !watchedLocObj.externalTMSID) && (
        <Controller
          name={`${stop}.apptRequired`}
          control={control}
          render={({ field }) => (
            <div className='flex items-center space-x-2'>
              <Checkbox
                onCheckedChange={(checked) => {
                  field.onChange(checked);
                }}
                checked={field.value || undefined}
              />
              <label
                htmlFor={`${stop}.apptRequired`}
                className='leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-grayscale-content-input'
              >
                {'Appointment Required?'}
              </label>
            </div>
          )}
        />
      )}

      {isEditingLocation && <hr className='my-2' />}

      <LoadDateTimeInput
        name={`${stop}.apptStartTime` as FieldPath<NormalizedLoad>}
        label='Appointment Start Time'
        required
      />

      <LoadDateTimeInput
        name={`${stop}.apptEndTime` as FieldPath<NormalizedLoad>}
        label='Appointment End Time'
      />
    </>
  );
}

export function hasNoErrorsInSubsection(
  subsectionPath: string,
  formErrors: FieldErrors
): boolean {
  const subsectionErrors = Object.entries(formErrors).filter(([key]) =>
    key.startsWith(subsectionPath)
  );

  return subsectionErrors.length === 0;
}
