import { useContext, useEffect } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';

import { Label } from 'components/Label';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { getCommodityOptions } from 'pages/QuoteView/LoadBuilding/McleodSectionForms/helpers';
import { NormalizedLoad, Specifications, Unit } from 'types/Load';
import { TMS } from 'types/enums/Integrations';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';
import { cn } from 'utils/shadcn';

import {
  LoadBuildingTextInput,
  RateType,
  devDisableRequiredFields,
} from '../McleodLoadBuildingForm';

const fetchOrderTypes = [
  { value: 'BACKUP', label: 'Backup Commitment' },
  { value: 'PRIMARY', label: 'Primary Commitment' },
  { value: 'SPOT', label: 'Spot Load' },
  { value: 'SUBJECT', label: 'Dummy Load' },
];

export function SpecificationsForm({
  formMethods,
  transportTypeOptions,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
  transportTypeOptions: {
    value: string;
    label: string;
  }[];
}) {
  const {
    control,
    formState: { errors },
    watch,
  } = formMethods;
  // TODO use tms.GetLoadBuildingAttributes ENG-3493

  const { tmsIntegrations } = useServiceFeatures();

  const tms = tmsIntegrations?.find((tms) => tms.name === TMS.McleodEnterprise);
  const shouldShowOrderType = tms
    ? !tms.tenant.includes('trident') && !tms.tenant.includes('fcfm')
    : false;

  const modes = ['LTL', 'Truckoad'];

  const {
    currentState: { clickedSuggestion },
  } = useContext(SidebarStateContext);

  const specs: Specifications = {
    totalWeight: { unit: Unit.Pounds },
    totalDistance: { unit: Unit.Miles },
    totalPieces: { unit: null, val: 0 },
  } as Specifications;

  const isWeightSupported = true;
  const isPiecesSupported = true;

  const isDistanceSupported = false;
  const isPalletSupported = tms ? tms.tenant.includes('fcfm') : false;

  const watchedCustomerRateType = watch('rateData.customerRateType');
  const arePalletsRequired = watch('specifications.palletsRequired');

  const commodityOptions = getCommodityOptions(tms);

  const palletOptions = [
    { value: true, label: 'Yes' },
    { value: false, label: 'No' },
  ];

  useEffect(() => {
    if (!arePalletsRequired) {
      formMethods.setValue('specifications.totalInPalletCount', null);
    }
  }, [arePalletsRequired]);

  return (
    <div className='grid grid-cols-2 gap-2 mx-0 w-full'>
      {/* Only Truckloads supported rn, not LTL */}
      <div className={cn(shouldShowOrderType ? 'col-span-1' : 'col-span-2')}>
        <Label hideAIHint={true} name={'mode'} required={true}>
          Mode
        </Label>
        {/* <div className='flex flex-row w-full items-center gap-2'> */}
        <Controller
          name='mode'
          control={control}
          rules={{ required: devDisableRequiredFields ? false : 'Required' }}
          render={({ field }) => (
            <div className='text-grayscale-content-input'>
              <AntdSelect
                showSearch
                disabled={true} // Only Truckloads supported rn, not LTL
                className='h-9 text-grayscale-content-input'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                options={modes?.map((mode) => ({
                  value: mode,
                  label: mode,
                }))}
              />
            </div>
          )}
        />
        {/* </div> */}
        <ErrorMessage
          errors={errors}
          name={'mode'}
          render={({ message }: { message: string }) => (
            <p className='text-red-500 text-xs'>{message}</p>
          )}
        />
      </div>

      {shouldShowOrderType && (
        <div className='col-span-1'>
          <Label name={'specifications.orderType'} required={true}>
            Order Type
          </Label>
          <Controller
            name='specifications.orderType'
            control={control}
            rules={{ required: devDisableRequiredFields ? false : 'Required' }}
            render={({ field }) => (
              <div className=' text-grayscale-content-input'>
                <AntdSelect
                  showSearch
                  className='h-9 text-grayscale-content-input'
                  placeholder={'Choose'}
                  optionFilterProp='children'
                  filterOption={(
                    input: string,
                    option: BaseOptionType | undefined
                  ) =>
                    (option?.label.toLocaleLowerCase() ?? '').includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(
                    optionA: BaseOptionType,
                    optionB: BaseOptionType
                  ) =>
                    (optionA?.label ?? '')
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  onChange={field.onChange}
                  value={field.value}
                  options={fetchOrderTypes?.map((method) => ({
                    value: method.label,
                    label: method.label,
                  }))}
                />
              </div>
            )}
          />
          <ErrorMessage
            errors={errors}
            name={'specifications.orderType'}
            render={({ message }: { message: string }) => (
              <p className='text-red-500 text-xs'>{message}</p>
            )}
          />
        </div>
      )}

      <div className='col-span-2'>
        <Label
          name={'specifications.transportType'}
          // We always default this value so don't show AI hint unless there's a suggestion
          hideAIHint={
            !clickedSuggestion ||
            clickedSuggestion.pipeline !== SuggestionPipelines.LoadBuilding
          }
        >
          Transport Type
        </Label>
        <Controller
          name='specifications.transportType'
          control={control}
          rules={{ required: false }}
          render={({ field }) => (
            <AntdSelect
              showSearch
              className='h-9 text-grayscale-content-input'
              placeholder={'Choose'}
              optionFilterProp='children'
              filterOption={(
                input: string,
                option: BaseOptionType | undefined
              ) =>
                (option?.label.toLocaleLowerCase() ?? '').includes(
                  input.toLocaleLowerCase()
                )
              }
              filterSort={(optionA: BaseOptionType, optionB: BaseOptionType) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
              onChange={field.onChange}
              value={field.value}
              options={transportTypeOptions?.map((type) => ({
                value: type.value,
                label: type.label,
              }))}
            />
          )}
        />
        <ErrorMessage
          errors={errors}
          name={'specifications.transportType'}
          render={({ message }: { message: string }) => (
            <p className='text-red-500 text-xs'>{message}</p>
          )}
        />
      </div>

      <div className='col-span-2'>
        <Label
          name={'specifications.commodities'}
          // We always default this value so don't show AI hint unless there's a suggestion
          hideAIHint={
            !clickedSuggestion ||
            clickedSuggestion.pipeline !== SuggestionPipelines.LoadBuilding
          }
        >
          Commodity
        </Label>
        <Controller
          name='specifications.commodities'
          control={control}
          rules={{ required: false }}
          render={({ field }) => (
            <AntdSelect
              showSearch
              className='h-9 text-grayscale-content-input'
              placeholder={'Choose'}
              optionFilterProp='children'
              filterOption={(
                input: string,
                option: BaseOptionType | undefined
              ) =>
                (option?.label.toLocaleLowerCase() ?? '').includes(
                  input.toLocaleLowerCase()
                )
              }
              filterSort={(optionA: BaseOptionType, optionB: BaseOptionType) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
              onChange={field.onChange}
              value={field.value}
              options={commodityOptions?.map((type) => ({
                value: type.code,
                label: type.description,
              }))}
            />
          )}
        />
        <ErrorMessage
          errors={errors}
          name={'specifications.commodities'}
          render={({ message }: { message: string }) => (
            <p className='text-red-500 text-xs'>{message}</p>
          )}
        />
      </div>

      {isPiecesSupported && (
        <div
          className={
            isPiecesSupported && !isWeightSupported
              ? 'col-span-2'
              : 'col-span-1'
          }
        >
          <LoadBuildingTextInput
            name='specifications.totalPieces.val'
            label={`Pieces${specs.totalPieces?.unit ? ' (' + specs.totalPieces?.unit + ')' : ''}`}
            inputType='number'
            options={{ valueAsNumber: true }}
          />
        </div>
      )}
      {isWeightSupported && (
        <div
          className={
            isWeightSupported && !isPiecesSupported
              ? 'col-span-2'
              : 'col-span-1'
          }
        >
          <LoadBuildingTextInput
            name='specifications.totalWeight.val'
            label={`Weight${specs.totalWeight?.unit ? ' (' + specs.totalWeight?.unit + ')' : ''}`}
            inputType='number'
            options={{
              valueAsNumber: true,
              required:
                watchedCustomerRateType === RateType.CWT ||
                watchedCustomerRateType === RateType.Tons
                  ? 'Required for weight-based rates'
                  : undefined,
            }}
            required={
              watchedCustomerRateType === RateType.CWT ||
              watchedCustomerRateType === RateType.Tons
            }
          />
        </div>
      )}
      <div className='col-span-1'>
        <Label
          name={'specifications.palletsRequired'}
          // We always default this value so don't show AI hint unless there's a suggestion
          hideAIHint={
            !clickedSuggestion ||
            clickedSuggestion.pipeline !== SuggestionPipelines.LoadBuilding
          }
        >
          Pallets Required
        </Label>
        <Controller
          name='specifications.palletsRequired'
          control={control}
          rules={{ required: false }}
          render={({ field }) => (
            <AntdSelect
              popupMatchSelectWidth={true}
              className='h-9 text-grayscale-content-input'
              placeholder={'Choose'}
              optionFilterProp='children'
              filterOption={(
                input: string,
                option: BaseOptionType | undefined
              ) =>
                (option?.label.toLocaleLowerCase() ?? '').includes(
                  input.toLocaleLowerCase()
                )
              }
              filterSort={(optionA: BaseOptionType, optionB: BaseOptionType) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
              onChange={field.onChange}
              value={field.value}
              options={palletOptions}
            />
          )}
        />
      </div>
      {isPalletSupported && arePalletsRequired && (
        <div className={'col-span-1'}>
          <LoadBuildingTextInput
            name='specifications.totalInPalletCount'
            label='Pallet Count'
            inputType='number'
            options={{
              valueAsNumber: true,
            }}
          />
        </div>
      )}
      <div className='col-span-2'>
        <LoadBuildingTextInput
          name='specifications.planningComment'
          label='Planning Comment'
          inputType='text'
        />
      </div>
      {isDistanceSupported && (
        <div
          className={
            isDistanceSupported && !isWeightSupported
              ? 'col-span-2'
              : 'col-span-1'
          }
        >
          <LoadBuildingTextInput
            name='specifications.totalDistance.val'
            label={`Distance${specs.totalDistance?.unit ? ' (' + specs.totalDistance?.unit + ')' : ''}`}
            inputType='number'
            options={{ valueAsNumber: true }}
          />
        </div>
      )}
    </div>
  );
}
