import { useContext, useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, useForm } from 'react-hook-form';

import { Accordion } from '@radix-ui/react-accordion';
import {
  BoxIcon,
  Building2,
  CircleDollarSignIcon,
  WarehouseIcon,
  Weight,
} from 'lucide-react';

import { Button } from 'components/Button';
import ButtonLoader from 'components/loading/ButtonLoader';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { CreateLoadRequest, createLoad } from 'lib/api/createLoad';
import { getCustomers } from 'lib/api/getCustomers';
import { getLocations } from 'lib/api/getLocations';
import { FormStorageService } from 'lib/services/FormStorage/service';
import { LoadSectionAccordionItem } from 'pages/LoadView/LoadInformationTab';
import {
  Load,
  NormalizedLoad,
  TMSCustomer,
  TMSLocation,
  Unit,
  createInitLoad,
  createInitRateData,
  createInitSpecs,
  normalizeLoad,
} from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import ButtonText from 'types/enums/ButtonText';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';
import { SuggestedLoad } from 'types/suggestions/LoadBuildingSuggestions';

import { CustomerSectionForm } from './TaiSectionForms/Customer';
import { RatesForm } from './TaiSectionForms/Rates';
import { SpecificationsForm } from './TaiSectionForms/Specifications';
import { StopForm } from './TaiSectionForms/Stop';

enum AvailableTabs {
  customer = 'customer',
  specifications = 'specifications',
  rates = 'rates',
  pickup = 'pickup',
  consignee = 'consignee',
}

export default function TaiLoadBuildingForm() {
  const { toast } = useToast();
  const { tmsIntegrations } = useServiceFeatures();
  const [isLoadingCustomers, setIsloadingCustomers] = useState(true);
  const [isLoadingLocations, setIsLoadingLocations] = useState(true);
  const [customers, setCustomers] = useState<Maybe<TMSCustomer[]>>(null);
  const [locations, setLocations] = useState<Maybe<TMSLocation[]>>(null);
  const [builtLoad, setBuiltLoad] = useState<Maybe<Load>>(null);
  const [activeTabs, setActiveTabs] = useState<string[]>(
    Object.values(AvailableTabs)
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadBuildingSuggestionClicked, setIsLoadBuildingSuggestionClicked] =
    useState(false);

  // Suggestion context
  const {
    currentState: { clickedSuggestion, threadId, goToSuggestionInCarousel },
    setCurrentState,
  } = useContext(SidebarStateContext);

  // Fetch customers/locations
  const fetchCustomers = async () => {
    setIsloadingCustomers(true);
    const res = await getCustomers(tmsIntegrations?.[0]?.id);
    if (res.isOk()) {
      setCustomers(res.value.customerList);
    } else {
      toast({
        description: 'Error while fetching customer list.',
        variant: 'destructive',
      });
    }
    setIsloadingCustomers(false);
  };

  const fetchLocations = async () => {
    setIsLoadingLocations(true);
    const res = await getLocations(tmsIntegrations?.[0]?.id);
    if (res.isOk()) {
      setLocations(res.value.locationList);
    } else {
      toast({
        description: 'Error while fetching location list.',
        variant: 'destructive',
      });
    }
    setIsLoadingLocations(false);
  };

  const handleRefreshLocations = async () => {
    setIsLoadingLocations(true);
    const res = await getLocations(tmsIntegrations?.[0]?.id, true);
    if (res.isOk()) {
      setLocations(res.value.locationList);
    } else {
      toast({
        description: 'Error while refreshing location list.',
        variant: 'destructive',
      });
    }
    setIsLoadingLocations(false);
  };

  // Memoized default values, with suggestion support
  const memoizedDefaultValues = useMemo(() => {
    if (
      clickedSuggestion &&
      clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
    ) {
      const casted = clickedSuggestion.suggested as SuggestedLoad;
      const transportType = casted.specifications?.transportType?.toLowerCase();
      const suggestedFields = {
        ...casted,
        mode: 'Truckload',
        specifications: {
          ...createInitSpecs(),
          ...casted.specifications,
          transportType:
            transportType === 'reefer'
              ? 'Reefer'
              : transportType === 'flatbed'
                ? 'Flatbed'
                : 'Van',
        },
        rateData: {
          ...createInitRateData(),
          ...casted.rateData,
          customerTotalCharge: {
            ...casted.rateData?.customerTotalCharge,
            unit: Unit.USD,
          },
        },
      };
      return {
        ...normalizeLoad('Tai', createInitLoad()),
        ...suggestedFields,
        mode: 'Truckload',
        rateData: {
          ...suggestedFields.rateData,
          customerTotalCharge: {
            ...suggestedFields.rateData.customerTotalCharge,
            unit: Unit.USD,
          },
        },
      } as NormalizedLoad;
    }
    // Otherwise, use normal default
    const parsedValues = normalizeLoad(tmsIntegrations?.[0]?.name || '', {
      ...createInitLoad(),
      mode: 'Truckload',
      specifications: {
        ...createInitSpecs(),
        transportType: 'Van',
        serviceType: 'Any',
      },
      rateData: {
        ...createInitRateData(),
        customerTotalCharge: {
          val: 0,
          unit: Unit.USD,
        },
      },
    });
    return parsedValues;
  }, [clickedSuggestion, tmsIntegrations]);

  const formMethods = useForm<NormalizedLoad>({
    defaultValues: memoizedDefaultValues,
  });

  // On mount, go to first load-building suggestion if none is selected
  useEffect(() => {
    if (!clickedSuggestion) {
      goToSuggestionInCarousel({
        suggestionPipeline: SuggestionPipelines.LoadBuilding,
      });
    }
    fetchCustomers();
    fetchLocations();
  }, []);

  // Set flag for suggestion presence
  useEffect(() => {
    if (
      clickedSuggestion &&
      clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
    ) {
      setIsLoadBuildingSuggestionClicked(true);
    } else {
      setIsLoadBuildingSuggestionClicked(false);
    }
  }, [clickedSuggestion]);

  // Reset form when default values change
  useEffect(() => {
    formMethods.reset(memoizedDefaultValues, {
      keepDefaultValues: false,
      keepTouched: false,
      keepDirtyValues: false,
      keepDirty: false,
    });
  }, [memoizedDefaultValues]);

  // Persist form state on every change (like Turvo)
  useEffect(() => {
    const subscription = formMethods.watch((value) => {
      FormStorageService.saveFormState(`Tai_${threadId}`, {
        threadID: threadId,
        values: value,
        clickedSuggestion: isLoadBuildingSuggestionClicked
          ? clickedSuggestion
          : null,
        dirtyFields: formMethods.formState.dirtyFields,
      });
    });
    return () => subscription.unsubscribe();
  }, [
    formMethods,
    threadId,
    isLoadBuildingSuggestionClicked,
    clickedSuggestion,
  ]);

  const onSubmit: SubmitHandler<NormalizedLoad> = async (data) => {
    setIsLoading(true);
    data.freightTrackingID = '';
    data.externalTMSID = '';
    const reqData: CreateLoadRequest = {
      load: {
        ...data,
        tmsID: tmsIntegrations[0]?.id,
      } as Load,
    };
    const res = await createLoad(reqData);
    if (res.isOk()) {
      setBuiltLoad(res.value.load);
      FormStorageService.clearFormState(`Tai_${threadId}`);
      if (
        clickedSuggestion &&
        clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
      ) {
        setCurrentState((prevState) => {
          const filteredList = prevState.curSuggestionList.filter(
            ({ id }) => id !== clickedSuggestion.id
          );
          return {
            ...prevState,
            clickedSuggestion: null,
            curSuggestionList: filteredList,
          };
        });
      }
      toast({
        title: res.value.message,
        description:
          'Load ID: ' +
          (res.value.load.externalTMSID ?? res.value.load.externalTMSID),
        variant: 'success',
      });
    } else {
      toast({
        description: res.error.message,
        variant: 'destructive',
      });
    }
    setIsLoading(false);
  };

  function handleClearForm() {
    setBuiltLoad(null);
    FormStorageService.clearFormState(`Tai_${threadId}`);
    formMethods.reset(formMethods.getValues(), {
      keepDefaultValues: false,
      keepDirty: false,
      keepDirtyValues: false,
      keepTouched: false,
    });
  }

  return (
    <div className='mb-5'>
      <ExtendedFormProvider aiDefaultValues={false}>
        <FormProvider {...formMethods}>
          <form onSubmit={formMethods.handleSubmit(onSubmit)}>
            <Accordion
              type='multiple'
              value={activeTabs}
              onValueChange={setActiveTabs}
            >
              <LoadSectionAccordionItem
                label='Customer'
                icon={<Building2 className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.customer}
                activeTabs={activeTabs}
              >
                <CustomerSectionForm
                  formMethods={formMethods}
                  customers={customers || []}
                  setCustomers={setCustomers}
                  isLoadingCustomers={isLoadingCustomers}
                  setIsLoadingCustomers={setIsloadingCustomers}
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Specs'
                icon={<Weight className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.specifications}
                activeTabs={activeTabs}
              >
                <SpecificationsForm formMethods={formMethods} />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Rates'
                icon={
                  <CircleDollarSignIcon className='h-6 w-6' strokeWidth={1} />
                }
                name={AvailableTabs.rates}
                activeTabs={activeTabs}
              >
                <RatesForm formMethods={formMethods} />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Pickup'
                icon={<BoxIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.pickup}
                activeTabs={activeTabs}
              >
                <StopForm
                  formMethods={formMethods}
                  locations={locations || []}
                  stop='pickup'
                  handleRefreshLocations={handleRefreshLocations}
                  setLocations={setLocations}
                  isLoadingLocations={isLoadingLocations}
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Consignee'
                icon={<WarehouseIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.consignee}
                activeTabs={activeTabs}
              >
                <StopForm
                  formMethods={formMethods}
                  locations={locations || []}
                  stop='consignee'
                  handleRefreshLocations={handleRefreshLocations}
                  setLocations={setLocations}
                  isLoadingLocations={isLoadingLocations}
                />
              </LoadSectionAccordionItem>
            </Accordion>

            <section className='w-full mt-4'>
              <Button
                buttonNamePosthog={ButtonNamePosthog.BuildLoad}
                type='submit'
                className='w-full'
                disabled={isLoading}
              >
                {isLoading ? <ButtonLoader /> : ButtonText.BuildLoad}
              </Button>

              {formMethods.formState.isDirty && (
                <div className='flex flex-row justify-center align-center'>
                  <Button
                    buttonNamePosthog={ButtonNamePosthog.ClearForm}
                    type='button'
                    className='w-50% mt-4 h-8 text-sm text-grayscale-content-input'
                    disabled={isLoading}
                    variant='outline'
                    onClick={handleClearForm}
                  >
                    {ButtonText.ClearForm}
                  </Button>
                </div>
              )}

              {builtLoad?.externalTMSID && (
                <div className='whitespace-pre-wrap my-3 rounded py-3 text-grayscale-content-label px-4 bg-green-bg'>
                  <p className='mb-2'>Load Created 🎉</p>
                  <p className='mb-2 text-sm'>
                    <b className='text-[14px]'>Load ID #: </b>
                    {builtLoad.externalTMSID}
                  </p>
                </div>
              )}
            </section>
          </form>
        </FormProvider>
      </ExtendedFormProvider>
    </div>
  );
}
