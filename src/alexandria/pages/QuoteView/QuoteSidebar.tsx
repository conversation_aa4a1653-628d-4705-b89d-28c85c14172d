import { useContext, useEffect, useRef, useState } from 'react';

import {
  CircleDollarSign,
  HammerIcon,
  ListChecksIcon,
  MailIcon,
  Truck,
} from 'lucide-react';

import SuggestionsCarousel from 'components/AISuggestions/SuggestionsCarousel';
import { Badge } from 'components/Badge';
import ErrorBoundary from 'components/ErrorBoundary';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  TabsTriggerVariants,
} from 'components/Tabs';
import { Toaster } from 'components/ToasterProvider';
import {
  QuoteSidebarPortName,
  UpdateQuoteRequestDataAction,
} from 'constants/BackgroundScript';
import { ServiceFeaturesListType } from 'contexts/serviceContext';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import useFetchQuoteRequest from 'hooks/useFetchQuoteRequest';
import { useServiceFeatures } from 'hooks/useServiceContext';
import {
  CreateQuoteRequestSuggestionRequest,
  createQuoteRequestSuggestion,
} from 'lib/api/createQuoteRequestSuggestion';
import { getLoadBuildingSuggestion } from 'lib/api/getLoadBuildingSuggestion';
import { getQuickQuoteSuggestion } from 'lib/api/getQuickQuoteSuggestion';
import getTruckList, { TruckListResponse } from 'lib/api/getTruckList';
import { Email } from 'types/Email';
import { Maybe, MaybeUndef, Undef } from 'types/UtilityTypes';
import { SidepanelMessage } from 'types/chromescript/util';
import {
  GenericSuggestion,
  SuggestionCategories,
  SuggestionPipelines,
} from 'types/suggestions/CoreSuggestions';
import { LoadBuildingSuggestions } from 'types/suggestions/LoadBuildingSuggestions';
import { SuggestionStatus } from 'types/suggestions/LoadSuggestions';
import { SuggestedQuoteChange } from 'types/suggestions/QuoteSuggestions';
import captureException from 'utils/captureException';
import { filterInvalidSuggestions } from 'utils/suggestionValidators';

import LoadBuildingTab from './LoadBuilding/LoadBuildingTab';
import CarrierQuoteTab from './Quoting/CarrierQuote/CarrierQuoteTab';
import QuickQuoteTab from './Quoting/RequestQuickQuote/QuickQuoteTab';
import TruckListTab from './TruckList/TruckListTab';

export enum AvailableTabs {
  QuickQuote = 'quickQuote',
  TrucksList = 'trucksList',
  LoadBuilding = 'loadBuilding',
  CarrierQuoting = 'carrierQuoting',
  // If tabs are added, update setDefaultTabByFeatureFlagsHierarchy
}

/**
 * Get the default tab based on the hierarchy of service features enabled.
 * Note that this is independent and before setting the *initially displayed* tab based on loaded suggestions.
 *
 * @param features - The service features enabled.
 * @returns The default tab.
 */
const setDefaultTabByFeatureFlagsHierarchy = (
  features: ServiceFeaturesListType
): AvailableTabs => {
  const {
    isQuoteViewEnabled,
    isQuickQuoteEnabled,
    isCarrierNetworkQuotingEnabled,
    isLoadBuildingEnabled,
    isTruckListEnabled,
  } = features;

  // Check enabled features in priority order
  if (isQuickQuoteEnabled) return AvailableTabs.QuickQuote;
  if (isCarrierNetworkQuotingEnabled) return AvailableTabs.CarrierQuoting;
  if (isLoadBuildingEnabled) return AvailableTabs.LoadBuilding;
  if (isTruckListEnabled) return AvailableTabs.TrucksList;

  // It's possible that the service features are not initialized yet when component is mounted so
  // verify it is before sending to Sentry; heuristic is that if QuoteSidebar is rendering at all, then isQuoteViewEnabled must be true
  if (isQuoteViewEnabled) {
    captureException(
      new Error('Unable to set default tab based on service feature flags:'),
      { features: features }
    );
  }

  return AvailableTabs.QuickQuote;
};

/**
 * Set the tab based on the hierarchy of suggestions.
 * Note that this is independent and after setting the *initially displayed* tab based purely on service features.
 * For example, a service has QQ & LB enabled, but no QQ suggestions for the current thread, only LBs
 * In this case, setDefaultTabByFeatureFlagsHierarchy will initially set to QQ tab based on feature flags,
 *  but once LB suggestions are loaded, setTabBySuggestionHierarchy will set the tab to LB as it has higher priority.
 * (This is a bit of a contrived example as it's unlikely a thread has QQ without LB suggestions.)
 *
 * @param features - The service features enabled.
 * @param quoteRequestSuggestions - The quote request suggestions.
 * @param loadBuildingSuggestions - The load building suggestions.
 * @param truckListResult - The truck list suggestions.
 */
const setTabBySuggestionHierarchy = (
  features: ServiceFeaturesListType,
  quoteRequestSuggestions: Maybe<SuggestedQuoteChange[]>,
  loadBuildingSuggestions: Maybe<LoadBuildingSuggestions[]>,
  truckListResult: MaybeUndef<TruckListResponse>
): Undef<AvailableTabs> => {
  const {
    isQuickQuoteEnabled,
    isCarrierNetworkQuotingEnabled,
    isLoadBuildingEnabled,
    isTruckListEnabled,
  } = features;

  if (quoteRequestSuggestions && quoteRequestSuggestions.length > 0) {
    // QR suggestions can be applied to both QQ and CQ tabs; default to QQ first if enabled
    if (isQuickQuoteEnabled) {
      return AvailableTabs.QuickQuote;
    }
    if (isCarrierNetworkQuotingEnabled) {
      return AvailableTabs.CarrierQuoting;
    }
  }

  if (
    loadBuildingSuggestions &&
    loadBuildingSuggestions.length > 0 &&
    isLoadBuildingEnabled
  ) {
    return AvailableTabs.LoadBuilding;
  }

  // Truck List suggestions
  if (isTruckListEnabled && truckListResult) {
    return AvailableTabs.TrucksList;
  }

  return undefined;
};

type QuoteSidebarProps = {
  email: Maybe<Email>;
};

export default function QuoteSidebar({ email }: QuoteSidebarProps) {
  const [isLoadingQuoteSuggestions, setIsLoadingQuoteSuggestions] =
    useState(false);
  const [quoteSuggestions, setQuoteSuggestions] = useState<
    Maybe<SuggestedQuoteChange[]>
  >([]);

  const [
    isLoadingLoadBuildingSuggestions,
    setIsLoadingLoadBuildingSuggestions,
  ] = useState(false);
  const [loadBuildingSuggestions, setLoadBuildingSuggestions] = useState<
    Maybe<LoadBuildingSuggestions[]>
  >([]);

  const [allSuggestions, setAllSuggestions] = useState<GenericSuggestion[]>([]);
  const [truckListResult, setTruckListResult] =
    useState<Maybe<TruckListResponse>>();

  const { serviceFeaturesEnabled, carrierQuoteConfig } = useServiceFeatures();
  const { request, isLoading: isLoadingRequest } = useFetchQuoteRequest(
    email?.threadID ?? ''
  );

  const {
    isQuickQuoteEnabled,
    isTruckListEnabled,
    isLoadBuildingEnabled,
    isCarrierNetworkQuotingEnabled,
  } = serviceFeaturesEnabled;

  const {
    setCurrentState,
    currentState: { clickedSuggestion, tabId },
  } = useContext(SidebarStateContext);

  const [tab, setTab] = useState<Undef<AvailableTabs>>();

  const quickQuoteTabRef = useRef<HTMLButtonElement>(null);
  const trucksListTabRef = useRef<HTMLButtonElement>(null);
  const loadBuildingTabRef = useRef<HTMLButtonElement>(null);
  const carrierQuotingTabRef = useRef<HTMLButtonElement>(null);

  const tabRefs: Record<AvailableTabs, React.RefObject<HTMLButtonElement>> = {
    [AvailableTabs.QuickQuote]: quickQuoteTabRef,
    [AvailableTabs.TrucksList]: trucksListTabRef,
    [AvailableTabs.LoadBuilding]: loadBuildingTabRef,
    [AvailableTabs.CarrierQuoting]: carrierQuotingTabRef,
  };

  const fetchQuoteSuggestions = async () => {
    if (!email?.threadID || email.threadID === '') {
      return;
    }
    setIsLoadingQuoteSuggestions(true);

    // Set a timeout to clear loading state after 1.5s in case one QR or LB endpoint is slower than the other
    const timeoutId = setTimeout(() => {
      setIsLoadingQuoteSuggestions(false);
    }, 1500);

    const res = await getQuickQuoteSuggestion(email.threadID);
    setQuoteSuggestions(res.isOk() ? filterInvalidSuggestions(res.value) : []);
    setIsLoadingQuoteSuggestions(false);
    clearTimeout(timeoutId); // Clear the timeout if fetch completes first
  };

  const fetchLoadBuildingSuggestions = async () => {
    if (!email?.threadID || email.threadID === '') {
      return;
    }
    setIsLoadingLoadBuildingSuggestions(true);

    // Set a timeout to clear loading state after 1.5s
    const timeoutId = setTimeout(() => {
      setIsLoadingLoadBuildingSuggestions(false);
    }, 1500);

    const res = await getLoadBuildingSuggestion(email?.threadID);
    setLoadBuildingSuggestions(
      res.isOk() ? filterInvalidSuggestions(res.value) : []
    );
    setIsLoadingLoadBuildingSuggestions(false);
    clearTimeout(timeoutId); // Clear the timeout if fetch completes first
  };

  const fetchTruckList = async () => {
    if (!email?.id || !email?.threadID || email.threadID === '') return;

    const res = await getTruckList(email.id, email.threadID);
    setTruckListResult(res.isOk() ? res.value : null);
  };

  // Set default tab based on service features
  useEffect(() => {
    setTab(setDefaultTabByFeatureFlagsHierarchy(serviceFeaturesEnabled));
  }, [email, serviceFeaturesEnabled]);

  // Get suggestions based on email labels & features enabled
  useEffect(() => {
    // Don't check most recent email's labels in case there are load building suggestions earlier in the thread
    if (isLoadBuildingEnabled) {
      fetchLoadBuildingSuggestions();
    } else {
      setLoadBuildingSuggestions([]);
    }

    if (
      isQuickQuoteEnabled // TODO: Support CQ, fix glitchy rendering
    ) {
      fetchQuoteSuggestions();
    } else {
      setQuoteSuggestions([]);
    }

    if (isTruckListEnabled) {
      fetchTruckList();
    } else {
      setTruckListResult(null);
    }
  }, [email, serviceFeaturesEnabled]);

  // Once all suggestions are fetched, update suggestions carousel
  useEffect(() => {
    if (isLoadingQuoteSuggestions || isLoadingLoadBuildingSuggestions) return;

    setAllSuggestions([
      // TODO: Name fields in load building form are buggy if suggestions are applied before tab is set
      // Show load building suggestions and tab first to
      // 1) workaround bug with and 2) create nice UX where first suggestion and first tab match
      ...(loadBuildingSuggestions || []),
      ...(quoteSuggestions || []),
    ]);

    const defaultTab = setTabBySuggestionHierarchy(
      serviceFeaturesEnabled,
      quoteSuggestions,
      loadBuildingSuggestions,
      truckListResult
    );

    if (defaultTab && defaultTab !== tab) {
      setTab(defaultTab);
    }
  }, [
    quoteSuggestions,
    loadBuildingSuggestions,
    isLoadingQuoteSuggestions,
    isLoadingLoadBuildingSuggestions,
    serviceFeaturesEnabled,
  ]);

  const handleChromeMessage = async (message: SidepanelMessage) => {
    if (message.targetTabId !== tabId) return;
    if (message.action === UpdateQuoteRequestDataAction) {
      const data = message.data as CreateQuoteRequestSuggestionRequest | null;

      // If data is null, clear the suggestions
      if (data === null) {
        if (clickedSuggestion?.pipeline === SuggestionPipelines.QuickQuote) {
          setCurrentState((prevState) => ({
            ...prevState,
            clickedSuggestion: null,
          }));
        }
        setQuoteSuggestions([]);

        return;
      }

      // Create the suggestion in the API with a 0.5 second timeout
      const result = await createQuoteRequestSuggestion(data);
      let suggestion: SuggestedQuoteChange;
      if (result.isOk()) {
        suggestion = result.value;
      } else {
        // Fail-open
        suggestion = {
          id: 0,
          createdAt: new Date().toISOString(),
          account: '',
          status: SuggestionStatus.Pending,
          pipeline: SuggestionPipelines.QuickQuote,
          category: SuggestionCategories.QuickQuote,
          attachment: null,
          emailID: 0,
          latestEmailIDFromThread: 0,
          source: data.source,
          sourceExternalID: data.sourceExternalID,
          sourceURL: data.sourceURL,
          sourceCategory: data.sourceCategory,
          suggested: {
            ...data,
            pickupDate: data.pickupDate ? new Date(data.pickupDate) : null,
            deliveryDate: data.deliveryDate
              ? new Date(data.deliveryDate)
              : null,
          },
        };
      }
      if (clickedSuggestion?.pipeline === SuggestionPipelines.QuickQuote) {
        setCurrentState((prevState) => ({
          ...prevState,
          clickedSuggestion: null,
        }));
      }
      setQuoteSuggestions([suggestion]);
    }
  };

  useEffect(() => {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      // Add listener for messages from the background script
      chrome.runtime.onMessage.addListener(handleChromeMessage);

      // Open a port connection to let background script know quote sidebar has loaded & is ready to start receiving messages
      chrome.runtime.connect({
        name: `${QuoteSidebarPortName}-${tabId}`,
      });

      // Cleanup function to remove the listener
      return () => {
        chrome.runtime.onMessage.removeListener(handleChromeMessage);
      };
    }
    return;
  }, [tabId]);

  // Redirect to correct tab after Email AI card is clicked
  useEffect(() => {
    if (!clickedSuggestion) return;

    switch (clickedSuggestion.pipeline) {
      case SuggestionPipelines.QuickQuote:
        if (isQuickQuoteEnabled) {
          setTab(AvailableTabs.QuickQuote);
          break;
        }
        if (isCarrierNetworkQuotingEnabled) {
          setTab(AvailableTabs.CarrierQuoting);
          break;
        }
        break;
      case SuggestionPipelines.LoadBuilding:
        setTab(AvailableTabs.LoadBuilding);
        break;
      default:
        captureException(
          new Error('Invalid suggestion pipeline: ' + clickedSuggestion)
        );
    }
  }, [clickedSuggestion]);

  const carrerQuoteTabLabel =
    carrierQuoteConfig?.customName !== ''
      ? carrierQuoteConfig?.customName
      : 'Carrier Quote';

  const numberOfCarrierResponses =
    request?.carrierNetworkEmails?.filter((email) =>
      request.carrierQuotes?.some(
        (cq) => cq.threadID === email.threadID && cq.emailID
      )
    ).length ?? 0;

  return (
    <div className='flex-1'>
      <div className='mt-4'>
        <SuggestionsCarousel suggestions={allSuggestions} />

        <Tabs
          value={tab}
          onValueChange={(tab) => setTab(tab as AvailableTabs)}
          className='w-full h-full flex-1 shrink-0 flex flex-col'
        >
          <TabsList className='w-full overflow-x-auto flex gap-0'>
            {isQuickQuoteEnabled && (
              <TabsTrigger
                value={AvailableTabs.QuickQuote}
                ref={tabRefs.quickQuote}
                variant={TabsTriggerVariants.LoadTabs}
              >
                <CircleDollarSign className='w-5 h-5' strokeWidth={1} />
                <p className='overflow-x-hidden'>Quick Quote</p>
                {quoteSuggestions && quoteSuggestions?.length > 0 && (
                  <Badge
                    variant='secondary'
                    className='ml-0.5 !bg-blue-50 !text-blue-main !border-blue-100 !px-1 !py-0 text-[9px] data-[state=inactive]:absolute data-[state=inactive]:right-0 data-[state=inactive]:top-0'
                  >
                    {quoteSuggestions.length}
                  </Badge>
                )}
              </TabsTrigger>
            )}
            {isCarrierNetworkQuotingEnabled && (
              <TabsTrigger
                value={AvailableTabs.CarrierQuoting}
                ref={tabRefs.carrierQuoting}
                variant={TabsTriggerVariants.LoadTabs}
              >
                <Truck className='w-5 h-5 flex-shrink-0' strokeWidth={1} />
                <p className='overflow-x-hidden'>{carrerQuoteTabLabel}</p>
                {numberOfCarrierResponses > 0 && (
                  <Badge
                    variant='secondary'
                    className='ml-0.5 !bg-blue-50 !text-blue-main !border-blue-100 !px-1 !py-0 text-[9px] data-[state=inactive]:absolute data-[state=inactive]:right-0 data-[state=inactive]:top-0'
                  >
                    <MailIcon className='w-3 h-3' strokeWidth={2} />
                  </Badge>
                )}
              </TabsTrigger>
            )}
            {isLoadBuildingEnabled && (
              <TabsTrigger
                value={AvailableTabs.LoadBuilding}
                ref={tabRefs.loadBuilding}
                variant={TabsTriggerVariants.LoadTabs}
              >
                <HammerIcon className='w-5 h-5' strokeWidth={1} />
                <p className='overflow-x-hidden'>Load Building</p>
                {loadBuildingSuggestions &&
                  loadBuildingSuggestions.length > 0 && (
                    <Badge
                      variant='secondary'
                      className='ml-0.5 !bg-blue-50 !text-blue-main !border-blue-100 !px-1 !py-0 text-[9px] data-[state=inactive]:absolute data-[state=inactive]:right-0 data-[state=inactive]:top-0'
                    >
                      {loadBuildingSuggestions.length}
                    </Badge>
                  )}
              </TabsTrigger>
            )}
            {isTruckListEnabled && (
              <TabsTrigger
                value={AvailableTabs.TrucksList}
                ref={tabRefs.trucksList}
                variant={TabsTriggerVariants.LoadTabs}
              >
                <ListChecksIcon className='w-5 h-5' strokeWidth={1} />
                <p className='overflow-x-hidden'>Truck List</p>
              </TabsTrigger>
            )}
          </TabsList>
          <div className='px-4 flex-1 shrink-0'>
            {isQuickQuoteEnabled && (
              <TabsContent value={AvailableTabs.QuickQuote}>
                <ErrorBoundary>
                  <QuickQuoteTab email={email} />
                </ErrorBoundary>
              </TabsContent>
            )}
            {isCarrierNetworkQuotingEnabled && (
              <TabsContent value={AvailableTabs.CarrierQuoting}>
                <ErrorBoundary>
                  <CarrierQuoteTab
                    email={email}
                    request={request}
                    isLoadingRequest={isLoadingRequest}
                  />
                </ErrorBoundary>
              </TabsContent>
            )}
            {isLoadBuildingEnabled && (
              <TabsContent value={AvailableTabs.LoadBuilding}>
                <ErrorBoundary>
                  <LoadBuildingTab />
                </ErrorBoundary>
              </TabsContent>
            )}
            {isTruckListEnabled && (
              <TabsContent value={AvailableTabs.TrucksList}>
                <ErrorBoundary>
                  <TruckListTab email={email} truckList={truckListResult} />
                </ErrorBoundary>
              </TabsContent>
            )}
          </div>
        </Tabs>
      </div>
      <Toaster />
    </div>
  );
}
