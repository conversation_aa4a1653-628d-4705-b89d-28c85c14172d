import { useMemo } from 'react';

import Section, { DataElement } from 'components/Section';
import { useLoadContext } from 'hooks/useLoadContext';
import useTMSContext from 'hooks/useTMSContext';
import { StopTypes } from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { TMS } from 'types/enums/Integrations';

interface MiniLoadInfoProps {
  load: NormalizedLoad;
  type: StopTypes;
}

export function MiniLoadInfo({ load, type }: MiniLoadInfoProps) {
  const { loadAttrsObj: attrs } = useLoadContext();
  const { tmsName } = useTMSContext();
  const pickup = load.pickup;
  const dropoff = load.consignee;
  const specs = load.specifications;
  const specsAttrs = attrs.specifications;

  const specsArray: DataElement[] = useMemo(
    () => [
      ...(type === StopTypes.Pickup
        ? [
            {
              field: 'pickup',
              label: `Pickup Address`,
              className: 'col-span-2',
              value: formatAddress(pickup),
              alwaysShown: true,
            },
          ]
        : []),
      ...(type === StopTypes.Dropoff
        ? [
            {
              field: 'dropoff',
              label: `Dropoff Address`,
              className: 'col-span-2',
              value: formatAddress(dropoff),
              alwaysShown: true,
            },
          ]
        : []),
      ...(specsAttrs.commodities.isNotSupported
        ? []
        : [
            {
              field: 'commodities',
              label: 'Commodities',
              value: specs.commodities,
              alwaysShown: false, // Specification fields are hidden by default
            },
          ]),
      ...(specsAttrs.totalWeight.isNotSupported
        ? []
        : [
            {
              field: 'totalWeight',
              label: `Weight${specs.totalWeight?.unit ? ' (' + specs.totalWeight?.unit + ')' : ''}`,
              value: specs.totalWeight?.val ?? '',
              className: tmsName === TMS.Relay ? 'col-span-1' : '',
              alwaysShown: false,
            },
          ]),
      ...(specsAttrs.totalDistance.isNotSupported
        ? []
        : [
            {
              field: 'totalDistance',
              label: `Distance${specs.totalDistance?.unit ? ' (' + specs.totalDistance?.unit + ')' : ''}`,
              value: specs.totalDistance?.val ?? '',
              className: tmsName === TMS.Relay ? 'col-span-1' : '',
              alwaysShown: false,
            },
          ]),
      ...(specsAttrs.totalOutPalletCount.isNotSupported
        ? []
        : [
            {
              field: 'pallets',
              label: 'Pallets',
              value: specs.totalOutPalletCount,
              className: tmsName === TMS.Relay ? 'col-span-1' : '',
              alwaysShown: false,
            },
          ]),
      ...(specsAttrs.totalPieces.isNotSupported
        ? []
        : [
            {
              field: 'pieces',
              label: `Pieces${specs.totalPieces?.unit ? ' (' + specs.totalPieces?.unit + ')' : ''}`,
              value: specs.totalPieces?.val ?? '',
              className: tmsName === TMS.Relay ? 'col-span-1' : '',
              alwaysShown: false,
            },
          ]),
    ],
    [type, pickup, dropoff, specs, specsAttrs, tmsName]
  );

  return (
    <div className='my-4'>
      <h3 className='font-semibold my-2'>Load Details</h3>
      <Section
        sectionPrefix={'specs'}
        objectData={specsArray}
        collapsible={true}
      />
    </div>
  );
}

function formatAddress(address: {
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  zipCode?: string;
}): string {
  if (!address) {
    return '';
  }

  const parts: string[] = [];

  // Join address lines with comma
  if (address.addressLine1) {
    parts.push(address.addressLine1);
  }

  if (address.addressLine2) {
    parts.push(address.addressLine2);
  }

  // Join address lines with comma, then add dash before city
  const addressPart = parts.join(', ');
  const cityStateZip: string[] = [];

  if (address.city) {
    cityStateZip.push(address.city);
  }

  if (address.state) {
    cityStateZip.push(address.state.toUpperCase());
  }

  if (address.zipCode) {
    cityStateZip.push(address.zipCode);
  }

  const cityStateZipPart = cityStateZip.join(', ');

  // Combine address part with city/state/zip part
  if (addressPart && cityStateZipPart) {
    return `${addressPart} - ${cityStateZipPart}`;
  } else if (addressPart) {
    return addressPart;
  } else if (cityStateZipPart) {
    return cityStateZipPart;
  }

  return '';
}
