import { useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON>ath,
  FormProvider,
  SubmitErrorHandler,
  useForm,
} from 'react-hook-form';

import { Divider } from 'antd';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import DateTimeInput from 'components/input/DateTimeInput';
import { RHFTextInput } from 'components/input/RHFTextInput';
import ButtonLoader from 'components/loading/ButtonLoader';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { useToast } from 'hooks/useToaster';
import { confirmSlotAppt } from 'lib/api/confirmSlotAppt';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import { submitAppt } from 'lib/api/submitAppt';
import { validateAppt } from 'lib/api/validateAppt';
import {
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import ButtonText from 'types/enums/ButtonText';
import { cn } from 'utils/shadcn';

dayjs.extend(utc);

// Company data for 3rd party requests
const COMPANY_WISE_LOCATORS = {
  'dart container': {},
  'abbott nutrition': {},
  "america's logistics, llc": {},
  'american sugar refining, inc': {},
  'associated wholesale grocers': {},
  'batory foods': {},
  'bay valley foods': {},
  'coca-cola beverages florida': {},
  'coca-cola united': {},
  'crown cork and seal': {},
  'ferrara candy': {},
  'flagstone foods llc': {},
  'heartland coca-cola': {},
  'heartland foods': {},
  hellofresh: {},
  'high liner foods inc': {},
  ingredion: {},
  "ken's foods": {},
  'lassonde industries inc': {},
  'mount franklin foods': {},
  'phillips pet': {},
  'reyes logistics solutions, llc': {
    'glccb/rccb scheduling': {},
    'reyes beer division': {},
  },
  'swire coca-cola': {},
  'toys r us': {},
  'ulta beauty': {},
  'winland foods': {
    'winland foods 3rd party appt sched': {},
    'winland foods 3rd party appt sched - int': {},
  },
};

interface E2openInputsWithoutLoad {
  freightTrackingID: string;
  startDateTime: Date;
  endDateTime: Date;
  shouldRequestLumper: boolean;
  apptNote: string;
  requestedDateTime: Date;
  appointmentType: 'normal' | '3rd_party';
  company: string;
  operation: string;
  zipCode: string;
  city: string;
  state: string;
  country: string;
}

type E2openTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<E2openInputsWithoutLoad>;
};
const E2openTextInput = (props: E2openTextInputProps) => (
  <RHFTextInput {...props} />
);

interface E2openFormProps {
  type: StopTypes;
  load: NormalizedLoad;
}

export function E2openForm({ type, load }: E2openFormProps) {
  const { toast } = useToast();
  const scrollResultsIntoViewRef = useRef<HTMLDivElement>(null);

  const [isValidatingPRONum, setIsValidatingPRONum] = useState(false);
  const [isValidPRONum, setIsValidPRONum] = useState(false);
  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isFetchingWarehouseDetails, setIsFetchingWarehouseDetails] =
    useState(false);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState(false);
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false);
  const [warehouseDetails, setWarehouseDetails] =
    useState<Maybe<OrderedSlots>>(null);
  const [orderedSlots, setOrderedSlots] = useState<Maybe<OrderedSlots>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [liveAppointmentsAvailable, setLiveAppointmentsAvailable] =
    useState(true);

  const formMethods = useForm<E2openInputsWithoutLoad>({
    defaultValues: {
      freightTrackingID: load.freightTrackingID || '',
      startDateTime: new Date(),
      endDateTime: dayjs().add(7, 'days').toDate(),
      shouldRequestLumper: false,
      apptNote: '',
      requestedDateTime: new Date(),
      appointmentType: 'normal',
      company: '',
      operation: '',
      zipCode: load.pickup.zipCode || '',
      city: load.pickup.city || '',
      state: load.pickup.state || '',
      country: load.pickup.country || '',
    },
  });

  const { control, handleSubmit, getValues, watch } = formMethods;

  // Watch for changes in form values
  const appointmentType = watch('appointmentType');
  const company = watch('company');

  // Get available operations for selected company
  const getAvailableOperations = (company: string) => {
    const companyData =
      COMPANY_WISE_LOCATORS[company as keyof typeof COMPANY_WISE_LOCATORS];
    if (
      companyData &&
      typeof companyData === 'object' &&
      Object.keys(companyData).length > 0
    ) {
      return Object.keys(companyData);
    }
    return [];
  };

  useEffect(() => {
    if (isValidPRONum) {
      scrollResultsIntoViewRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isValidPRONum]);

  const validatePRONumber = async (proNumber: string) => {
    setIsValidatingPRONum(true);

    try {
      const formValues = getValues();
      const additionalParams: any = {};
      additionalParams.appointmentType = formValues.appointmentType;
      if (formValues.appointmentType === '3rd_party') {
        additionalParams.operation = formValues.operation;
        additionalParams.company = formValues.company;
      }
      const validateRes = await validateAppt(
        'placeholder',
        SchedulingPortals.E2open,
        [proNumber],
        {
          requestType: type,
          zipCode: formValues.zipCode,
          city: formValues.city,
          state: formValues.state,
          country: formValues.country,
          ...additionalParams,
        }
      );

      if (validateRes.isOk()) {
        const { validatedPONumbers: validatedPRONumbers } = validateRes.value;

        if (validatedPRONumbers.length === 0) {
          toast({
            description: 'No PRO number to validate.',
            variant: 'default',
          });
          setIsValidatingPRONum(false);
          return false;
        }

        const proValidation = validatedPRONumbers[0];

        if (!proValidation.isValid) {
          if (proValidation.error?.includes('FCFS scheduling')) {
            setLiveAppointmentsAvailable(false);
            setIsValidPRONum(true);
            toast({
              description:
                'Live appointments not available. Please use Option 1 to submit an appointment request.',
              variant: 'default',
            });
            return true;
          } else if (
            proValidation.error?.includes('No pickup/dropoff locations found')
          ) {
            toast({
              description: 'Invalid PRO number: No locations found.',
              variant: 'destructive',
            });
            setIsValidatingPRONum(false);
            return false;
          } else {
            toast({
              description: proValidation.error || 'PRO number is invalid.',
              variant: 'destructive',
            });
            setIsValidatingPRONum(false);
            return false;
          }
        } else {
          setIsValidPRONum(true);
          setLiveAppointmentsAvailable(true);
        }

        const defaultStart = dayjs().startOf('day');
        const defaultEnd = dayjs().add(7, 'days').endOf('day');
        setIsFetchingWarehouseDetails(true);
        setIsLoadingSlots(true);

        getOpenApptSlots({
          source: SchedulingPortals.E2open,
          freightTrackingID: proNumber,
          loadTypeID: 'placeholder',
          requestType: type,
          startDateTime: defaultStart,
          endDateTime: defaultEnd,
          zipCode: formValues.zipCode,
          city: formValues.city,
          state: formValues.state,
          country: formValues.country,
          ...additionalParams,
        })
          .then((slotsRes) => {
            if (slotsRes.isOk()) {
              setWarehouseDetails(slotsRes.value);
              setOrderedSlots(slotsRes.value);
            } else {
              setLiveAppointmentsAvailable(false);
              if (
                slotsRes.error.message ===
                'Live appointments are not available at this time'
              ) {
                setLiveAppointmentsAvailable(false);
                toast({
                  description:
                    'Option 2 (Live Appointments) is not available for ' +
                    'this warehouse. Please use Option 1 to submit an ' +
                    'appointment request.',
                  variant: 'default',
                });
              }
            }
          })
          .catch(() => {
            setLiveAppointmentsAvailable(false);
          })
          .finally(() => {
            setIsFetchingWarehouseDetails(false);
            setIsLoadingSlots(false);
          });

        setIsValidatingPRONum(false);
        return true;
      } else {
        toast({
          description: 'Failed to validate PRO number.',
          variant: 'destructive',
        });
        setIsValidatingPRONum(false);
        return false;
      }
    } catch {
      toast({
        description: 'An error occurred during validation.',
        variant: 'destructive',
      });
      setIsValidatingPRONum(false);
      return false;
    }
  };

  const loadAvailableSlots = async () => {
    const formValues = getValues();
    const defaultStart = dayjs().startOf('day');
    const defaultEnd = dayjs().add(7, 'days').endOf('day');
    const start = formValues.startDateTime
      ? dayjs(formValues.startDateTime)
      : defaultStart;
    const end = formValues.endDateTime
      ? dayjs(formValues.endDateTime)
      : defaultEnd;
    const today = dayjs().startOf('day');

    if (start && !end) {
      toast({
        description: 'End date is required when start date is provided',
        variant: 'destructive',
      });
      return;
    }

    if (start && end && start.isAfter(end)) {
      toast({
        description: formValues.endDateTime
          ? 'Start date cannot be after end date'
          : 'Please select an end date',
        variant: 'destructive',
      });
      return;
    }

    if (start?.isBefore(today, 'day') || end?.isBefore(today, 'day')) {
      toast({
        description: 'Appointment dates cannot be in the past',
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingSlots(true);

    // Prepare additional parameters for 3rd party requests
    const additionalParams: any = {};
    additionalParams.appointmentType = formValues.appointmentType;
    if (formValues.appointmentType === '3rd_party') {
      additionalParams.operation = formValues.operation;
      additionalParams.company = formValues.company;
    }

    try {
      const res = await getOpenApptSlots({
        source: SchedulingPortals.E2open,
        freightTrackingID: formValues.freightTrackingID,
        loadTypeID: 'placeholder',
        requestType: type,
        startDateTime: start,
        endDateTime: end,
        zipCode: formValues.zipCode,
        city: formValues.city,
        state: formValues.state,
        country: formValues.country,
        ...additionalParams,
      });

      if (res.isOk()) {
        setOrderedSlots(res.value);
        setLiveAppointmentsAvailable(true);

        if (!res.value?.slots || Object.keys(res.value.slots).length === 0) {
          toast({
            description: 'No available appointment times found.',
            variant: 'default',
          });
          return false;
        }
        return true;
      } else {
        if (
          res.error.message ===
          'Live appointments are not available at this time'
        ) {
          setLiveAppointmentsAvailable(false);
          toast({
            description:
              'Option 2 (Live Appointments) is not available for ' +
              'this warehouse. Please use Option 1 to submit an ' +
              'appointment request.',
            variant: 'default',
          });
        } else {
          toast({
            description:
              res.error.message || 'Failed to load available appointments.',
            variant: 'destructive',
          });
        }
        return false;
      }
    } catch {
      toast({
        description: 'Failed to load available appointments.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoadingSlots(false);
    }
  };

  const handleValidateAppointment = async () => {
    const { freightTrackingID } = getValues();
    await validatePRONumber(freightTrackingID);
  };

  const handleConfirmAppointment = async () => {
    if (!selectedSlot || !orderedSlots) {
      toast({
        description: 'Please select an available time slot',
        variant: 'default',
      });
      return;
    }

    const formValues = getValues();

    // Prepare additional parameters for 3rd party requests
    const additionalParams: any = {};

    additionalParams.appointmentType = formValues.appointmentType;
    if (formValues.appointmentType === '3rd_party') {
      additionalParams.operation = formValues.operation;
      additionalParams.company = formValues.company;
    }

    setIsLoadingConfirm(true);

    try {
      let trailerType = '';
      if (load.specifications && load.specifications.transportType) {
        trailerType = load.specifications?.transportType
          ?.toLowerCase()
          .split(' ')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      }

      const res = await confirmSlotAppt({
        source: SchedulingPortals.E2open,
        isTMSLoad: false,
        isV2: true,
        stopType: type,
        start: selectedSlot.startTime,
        loadTypeId: 'placeholder',
        warehouseID: '-',
        warehouseTimezone: 'UTC',
        dockId: 'placeholder',
        loadID: load.ID!,
        freightTrackingId: formValues.freightTrackingID,
        appointments: [
          {
            start: selectedSlot.startTime,
            freightTrackingId: formValues.freightTrackingID,
            zipCode: formValues.zipCode,
            city: formValues.city,
            state: formValues.state,
            country: formValues.country,
          },
        ],
        requestType: type,
        trailerType: trailerType,
        ...additionalParams,
      });

      if (res.isOk()) {
        setApptConfirmationNumber(res.value.ConfirmationNo);
        toast({
          description: 'Appointment confirmed successfully!',
          variant: 'success',
        });
      } else {
        if (res.error.message === 'Conflicting Appointments') {
          toast({
            title: 'Conflicting Appointments',
            description:
              "Make sure you don't have an existing appointment for this load.",
            variant: 'destructive',
          });
        } else {
          toast({
            description: res.error.message || 'Failed to confirm appointment.',
            variant: 'destructive',
          });
        }
      }
    } catch {
      toast({
        description: 'An error occurred while confirming the appointment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingConfirm(false);
    }
  };

  const handleSubmitAppointment = async () => {
    setIsLoadingSubmit(true);

    const formValues = getValues();
    const defaultStart = dayjs().startOf('day');
    const requestedDateTime = formValues.requestedDateTime
      ? dayjs(formValues.requestedDateTime)
      : defaultStart;

    const request = {
      warehouseId: '',
      source: SchedulingPortals.E2open,
      // treat freightTrackingID aka PRO # as PO # here
      poNumbers: [formValues.freightTrackingID],
      lumperRequested: false,
      note: '',
      requestedDateTime: requestedDateTime,
      timePreference: undefined,
    };

    try {
      const submitRes = await submitAppt(request);

      if (submitRes.isOk()) {
        toast({
          description: 'Your appointment has been submitted.',
          variant: 'success',
        });
      } else {
        toast({
          description:
            submitRes.error.message ||
            'An error occurred while submitting your appointment.',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        description: 'An error occurred while submitting your appointment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingSubmit(false);
    }
  };

  const onInvalid: SubmitErrorHandler<E2openInputsWithoutLoad> = async () => {
    toast({
      description: 'Some fields are invalid.',
      variant: 'destructive',
    });
  };

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <form
          onSubmit={handleSubmit(handleSubmitAppointment, onInvalid)}
          className='flex flex-col gap-4 mt-4 mx-0 w-full'
        >
          <p className='text-xs text-grayscale-content-description mb-2 italic'>
            Note: This scheduling form only works for E2open actions (not
            compatible with EDI)
          </p>

          <div className='flex flex-col gap-4'>
            <E2openTextInput
              name='freightTrackingID'
              label='PRO ID'
              placeholder='Enter PRO ID'
              required
            />

            {/* Address Fields */}
            <E2openTextInput
              name='zipCode'
              label='Zip Code'
              placeholder='Enter zip code'
              required
            />

            <E2openTextInput
              name='city'
              label='City'
              placeholder='Enter city'
            />

            <E2openTextInput
              name='state'
              label='State'
              placeholder='Enter state'
            />

            <E2openTextInput
              name='country'
              label='Country'
              placeholder='Enter country'
            />

            {/* Appointment Type Dropdown */}
            <div className='flex flex-col gap-2'>
              <Label htmlFor='appointmentType' name='appointmentType'>
                Appointment Type *
              </Label>
              <Select
                value={appointmentType}
                onValueChange={(value: 'normal' | '3rd_party') => {
                  formMethods.setValue('appointmentType', value);
                  // Reset company and operation when changing appointment type
                  if (value === 'normal') {
                    formMethods.setValue('company', '');
                    formMethods.setValue('operation', '');
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder='Select appointment type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='normal'>NORMAL</SelectItem>
                  <SelectItem value='3rd_party'>3RD PARTY</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Company Name Dropdown - only show for 3rd party */}
            {appointmentType === '3rd_party' && (
              <div className='flex flex-col gap-2'>
                <Label htmlFor='company' name='company'>
                  Company Name *
                </Label>
                <Select
                  value={company}
                  onValueChange={(value: string) => {
                    formMethods.setValue('company', value);
                    // Reset operation when changing company
                    formMethods.setValue('operation', '');
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select company' />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.keys(COMPANY_WISE_LOCATORS).map((company) => (
                      <SelectItem key={company} value={company}>
                        {company.toUpperCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Operation Dropdown - only show for 3rd party with company selected and has operations */}
            {appointmentType === '3rd_party' &&
              company &&
              getAvailableOperations(company).length > 0 && (
                <div className='flex flex-col gap-2'>
                  <Label htmlFor='operation' name='operation'>
                    Operation *
                  </Label>
                  <Select
                    value={watch('operation')}
                    onValueChange={(value: string) => {
                      formMethods.setValue('operation', value);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select operation' />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableOperations(company).map((operation) => (
                        <SelectItem key={operation} value={operation}>
                          {operation.toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

            <Button
              buttonNamePosthog={ButtonNamePosthog.ValidateE2openPRONumber}
              className='w-full'
              type='button'
              disabled={isValidatingPRONum}
              onClick={handleValidateAppointment}
            >
              {isValidatingPRONum ? <ButtonLoader /> : 'Validate PRO ID'}
            </Button>

            {isValidPRONum && (
              <div
                className='flex flex-col gap-8 mt-4'
                ref={scrollResultsIntoViewRef}
              >
                {isFetchingWarehouseDetails && !warehouseDetails && (
                  <div className='p-4 text-left animate-slide-in'>
                    <div className='mb-6'>
                      <span className='text-sm font-medium text-gray-500 block mb-2'>
                        Warehouse
                      </span>
                      <p className='text-gray-500'>
                        Fetching warehouse details...
                      </p>
                    </div>
                    <Divider className='border border-grayscale-border-divider my-2' />
                  </div>
                )}
                {warehouseDetails && (
                  <div className='p-4 text-left animate-slide-in'>
                    <div className='mb-6'>
                      <span className='text-sm font-medium text-gray-500 block mb-2'>
                        Warehouse
                      </span>
                      <h2 className='text-lg font-semibold text-gray-900 mb-2'>
                        {warehouseDetails.warehouse.warehouseID}
                        {warehouseDetails.warehouse.city &&
                          warehouseDetails.warehouse.state && (
                            <>
                              <br />
                              {warehouseDetails.warehouse.city},{' '}
                              {warehouseDetails.warehouse.state}
                            </>
                          )}
                      </h2>
                      <p className='text-xs text-grayscale-content-description mt-1'>
                        All times are displayed in the warehouse's timezone.
                      </p>
                    </div>
                    <Divider className='border border-grayscale-border-divider my-2' />
                  </div>
                )}

                {/* Submit appointment request section - always visible */}
                <div className='rounded-lg border border-gray-200 p-4 bg-white shadow-sm'>
                  <div className='mb-6'>
                    <span className='text-sm font-medium text-gray-500 block mb-2'>
                      Option 1
                    </span>
                    <h2 className='text-lg font-semibold text-gray-900 mb-2'>
                      Submit Appointment Request
                    </h2>
                    <p className='text-sm text-gray-500 mt-1'>
                      Submit a request for review. You will be notified once
                      approved.
                    </p>
                  </div>

                  <div className='space-y-6 py-4'>
                    <div className='space-y-2'>
                      <DateTimeInput
                        control={control}
                        name='requestedDateTime'
                        label='Requested Date'
                        preventNormalizedLabelTZ={true}
                        hideAIHint={true}
                      />
                    </div>
                  </div>

                  <Button
                    buttonNamePosthog={ButtonNamePosthog.SubmitE2openAppt}
                    type='submit'
                    className='mt-4 w-full'
                    disabled={isLoadingSubmit}
                  >
                    {isLoadingSubmit ? <ButtonLoader /> : 'Submit'}
                  </Button>
                </div>

                {/* Live Appointment Section */}
                <div
                  className={cn(
                    'rounded-lg border border-gray-200 p-4 bg-white shadow-sm',
                    !liveAppointmentsAvailable &&
                      'opacity-50 pointer-events-none'
                  )}
                >
                  <div className='mb-6'>
                    <span className='text-sm font-medium text-gray-500 block mb-2'>
                      Option 2
                    </span>
                    <h2 className='text-lg font-semibold text-gray-900 mb-2'>
                      Book Live Appointment
                    </h2>
                    <p className='text-sm text-gray-500'>
                      {liveAppointmentsAvailable
                        ? 'Book an appointment immediately from available time slots.'
                        : 'Live appointments are not available for this PRO ID. Please use Option 1 to submit an appointment request.'}
                    </p>
                  </div>

                  <div className='space-y-4'>
                    <div>
                      <DateTimeInput
                        control={control}
                        name='startDateTime'
                        label='Search From'
                        preventNormalizedLabelTZ={true}
                        hideAIHint={true}
                        hideTimePicker={true}
                      />
                    </div>

                    <div>
                      <DateTimeInput
                        control={control}
                        name='endDateTime'
                        label='Search To'
                        preventNormalizedLabelTZ={true}
                        hideAIHint={true}
                        hideTimePicker={true}
                      />
                    </div>

                    <Button
                      type='button'
                      className='w-full'
                      disabled={isLoadingSlots || !liveAppointmentsAvailable}
                      buttonNamePosthog={ButtonNamePosthog.FindOpenApptSlots}
                      onClick={loadAvailableSlots}
                    >
                      {isLoadingSlots ? (
                        <ButtonLoader />
                      ) : (
                        ButtonText.GetOpenApptSlots
                      )}
                    </Button>
                  </div>

                  {orderedSlots && (
                    <div className='mt-4'>
                      {Object.entries(orderedSlots.slots).map(
                        ([date, slots]) => (
                          <div key={date}>
                            <h3 className='text-grayscale-content-description font-bold uppercase text-sm mt-4'>
                              {date}
                            </h3>
                            <div className='grid grid-cols-3 gap-1 mt-2 mx-0 w-full'>
                              {slots.map((slot, idx) => (
                                <button
                                  type='button'
                                  key={idx}
                                  onClick={() =>
                                    setSelectedSlot(
                                      selectedSlot === slot ? null : slot
                                    )
                                  }
                                  className={cn(
                                    'bg-white border border-grayscale-border-outline p-1 py-2 rounded cursor-pointer text-sm',
                                    selectedSlot === slot &&
                                      'bg-orange-pressed border-orange-pressed text-white'
                                  )}
                                >
                                  {dayjs.utc(slot.startTime).format('HH:mm')}
                                </button>
                              ))}
                            </div>
                          </div>
                        )
                      )}

                      {selectedSlot && (
                        <div className='mt-4 text-grayscale-content-description text-left text-sm'>
                          <p className='my-1 font-bold'>Selected Slot:</p>
                          <p className='mb-2'>
                            {dayjs
                              .utc(selectedSlot.startTime)
                              .format('MMM D, YYYY, HH:mm')}
                          </p>

                          {warehouseDetails && (
                            <>
                              <p className='my-1 font-bold'>
                                Warehouse Details:
                              </p>
                              <p className='mb-2'>
                                {warehouseDetails.warehouse.warehouseID}
                                {warehouseDetails.warehouse.city &&
                                  warehouseDetails.warehouse.state && (
                                    <>
                                      <br />
                                      {warehouseDetails.warehouse.city},{' '}
                                      {warehouseDetails.warehouse.state}
                                    </>
                                  )}
                              </p>
                            </>
                          )}

                          {apptConfirmationNumber ? (
                            <div className='whitespace-pre-wrap my-3 rounded py-3 text-grayscale-content-1 px-4 bg-green-bg'>
                              <p className='mb-2'>Appointment confirmed 🎉</p>
                              <p className='mb-4 text-[14px]'>
                                <b className='text-[14px]'>
                                  E2open Confirmation #:{' '}
                                </b>
                                {apptConfirmationNumber}
                              </p>
                            </div>
                          ) : (
                            <Button
                              buttonNamePosthog={
                                ButtonNamePosthog.ConfirmSlotApptScheduling
                              }
                              className='mt-2 w-full'
                              onClick={handleConfirmAppointment}
                              disabled={isLoadingConfirm}
                            >
                              {isLoadingConfirm ? (
                                <ButtonLoader />
                              ) : (
                                'Confirm appointment'
                              )}
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </form>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
