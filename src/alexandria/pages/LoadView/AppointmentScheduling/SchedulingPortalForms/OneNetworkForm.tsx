import { useState } from 'react';
import { Field<PERSON>ath, FormProvider, useForm } from 'react-hook-form';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import { Button } from 'components/Button';
import DateTimeInput from 'components/input/DateTimeInput';
import { RHFTextInput } from 'components/input/RHFTextInput';
import ButtonLoader from 'components/loading/ButtonLoader';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { useToast } from 'hooks/useToaster';
import { confirmSlotAppt } from 'lib/api/confirmSlotAppt';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import {
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import ButtonText from 'types/enums/ButtonText';
import { cn } from 'utils/shadcn';

dayjs.extend(utc);

interface OneNetworkInputsWithoutLoad {
  freightTrackingID: string;
  startDateTime: Date;
  endDateTime: Date;
  phone: string;
  email: string;
  requestedDateTime: Date;
}

type OneNetworkTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<OneNetworkInputsWithoutLoad>;
};
const OneNetworkTextInput = (props: OneNetworkTextInputProps) => (
  <RHFTextInput {...props} />
);

interface OneNetworkFormProps {
  type: StopTypes;
  load: NormalizedLoad;
}

export function OneNetworkForm({ type, load }: OneNetworkFormProps) {
  const { toast } = useToast();

  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false);
  const [orderedSlots, setOrderedSlots] = useState<Maybe<OrderedSlots>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [liveAppointmentsAvailable, setLiveAppointmentsAvailable] =
    useState(true);

  const formMethods = useForm<OneNetworkInputsWithoutLoad>({
    defaultValues: {
      freightTrackingID: load.freightTrackingID || '',
      startDateTime: new Date(),
      endDateTime: dayjs().add(7, 'days').toDate(),
      phone: '',
      email: '',
      requestedDateTime: new Date(),
    },
  });

  const { control, getValues } = formMethods;

  const loadAvailableSlots = async () => {
    const formValues = getValues();
    const defaultStart = dayjs().startOf('day');
    const defaultEnd = dayjs().add(7, 'days').endOf('day');
    const start = formValues.startDateTime
      ? dayjs(formValues.startDateTime)
      : defaultStart;
    const end = formValues.endDateTime
      ? dayjs(formValues.endDateTime)
      : defaultEnd;
    const today = dayjs().startOf('day');

    if (start && !end) {
      toast({
        description: 'End date is required when start date is provided',
        variant: 'destructive',
      });
      return;
    }

    if (start && end && start.isAfter(end)) {
      toast({
        description: formValues.endDateTime
          ? 'Start date cannot be after end date'
          : 'Please select an end date',
        variant: 'destructive',
      });
      return;
    }

    if (start?.isBefore(today, 'day') || end?.isBefore(today, 'day')) {
      toast({
        description: 'Appointment dates cannot be in the past',
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingSlots(true);

    try {
      const res = await getOpenApptSlots({
        source: SchedulingPortals.OneNetwork,
        freightTrackingID: formValues.freightTrackingID,
        loadTypeID: 'placeholder',
        requestType: type,
        startDateTime: start,
        endDateTime: end,
      });

      if (res.isOk()) {
        setOrderedSlots(res.value);
        setLiveAppointmentsAvailable(true);

        if (!res.value?.slots || Object.keys(res.value.slots).length === 0) {
          toast({
            description: 'No available appointment times found.',
            variant: 'default',
          });
          return false;
        }
        return true;
      } else {
        toast({
          description:
            res.error.message || 'Failed to load available appointments.',
          variant: 'destructive',
        });
        return false;
      }
    } catch {
      toast({
        description: 'Failed to load available appointments.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoadingSlots(false);
    }
  };

  const handleConfirmAppointment = async () => {
    if (!selectedSlot || !orderedSlots) {
      toast({
        description: 'Please select an available time slot',
        variant: 'default',
      });
      return;
    }

    setIsLoadingConfirm(true);

    try {
      const res = await confirmSlotAppt({
        source: SchedulingPortals.OneNetwork,
        isTMSLoad: false,
        isV2: true,
        stopType: type,
        start: selectedSlot.startTime,
        loadTypeId: 'placeholder',
        warehouseID: '',
        warehouseTimezone: 'UTC',
        dockId: 'placeholder',
        loadID: load.ID!,
        freightTrackingId: getValues().freightTrackingID,
        requestType: type,
        phone: getValues().phone,
        email: getValues().email,
      });

      if (res.isOk()) {
        setApptConfirmationNumber(res.value.ConfirmationNo);
        toast({
          description: 'Appointment confirmed successfully!',
          variant: 'success',
        });
      } else {
        if (res.error.message === 'Conflicting Appointments') {
          toast({
            title: 'Conflicting Appointments',
            description:
              "Make sure you don't have an existing appointment for this load.",
            variant: 'destructive',
          });
        } else {
          toast({
            description: res.error.message || 'Failed to confirm appointment.',
            variant: 'destructive',
          });
        }
      }
    } catch {
      toast({
        description: 'An error occurred while confirming the appointment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingConfirm(false);
    }
  };

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <form className='flex flex-col gap-4 mt-4 mx-0 w-full'>
          <div className='flex flex-col gap-4'>
            <div className='flex flex-col gap-8 mt-4'>
              <div
                className={cn(
                  'rounded-lg border border-gray-200 p-4 bg-white shadow-sm',
                  !liveAppointmentsAvailable && 'opacity-50 pointer-events-none'
                )}
              >
                <div className='mb-6'>
                  <h2 className='text-lg font-semibold text-gray-900 mb-2'>
                    Book Live Appointment
                  </h2>
                  <p className='text-sm text-gray-500'>
                    {liveAppointmentsAvailable
                      ? 'Book an appointment immediately from available time slots.'
                      : 'Live appointments are not available for this PRO ID.'}
                  </p>
                </div>

                <div className='space-y-4'>
                  <OneNetworkTextInput
                    name='freightTrackingID'
                    label='PRO ID'
                    placeholder='Enter PRO ID'
                    required
                  />

                  <OneNetworkTextInput
                    name='phone'
                    label='Phone Number'
                    placeholder='Enter phone number'
                    required
                  />

                  <OneNetworkTextInput
                    name='email'
                    label='Email'
                    placeholder='Enter email'
                    required
                  />

                  <DateTimeInput
                    control={control}
                    name='startDateTime'
                    label='Search From'
                    preventNormalizedLabelTZ={true}
                    hideAIHint={true}
                    hideTimePicker={true}
                  />

                  <DateTimeInput
                    control={control}
                    name='endDateTime'
                    label='Search To'
                    preventNormalizedLabelTZ={true}
                    hideAIHint={true}
                    hideTimePicker={true}
                  />

                  <Button
                    type='button'
                    className='w-full'
                    disabled={isLoadingSlots || !liveAppointmentsAvailable}
                    buttonNamePosthog={ButtonNamePosthog.FindOpenApptSlots}
                    onClick={loadAvailableSlots}
                  >
                    {isLoadingSlots ? (
                      <ButtonLoader />
                    ) : (
                      ButtonText.GetOpenApptSlots
                    )}
                  </Button>
                </div>

                {orderedSlots && (
                  <div className='mt-4'>
                    {Object.entries(orderedSlots.slots).map(([date, slots]) => (
                      <div key={date}>
                        <h3 className='text-grayscale-content-description font-bold uppercase text-sm mt-4'>
                          {date}
                        </h3>
                        <div className='grid grid-cols-3 gap-1 mt-2 mx-0 w-full'>
                          {slots.map((slot, idx) => (
                            <button
                              type='button'
                              key={idx}
                              onClick={() =>
                                setSelectedSlot(
                                  selectedSlot === slot ? null : slot
                                )
                              }
                              className={cn(
                                'bg-white border border-grayscale-border-outline p-1 py-2 rounded cursor-pointer text-sm',
                                selectedSlot === slot &&
                                  'bg-orange-pressed border-orange-pressed text-white'
                              )}
                            >
                              {dayjs.utc(slot.startTime).format('HH:mm')}
                            </button>
                          ))}
                        </div>
                      </div>
                    ))}

                    {selectedSlot && (
                      <div className='mt-4 text-grayscale-content-description text-left text-sm'>
                        <p className='my-1 font-bold'>Selected Slot:</p>
                        <p className='mb-2'>
                          {dayjs
                            .utc(selectedSlot.startTime)
                            .format('MMM D, YYYY, HH:mm')}
                        </p>

                        {apptConfirmationNumber ? (
                          <div className='whitespace-pre-wrap my-3 rounded py-3 text-grayscale-content-1 px-4 bg-green-bg'>
                            <p className='mb-2'>Appointment confirmed 🎉</p>
                            <p className='mb-4 text-[14px]'>
                              <b className='text-[14px]'>
                                OneNetwork Confirmation #:{' '}
                              </b>
                              {apptConfirmationNumber}
                            </p>
                          </div>
                        ) : (
                          <Button
                            buttonNamePosthog={
                              ButtonNamePosthog.ConfirmSlotApptScheduling
                            }
                            className='mt-2 w-full'
                            onClick={handleConfirmAppointment}
                            disabled={isLoadingConfirm}
                          >
                            {isLoadingConfirm ? (
                              <ButtonLoader />
                            ) : (
                              'Confirm appointment'
                            )}
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </form>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
