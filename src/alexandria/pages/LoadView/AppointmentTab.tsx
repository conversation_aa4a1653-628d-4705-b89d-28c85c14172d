import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import useLogPostHogPageView from 'hooks/useLogPostHogPageView';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import Pageview from 'types/enums/Pageview';

import { AppointmentEditor } from './AppointmentScheduling/AppointmentEditor';

dayjs.extend(utc);
dayjs.extend(timezone);

type AppointmentSchedulingSectionProps = {
  normalizedLoad: NormalizedLoad;
  loadPickupWarehouse: Maybe<Warehouse>;
  loadDropoffWarehouse: Maybe<Warehouse>;
};

export default function AppointmentSchedulingSection({
  normalizedLoad: load,
  loadPickupWarehouse,
  loadDropoffWarehouse,
}: AppointmentSchedulingSectionProps) {
  useLogPostHogPageView(Pageview.AppointmentScheduling, {
    service_id: load.serviceID,
    load_id: load.ID,
    freightTrackingID: load.freightTrackingID,
  });

  return (
    <div className='px-4'>
      <AppointmentEditor
        normalizedLoad={load}
        loadPickupWarehouse={loadPickupWarehouse}
        loadDropoffWarehouse={loadDropoffWarehouse}
      />
    </div>
  );
}
