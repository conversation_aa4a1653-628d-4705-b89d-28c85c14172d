import { Order } from 'types/Order';

interface OrderTabProps {
  order: Order;
}

export default function OrderTab({ order }: OrderTabProps) {
  return (
    <div className='p-4'>
      <div className='border rounded-lg p-4 bg-white shadow-sm'>
        <div className='text-xs text-gray-500 mb-2'>
          Debug: OrderTab received order with ID: {order.ID}, externalOrderId:{' '}
          {order.externalOrderId}
        </div>

        <div className='flex justify-between items-start mb-4'>
          <div>
            <h3 className='font-medium text-lg'>
              Order #{order.externalOrderId}
            </h3>
            <p className='text-sm text-gray-500'>Type: {order.type}</p>
          </div>
          <div className='text-right'>
            <span
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                order.status === 'COMPLETED'
                  ? 'bg-green-100 text-green-800'
                  : order.status === 'IN_PROGRESS'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
              }`}
            >
              {order.status}
            </span>
            <p className='text-sm text-gray-500 mt-1'>Mode: {order.mode}</p>
          </div>
        </div>

        <div className='grid grid-cols-2 gap-6 mb-4 mx-0 w-full'>
          <div className='space-y-4'>
            <div>
              <h4 className='font-medium text-sm text-gray-700 mb-1'>
                Pickup Location
              </h4>
              <div className='text-sm'>
                <p className='font-medium'>{order.pickup.name}</p>
                <p>{order.pickup.addressLine1}</p>
                {order.pickup.addressLine2 && (
                  <p>{order.pickup.addressLine2}</p>
                )}
                <p>{`${order.pickup.city}, ${order.pickup.state} ${order.pickup.zipcode}`}</p>
              </div>
            </div>
            <div>
              <h4 className='font-medium text-sm text-gray-700 mb-1'>
                Delivery Location
              </h4>
              <div className='text-sm'>
                <p className='font-medium'>{order.consignee.name}</p>
                <p>{order.consignee.addressLine1}</p>
                {order.consignee.addressLine2 && (
                  <p>{order.consignee.addressLine2}</p>
                )}
                <p>{`${order.consignee.city}, ${order.consignee.state} ${order.consignee.zipcode}`}</p>
              </div>
            </div>
          </div>

          <div className='space-y-4'>
            <div>
              <h4 className='font-medium text-sm text-gray-700 mb-1'>
                Specifications
              </h4>
              <div className='text-sm space-y-1'>
                <p>
                  Weight: {order.specifications.totalWeight.val}{' '}
                  {order.specifications.totalWeight.unit}
                </p>
                <p>Pieces: {order.pieceCount}</p>
                {order.specifications.isRefrigerated && (
                  <p className='text-blue-600'>
                    Temperature: {order.specifications.minTempFahrenheit}
                    °F - {order.specifications.maxTempFahrenheit}°F
                  </p>
                )}
                {order.isHazmat && <p className='text-red-600'>Hazmat</p>}
              </div>
            </div>
            <div>
              <h4 className='font-medium text-sm text-gray-700 mb-1'>
                Rate Information
              </h4>
              <div className='text-sm space-y-1'>
                <p>
                  Total Charge: {order.rateData.customerTotalCharge.val}{' '}
                  {order.rateData.customerTotalCharge.unit}
                </p>
              </div>
            </div>
          </div>
        </div>

        {order.poNums && (
          <div className='mt-4 pt-4 border-t'>
            <h4 className='font-medium text-sm text-gray-700 mb-2'>
              References
            </h4>
            <div className='flex flex-wrap gap-2'>
              <span className='text-xs bg-gray-100 px-2 py-1 rounded'>
                PO: {order.poNums}
              </span>
              <span className='text-xs bg-gray-100 px-2 py-1 rounded'>
                Order ID: {order.orderTrackingId}
              </span>
            </div>
          </div>
        )}

        {order.loadId && (
          <div className='mt-4 pt-4 border-t'>
            <h4 className='font-medium text-sm text-gray-700 mb-2'>
              Load Association
            </h4>
            <p className='text-sm text-gray-600'>
              This order is associated with load ID: {order.loadId}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
