import { createContext, useCallback, useContext, useState } from 'react';

import putViewedLoadHistory from '../lib/api/putViewedLoadHistory';

export type TabType = 'load' | 'order';

export interface TabItem {
  id: string;
  type: TabType;
}

export const getTabKey = (tab: TabItem): string => `${tab.id}-${tab.type}`;

export type AddTabFn = (id: string, type: TabType) => Promise<void>;

type LoadSearchContextType = {
  proNumberInput: string;
  setProNumberInput: (value: string) => void;
  addTab: AddTabFn;
  tabs: TabItem[];
  setTabs: (tabs: TabItem[] | ((prev: TabItem[]) => TabItem[])) => void;
  activeTabKey: string;
  setActiveTabKey: (key: string) => void;
};

const noop: AddTabFn = async () => {};

export const LoadSearchContext = createContext<LoadSearchContextType>({
  proNumberInput: '',
  setProNumberInput: () => {},
  addTab: noop,
  tabs: [],
  setTabs: () => {},
  activeTabKey: '',
  setActiveTabKey: () => {},
});

type LoadSearchProviderProps = {
  children: React.ReactNode;
  initialFreightTrackingIDs?: string[];
};

export function LoadSearchProvider({
  children,
  initialFreightTrackingIDs,
}: LoadSearchProviderProps) {
  const [proNumberInput, setProNumberInput] = useState('');
  const [tabs, setTabs] = useState<TabItem[]>(
    (initialFreightTrackingIDs ?? []).map((id) => ({ id, type: 'load' }))
  );
  const [activeTabKey, setActiveTabKey] = useState<string>(
    initialFreightTrackingIDs?.[0]
      ? getTabKey({ id: initialFreightTrackingIDs[0], type: 'load' })
      : ''
  );

  const addTab = useCallback(async (id: string, type: TabType) => {
    const trimmedId = id.trim();
    if (!trimmedId) return;

    const newTab: TabItem = { id: trimmedId, type };

    setTabs((prevTabs) => {
      const existingTab = prevTabs.find(
        (tab) => tab.id === newTab.id && tab.type === newTab.type
      );
      if (existingTab) {
        setActiveTabKey(getTabKey(existingTab));
        return prevTabs;
      }
      setActiveTabKey(getTabKey(newTab));
      return [...prevTabs, newTab];
    });

    setProNumberInput('');

    // Only track viewed history for loads
    if (type === 'load') {
      try {
        const res = await putViewedLoadHistory(trimmedId);
        if (!res.isOk()) {
          console.error('putViewedLoadHistory response not OK:', res);
        }
      } catch (error) {
        console.error('Error updating viewed loads:', error);
      }
    }
  }, []);

  return (
    <LoadSearchContext.Provider
      value={{
        proNumberInput,
        setProNumberInput,
        addTab,
        tabs,
        setTabs,
        activeTabKey,
        setActiveTabKey,
      }}
    >
      {children}
    </LoadSearchContext.Provider>
  );
}

export function useLoadSearch() {
  const context = useContext(LoadSearchContext);

  if (context === undefined) {
    throw new Error('useLoadSearch must be used within a LoadSearchProvider');
  }
  return context;
}
