import { createContext } from 'react';

import { ProfitType } from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { SchedulingPortals } from 'types/Appointment';
import { Maybe } from 'types/UtilityTypes';
import { Quoting, TMS } from 'types/enums/Integrations';

export type QuickQuoteConfig = {
  id: number;
  defaultPercentMargin: number;
  defaultFlatMargin: number;
  defaultTransportType: string;
  otherTransportTypes?: string[];
  defaultMarginType: string;
  lowConfidenceThreshold: number;
  mediumConfidenceThreshold: number;
  // If false, FE buttons will input quote into form but not submit it. User must submit manually
  isSubmitOnPortalEnabled: boolean;
};

export type ServiceFeatureConfigurations = {
  defaultPriceMargin: Maybe<number>;
  defaultPriceMarginType: Maybe<ProfitType>;
};

export type Service = {
  serviceID: number;
  tmsIntegrations: IntegrationCore[];
  quotingIntegrations: IntegrationCore[];
  schedulerIntegrations: IntegrationCore[];
  configurations: ServiceFeatureConfigurations;
  serviceFeaturesEnabled: ServiceFeaturesListType;
  quickQuoteConfig?: QuickQuoteConfig;
  carrierQuoteConfig?: Maybe<CarrierQuoteConfig>;
};

export type IntegrationCore = {
  id: number;
  name: TMS | Quoting | SchedulingPortals;
  tenant: string;
  featureFlags: {
    isCarrierAssignmentDisabled: boolean;
    isOnlyCityStateRequired: boolean;
  };
};

export type ServiceFeaturesListType = {
  isLoadViewEnabled: boolean; // Load View
  isQuoteViewEnabled: boolean;

  // Load View Tabs (Load Info is on by default)
  isAppointmentSchedulingEnabled: boolean;
  isTrackAndTraceEnabled: boolean;
  isCarrierVerificationEnabled: boolean;

  // Quote View Tabs
  isQuickQuoteEnabled: boolean;
  isCarrierNetworkQuotingEnabled: boolean;
  isLoadBuildingEnabled: boolean;
  isTruckListEnabled: boolean;

  // Load View - Load Info Tab sub-sections
  // Allows user to edit TMS object on Turvo instead of via Drumkit
  isTurvoSectionLinksEnabled: boolean;
  // Allows user to assign operator to load on Drumkit; TMS integration must implement this
  isOperatorEnabled: boolean;
  isOrderEnabled: boolean;
  // Track & Trace sub-sections / sub-features
  isCheckCallNotesEnabled: boolean;
  isExceptionsEnabled: boolean;
  isCheckCallCarrierSOPEnabled: boolean;
  isCheckCallShipperSOPEnabled: boolean;
  isCarrierEmailOutboxEnabled: boolean;

  // isCheckCallSuggestionsEnabled: boolean;
  isCarrierInfoSuggestionsEnabled: boolean;

  // Appointment Scheduling sub-sections / sub-features
  isAppointmentEmailingEnabled: boolean;
  isAppointmentSuggestionsEnabled: boolean;

  // Quick Quote sub-sections / sub-features
  // Allow users to submit quotes to third-party URLs, e.g. bidding websites
  isQuoteSubmissionViaURLEnabled: boolean;
  // Allows submitting quote to service's internal system (not the same as TMS)
  isQuoteSubmissionToServiceEnabled: boolean;
  isGetLaneRateFromServiceEnabled: boolean;
  // Allows access to quoting tools lane history (e.g. Greenscreens)
  isQuoteLaneHistoryEnabled: boolean;
  // Allows access to TMS lane history (e.g. McLeod)
  isTMSLaneHistoryEnabled: boolean;
  // Allow user to submit quote to TMS (TMS integration must implement CreateQuote)
  isTMSQuoteSubmissionEnabled: boolean;
  // Allow user to input profit margin rather than mark-up on QQ Calculator
  isQuoteCalculatorMarginEnabled: boolean;

  isAdvancedSearchEnabled: boolean;

  isMultiStopLoadViewEnabled: boolean;

  isBatchQuoteEnabled: boolean;
};

export type CarrierQuoteConfig = {
  from?: Maybe<string>;
  cc?: Maybe<string[]>;
  bcc?: Maybe<string[]>;
  subject?: Maybe<string>;
  emailBody?: Maybe<string>;
  // bccCarriers is not currently used in FE, in API we currently only send emails with BCC carriers on
  // TODO: If we ever allow this to be off for a customer we can use this to conditionally render that behavior
  bccCarriers: boolean;
  isFindCarrierByGroupEnabled: boolean;
  isFindCarrierByLocationEnabled: boolean;
  // Customer Search input in Request Carriers section
  showCustomerSearch: boolean;
  // Item Description input in Request Carriers section
  showItemDescription: boolean;
  // Delivery Section in Request Carriers section
  showDeliverySection: boolean;
  // Require Delivery Location input in Request Carriers section
  requireDeliveryLocation: boolean;
  // Show Pickup Address Line 1 input in Request Carriers section
  showPickupAddressLine1: boolean;
  // Show Delivery Address Line 1 input in Request Carriers section
  showDeliveryAddressLine1: boolean;
  // Custom name for the carrier quote feature (used in header/tab title)
  customName: string;
};

export const allEnabledServiceFeatures: ServiceFeaturesListType = {
  isAppointmentSchedulingEnabled: true,
  isAppointmentEmailingEnabled: true,
  isTrackAndTraceEnabled: true,
  isQuickQuoteEnabled: true,
  isAppointmentSuggestionsEnabled: true,
  isCarrierInfoSuggestionsEnabled: true,
  isTurvoSectionLinksEnabled: true,
  isOperatorEnabled: true,
  isOrderEnabled: true,
  isExceptionsEnabled: true,
  isCheckCallCarrierSOPEnabled: true,
  isCheckCallShipperSOPEnabled: true,
  isCheckCallNotesEnabled: true,
  isCarrierNetworkQuotingEnabled: true,
  isCarrierEmailOutboxEnabled: true,
  isAdvancedSearchEnabled: true,
  isCarrierVerificationEnabled: true,
  isTruckListEnabled: true,
  isTMSQuoteSubmissionEnabled: true,
  isLoadBuildingEnabled: true,
  isQuoteLaneHistoryEnabled: true,
  isTMSLaneHistoryEnabled: true,
  isQuoteSubmissionViaURLEnabled: true,
  isQuoteSubmissionToServiceEnabled: true,
  isGetLaneRateFromServiceEnabled: true,
  isLoadViewEnabled: true,
  isQuoteViewEnabled: true,
  isQuoteCalculatorMarginEnabled: true,
  isMultiStopLoadViewEnabled: true,
  isBatchQuoteEnabled: true,
};

export const initialServiceFeatureValues: ServiceFeaturesListType = {
  isAppointmentSchedulingEnabled: false,
  isAppointmentEmailingEnabled: false,
  isTrackAndTraceEnabled: false,
  isQuickQuoteEnabled: false,
  isAppointmentSuggestionsEnabled: false,
  isCarrierInfoSuggestionsEnabled: false,
  isTurvoSectionLinksEnabled: false,
  isOperatorEnabled: false,
  isOrderEnabled: false,
  isExceptionsEnabled: false,
  isCheckCallCarrierSOPEnabled: false,
  isCheckCallShipperSOPEnabled: false,
  isCheckCallNotesEnabled: false,
  isCarrierNetworkQuotingEnabled: false,
  isCarrierEmailOutboxEnabled: false,
  isAdvancedSearchEnabled: false,
  isCarrierVerificationEnabled: false,
  isTruckListEnabled: false,
  isTMSQuoteSubmissionEnabled: false,
  isLoadBuildingEnabled: false,
  isQuoteLaneHistoryEnabled: false,
  isTMSLaneHistoryEnabled: false,
  isQuoteSubmissionViaURLEnabled: false,
  isQuoteSubmissionToServiceEnabled: false,
  isGetLaneRateFromServiceEnabled: false,
  isLoadViewEnabled: false,
  isQuoteViewEnabled: false,
  isQuoteCalculatorMarginEnabled: false,
  isMultiStopLoadViewEnabled: false,
  isBatchQuoteEnabled: false,
};

export const initialServiceValues: Service = {
  serviceID: 0,
  tmsIntegrations: [],
  quotingIntegrations: [],
  schedulerIntegrations: [],
  configurations: { defaultPriceMargin: null, defaultPriceMarginType: null },
  serviceFeaturesEnabled: initialServiceFeatureValues,
  quickQuoteConfig: undefined,
  carrierQuoteConfig: null,
};

export type ServiceContextType = Service & {
  setService: React.Dispatch<React.SetStateAction<Service>>;
};

export const ServiceContext = createContext<ServiceContextType>({
  serviceID: 0,
  tmsIntegrations: [],
  quotingIntegrations: [],
  schedulerIntegrations: [],
  configurations: { defaultPriceMargin: null, defaultPriceMarginType: null },
  serviceFeaturesEnabled: initialServiceFeatureValues,
  setService: () => false,
});
