{"compilerOptions": {"allowJs": false, "allowUnusedLabels": false, "baseUrl": ".", "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "node", "noImplicitReturns": true, "noUnusedParameters": true, "outDir": "dist", "removeComments": false, "skipLibCheck": true, "sourceMap": true, "target": "ESNext", "strict": true, "forceConsistentCasingInFileNames": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "pretty": true, "types": ["jest", "node", "office-js", "chrome"], "typeRoots": ["./src/global.d.ts", "./node_modules/@types", "../../node_modules/@types"], "paths": {"@assets/*": ["../assets/*"], "@auth/*": ["../auth/*"], "@components/*": ["../components/*"], "@constants/*": ["../constants/*"], "@contexts/*": ["../contexts/*"], "@css/*": ["../css/*"], "@hooks/*": ["../hooks/*"], "@lib/*": ["../lib/*"], "@pages/*": ["../pages/*"], "@utils/*": ["../utils/*"], "@types/*": ["../types/*"], "virtual:reload-on-update-in-background-script": ["../global.d.ts"], "virtual:reload-on-update-in-view": ["../global.d.ts"], "@src/*": ["../*"]}}, "compileOnSave": false, "buildOnSave": false, "ts-node": {"files": true}}