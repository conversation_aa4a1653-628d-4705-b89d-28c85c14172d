import { TMSLocation, TMSLocationWithDistance } from './Load';
import { Maybe } from './UtilityTypes';

export type QuoteRequest = EmailCarriersBody & {
  carrierQuotes: Maybe<CarrierQuote[]>;
};

export type EmailCarriersBody = {
  id: number;
  emailID: number;
  threadID: string;
  transportType: Maybe<TransportType>;
  commodity: Maybe<string>;
  weightLbs: Maybe<number>;
  weightUnit: Maybe<string>;
  pallets: Maybe<Pallet[]>;
  pickupLocation: Maybe<Address>; // First element from the stops array
  pickupStartDate: Maybe<Date>;
  pickupEndDate: Maybe<Date>;
  deliveryLocation: Maybe<Address>; // Last element from the stops array
  deliveryStartDate: Maybe<Date>;
  deliveryEndDate: Maybe<Date>;
  carrierNetworkEmails: Maybe<CarrierNetworkEmail[]>;
  from: Maybe<string>;
  carriers: Maybe<TMSLocationWithDistance[]>;
  carrierGroupId?: Maybe<number>;
};

export enum TransportType {
  VAN = 'VAN',
  REEFER = 'REEFER',
  FLATBED = 'FLATBED',
  // Not supported natively by GS/DAT; there's special logic in BE to handle these for quote lookups
  HOTSHOT = 'HOTSHOT', // mapped to FLATBED
  BOXTRUCK = 'BOX TRUCK', // mapped to VAN
  SPRINTER = 'SPRINTER', // mapped to VAN
  SPECIAL = 'SPECIAL',
}

export interface CarrierNetworkEmail {
  id: number;
  externalID: string;
  threadID: string;
  recipients: string[];
  subject: string;
  bounced: boolean;
  url: string; // URL-encoded
}

export interface Quote {
  id: number;
  externalID: string;
  emailAddress: string;
  threadID: string;
  totalCostUSD: number;
  available: boolean;
  notes: string; // e.g. all-in, FSC (fuel surcharge) is separate, etc
}

// Always initialized with a threadID and TMSLocation so that we can render the quote card
export interface CarrierQuote {
  ID: number;
  emailID: Maybe<number>; // Non-empty if carrier responded at least once
  threadID: string; // Always non-empty
  suggestedQuote: Maybe<CarrierQuoteData>;
  appliedQuote: Maybe<CarrierQuoteData>;
  carrierLocation: TMSLocation;
}

export interface CarrierQuoteData {
  totalCost: number;
  currency: string; // "USD" assume right now
  isAvailable: boolean;
  notes: string; // e.g. all-in, availability window, FSC (fuel surcharge) is separate, etc
}

export interface Pallet {
  count: number; // Can also be crate count
  declaredValueUsd: number;
  weightLbs: number;
  lengthInches: number;
  widthInches: number;
  heightInches: number;
}

export interface Address {
  name: string;
  addressLine1: string;
  addressLine2: string;
  zip: string;
  city: string;
  state: string;
  country: string;
}
