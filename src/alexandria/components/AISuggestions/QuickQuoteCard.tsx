import { NewShipmentCardCore } from 'components/AISuggestions/CoreNewShipmentCard';
import { KeyValueElement } from 'components/AISuggestions/SuggestionsCard';
import { CompanyCoreInfo, formatAddressCityStateZip } from 'types/Load';
import { Undef } from 'types/UtilityTypes';
import { SuggestedQuoteChange } from 'types/suggestions/QuoteSuggestions';

export const QuickQuoteCard = (
  suggestion: SuggestedQuoteChange
): Undef<JSX.Element> => {
  const sug = suggestion.suggested;

  const pickupAddr =
    formatQQAddress(sug.pickupCity, sug.pickupState, sug.pickupZip) ??
    'Unknown';
  const dropoffAddr =
    formatQQAddress(sug.deliveryCity, sug.deliveryState, sug.deliveryZip) ??
    'Unknown';

  if (!pickupAddr && !dropoffAddr) {
    return undefined;
  }

  return (
    <NewShipmentCardCore
      pickupAddr={pickupAddr}
      dropoffAddr={dropoffAddr}
      pickupDate={''} // Don't show pickup date for quick quote carousel suggestion
      dropoffDate={''} // Don't show dropoff date for quick quote carousel suggestion
      additionalInfo={[
        <KeyValueElement
          name='transportType'
          changeLabel='Transport Type'
          changeValue={sug.transportType}
        />,
      ]}
    />
  );
};

const formatQQAddress = (city: string, state: string, zip: string): string => {
  if (!city && !state && !zip) {
    return '';
  }

  const address: CompanyCoreInfo = {
    city: city,
    state: state,
    zipCode: zip,
  } as CompanyCoreInfo;

  return formatAddressCityStateZip(address);
};
