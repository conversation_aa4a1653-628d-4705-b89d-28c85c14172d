import { ReactNode, useEffect, useState } from 'react';

import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react';

import { Button } from 'components/Button';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';

interface ExpandableContentProps {
  showText: string;
  hideText: string;
  forceOpen?: boolean;
  children: ReactNode;
}

export default function ExpandableContent({
  showText,
  hideText,
  forceOpen,
  children,
}: ExpandableContentProps) {
  const [showContent, setShowContent] = useState(false);

  const toggleContent = () => {
    setShowContent(!showContent);
  };

  useEffect(() => {
    if (forceOpen) setShowContent(true);
  }, [forceOpen]);

  return (
    <div className='grid gap-6 grid-cols-1 mb-4 w-full mx-0'>
      <Button
        className={`w-full col-span-6 flex items-center gap-2 ${
          showContent ? 'bg-gray-100 hover:bg-gray-200' : ''
        }`}
        type='button'
        variant='outline'
        size='sm'
        onClick={toggleContent}
        buttonNamePosthog={ButtonNamePosthog.ToggleExpandableContent}
      >
        <span className='flex items-center gap-2 text-sm'>
          {showContent ? hideText : showText}
          {showContent ? (
            <ChevronUpIcon className='w-4 h-4' />
          ) : (
            <ChevronDownIcon className='w-4 h-4' />
          )}
        </span>
      </Button>
      <div className={`w-full col-span-6 ${showContent ? 'block' : 'hidden'}`}>
        {children}
      </div>
    </div>
  );
}
