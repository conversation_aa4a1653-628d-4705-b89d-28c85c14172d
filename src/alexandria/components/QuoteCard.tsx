import _ from 'lodash';
import {
  BadgeDollarSignIcon,
  CircleCheckIcon,
  CoinsIcon,
  InfoIcon,
  ShieldCheckIcon,
  Truck,
} from 'lucide-react';

import QuoteConfidenceLevel from 'components/QuoteConfidenceLevel';
import { SelectedCarrierType } from 'lib/api/getQuickQuote';
import {
  DATQuoteLocationType,
  DATQuoteTimeframe,
  LaneTier,
  laneTierMap,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { TransportType } from 'types/QuoteRequest';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';
import { titleCase } from 'utils/formatStrings';
import { cn } from 'utils/shadcn';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './Tooltip';

export type TooltipContentType = {
  // GS specific
  zipCodeLookupExplanation?: string;
  cityStateLookupExplanation?: string;
  // DAT specific
  timeframe?: string;
  originName?: string;
  originType?: DATQuoteLocationType | LaneTier;
  destinationName?: string;
  destinationType?: DATQuoteLocationType | LaneTier;
  reports?: number;
  companies?: number;
};

export type PriceRangeType = {
  lowEstimate: number;
  highEstimate: number;
};

export type QuoteCardType = {
  type: SelectedCarrierType; // e.g. NETWORK, BUYPOWER, DAT
  title: string; // e.g. "Network Quote"
  icon: React.ReactNode; // e.g. <GreenscreensLogo />
  cost: number; // e.g. 1125 (flat number, not a function)
  priceRange: Maybe<PriceRangeType>; // e.g. $1125 - $1350
  confidence: Maybe<number>; // e.g. 85 (confidence out of 100)
  costPerMile?: number; // e.g. 2.10 (flat number, not a function)
  priceRangePerMile?: Maybe<PriceRangeType>; // e.g. $2.05 - $2.15
  inputtedTransportType: TransportType; // Greenscreens does not support some enums like HOTSHOT and BOXTRUCK,
  actualTransportType: TransportType; // so the data is proxied with other types. We want to inform the user which data source was used
  quotingIntegrationName?: string; // e.g. "Greenscreens", "DAT", "Truckstop"
} & (
  | {
      tooltipContent: MaybeUndef<TooltipContentType>;
      tooltipConstructor: (content: TooltipContentType) => JSX.Element; // e.g. DATTooltipConstructor
    }
  | {
      tooltipContent?: undefined;
      tooltipConstructor?: undefined;
    }
);

type QuoteCardProps = {
  carrier: QuoteCardType;
  isSelected: boolean;
  onClick: () => void;
  lowConfidenceThreshold: number;
  mediumConfidenceThreshold: number;
};

export const QuoteCard = ({
  carrier,
  isSelected,
  onClick,
  lowConfidenceThreshold,
  mediumConfidenceThreshold,
}: QuoteCardProps) => {
  const roundedCost = _.round(carrier.cost);
  const roundedCostPerMile = carrier.costPerMile
    ? _.round(carrier.costPerMile, 2)
    : null;

  const roundedPriceRange = carrier.priceRange
    ? {
        lowEstimate: _.round(carrier.priceRange.lowEstimate),
        highEstimate: _.round(carrier.priceRange.highEstimate),
      }
    : null;

  const roundedPriceRangePerMile = carrier.priceRangePerMile
    ? {
        lowEstimate: _.round(carrier.priceRangePerMile.lowEstimate, 2),
        highEstimate: _.round(carrier.priceRangePerMile.highEstimate, 2),
      }
    : null;

  const roundedConfidence = carrier.confidence
    ? _.round(carrier.confidence)
    : null;

  return (
    <TooltipProvider>
      <div
        key={carrier.type}
        className={cn(
          'relative flex-column w-full border-2 border-[#FE9659] rounded-[4px] p-2 transition-transform',
          isSelected
            ? 'bg-orange-bg'
            : 'cursor-pointer hover:scale-[1.01] bg-white'
        )}
        onClick={onClick}
      >
        <div className='flex justify-between items-center mb-2'>
          <div className='flex gap-2'>
            <h4>{carrier.title}</h4>{' '}
            {carrier.tooltipContent && (
              <Tooltip delayDuration={10}>
                <TooltipTrigger onClick={(e: any) => e.preventDefault()}>
                  <InfoIcon className='h-4 w-4 text-grayscale-content-input' />
                </TooltipTrigger>
                <TooltipContent className='ml-2 max-w-64 whitespace-pre-wrap'>
                  {carrier.tooltipConstructor(carrier.tooltipContent)}
                </TooltipContent>
              </Tooltip>
            )}
          </div>
          {carrier.icon}
        </div>
        <div className='text-[#444] text-[14px]'>
          <div className='flex justify-between mt-2'>
            <div className='flex items-center gap-x-1.5'>
              <Truck className='h-4 w-4' />
              Transport Type
            </div>
            {/* If the transport type was proxied, then show the source data + tooltip */}
            {carrier.inputtedTransportType !== carrier.actualTransportType ? (
              <div className='flex items-baseline gap-x-2'>
                <Tooltip delayDuration={10}>
                  <TooltipTrigger className='border-b border-dashed border-black text-sm'>
                    {`${titleCase(carrier.actualTransportType)}`}
                  </TooltipTrigger>
                  <TooltipContent className='mr-1 max-w-60 whitespace-pre-wrap'>
                    <p>
                      {`${carrier.quotingIntegrationName ? `${carrier.quotingIntegrationName}` : 'Quoting integration'}`}{' '}
                      does not support{' '}
                      {titleCase(carrier.inputtedTransportType)} quotes, showing
                      equivalent {titleCase(carrier.actualTransportType)} quote.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
            ) : (
              <div className='text-sm'>
                {`${titleCase(carrier.actualTransportType)}`}
              </div>
            )}
          </div>

          <div className='flex justify-between'>
            <div className='flex items-center gap-x-1.5'>
              <BadgeDollarSignIcon className='h-4 w-4' />
              Target Buy
            </div>
            <div className='flex items-baseline gap-x-2'>
              {`$${roundedCost}`}
              {roundedCostPerMile && (
                <p className='text-xs'>{`($${roundedCostPerMile}/mi)`}</p>
              )}
            </div>
          </div>
          <div className='flex justify-between'>
            {roundedConfidence && (
              <>
                <div className='flex items-center gap-x-1.5'>
                  <ShieldCheckIcon className='h-4 w-4' />
                  Confidence
                </div>
                <QuoteConfidenceLevel
                  confidence={roundedConfidence}
                  lowConfidenceThreshold={lowConfidenceThreshold}
                  mediumConfidenceThreshold={mediumConfidenceThreshold}
                />
              </>
            )}
          </div>
          <div className='flex justify-between'>
            {roundedPriceRange && (
              <>
                <div className='flex items-center gap-x-1.5'>
                  <CoinsIcon className='h-4 w-4' />
                  Price Range
                </div>
                {roundedPriceRangePerMile ? (
                  <Tooltip delayDuration={10}>
                    <TooltipTrigger className='border-b border-dashed border-black'>
                      {`$${roundedPriceRange.lowEstimate} - $${roundedPriceRange.highEstimate}`}
                    </TooltipTrigger>
                    <TooltipContent className='mr-2 max-w-60 whitespace-pre-wrap'>
                      <div className='font-medium text-foreground pb-3'>
                        {`Price range per mile`}
                        <div className='flex text-[13px] text-grayscale-content-2'>
                          {`$${roundedPriceRangePerMile.lowEstimate} - $${roundedPriceRangePerMile.highEstimate}/mi.`}
                        </div>
                      </div>

                      <p className='border-t pt-3'>All prices include fuel.</p>
                    </TooltipContent>
                  </Tooltip>
                ) : (
                  <p>{`$${roundedPriceRange.lowEstimate} - $${roundedPriceRange.highEstimate}`}</p>
                )}
              </>
            )}
          </div>

          {isSelected && (
            <CircleCheckIcon className='absolute -top-4 -right-3 fill-white stroke-green-500 w-6 h-6 mt-1' />
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

const parseDATLocationDetails = (
  type: DATQuoteLocationType,
  name: string
): string => {
  switch (type) {
    case DATQuoteLocationType['3_DIGIT_ZIP']:
      return `${name.toUpperCase()}`;
    case DATQuoteLocationType['MARKET_AREA']:
      return `${name.replace(' Mkt', '')} Market Area`;
    case DATQuoteLocationType['EXTENDED_MARKET_AREA']:
      return `${name.replace(' X-Mkt', '')} Extended Market Area`;
    case DATQuoteLocationType['STATE']:
      return `${name} State`;
    case DATQuoteLocationType['REGION']:
      return `${name} Region`;
    case DATQuoteLocationType['COUNTRY']:
      return `${name} Country`;
    default:
      return '';
  }
};

const parseDATTimeframe = (timeframe: DATQuoteTimeframe): string => {
  return timeframe
    .replace('_', ' ') // Remove underscores for readability
    .replace('DAYS', 'DAY') // Replace plural for conciseness (e.g. 30 day timeframe)
    .toLowerCase();
};

export const GreenscreensTooltipConstructor = (
  content: TooltipContentType,
  isZipCodeLookup: boolean
): JSX.Element => {
  return (
    <>
      <p>
        {isZipCodeLookup
          ? content.zipCodeLookupExplanation
          : content.cityStateLookupExplanation}
      </p>
    </>
  );
};

export const DATTooltipConstructor = (
  content: TooltipContentType
): JSX.Element => {
  return (
    <>
      <p>
        <b>Broker Spot Rate</b> based on a
        <b>{` ${parseDATTimeframe(content.timeframe as DATQuoteTimeframe)} `}</b>
        timeframe from
        <b>{` ${parseDATLocationDetails(content.originType as DATQuoteLocationType, content.originName || '')} `}</b>
        to
        <b>{` ${parseDATLocationDetails(content.destinationType as DATQuoteLocationType, content.destinationName || '')}`}</b>
      </p>
      {content.companies && content.reports && (
        <p className='mt-2'>
          Data calculated using <b>{content.reports} reports</b> from{' '}
          <b>{content.companies} companies</b>
        </p>
      )}
    </>
  );
};

export const LaneHistoryTooltipConstructor = (
  content: TooltipContentType
): JSX.Element => {
  return (
    <p>
      Rate based on a<b>{` ${content.timeframe} `}</b>
      from {`${laneTierMap[content.originType as LaneTier]}`} to
      {` ${laneTierMap[content.destinationType as LaneTier]}`}.
    </p>
  );
};
