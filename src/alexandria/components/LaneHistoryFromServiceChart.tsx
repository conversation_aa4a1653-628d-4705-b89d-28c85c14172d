import React from 'react';

import _ from 'lodash';
import { CartesianGrid, Line, LineChart, ReferenceArea, YAxis } from 'recharts';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore library installed on parent module, overriding tsc check
import { Payload } from 'recharts/types/component/DefaultTooltipContent';
import { DataKey } from 'recharts/types/util/types';

import { Last7DaysData, MonthData } from 'lib/api/getLaneHistoryFromService';
import { laneHistoryFromServiceChartConfig } from 'utils/laneHistoryFromServiceChartConfig';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from './Card';
import {
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from './Chart';

interface LaneHistoryFromServiceChartProps {
  months: MonthData[];
  last7Days: Last7DaysData;
  title?: string;
  subtitle?: string;
  description?: string;
}

export function LaneHistoryFromServiceChart({
  months,
  last7Days,
  title = 'DAT Rate History',
  subtitle,
  description,
}: LaneHistoryFromServiceChartProps) {
  // Transform data to include a break between monthly data and 7-day data
  const chartData = React.useMemo(() => {
    const monthlyData = months.map((month) => ({
      ...month,
      period: month.month,
      isBreak: false,
    }));

    // Add a break point
    const breakPoint = {
      period: '',
      averageRate: null,
      highRate: null,
      lowRate: null,
      p75Rate: null,
      p25Rate: null,
      reports: 0,
      isBreak: true,
    };

    // Add 7-day data with special labeling
    const sevenDayData = {
      period: 'Last 7 Days',
      averageRate: last7Days.averageRate,
      highRate: last7Days.highRate,
      lowRate: last7Days.lowRate,
      p75Rate: null, // 7-day data doesn't have percentile data
      p25Rate: null,
      reports: last7Days.reports,
      isBreak: false,
    };

    return [...monthlyData, breakPoint, sevenDayData];
  }, [months, last7Days]);

  const yAxisMax = Math.max(
    ...chartData
      .filter((item) => !item.isBreak && item.averageRate !== null)
      .map((item) =>
        Math.max(item.averageRate || 0, item.highRate || 0, item.lowRate || 0)
      )
  );

  const formatDecimalValue = (
    value: number,
    minPrecision = 0,
    maxPrecision = 0
  ) => {
    return value.toLocaleString('en-US', {
      minimumFractionDigits: minPrecision,
      maximumFractionDigits: maxPrecision,
    });
  };

  return (
    <Card className='bg-transparent border-none shadow-none'>
      <CardHeader className='p-0 pb-3'>
        <CardTitle className='flex flex-col text-md text-grayscale-content-label font-semibold'>
          <div className='flex flex-row flex-wrap gap-x-2 items-center'>
            {title}
            {subtitle && (
              <span className='text-sm font-normal'>({subtitle})</span>
            )}
          </div>
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className='p-0'>
        <ChartContainer config={laneHistoryFromServiceChartConfig}>
          <LineChart accessibilityLayer data={chartData}>
            <CartesianGrid vertical={false} />
            <YAxis
              domain={[0, yAxisMax]}
              tickFormatter={(value) => `$${_.round(value)}`}
              tickLine={false}
              width={yAxisMax > 999 ? 45 : 40}
            />

            {/* Error bars for monthly data */}
            {chartData
              .filter(
                (item) =>
                  !item.isBreak &&
                  item.averageRate !== null &&
                  item.lowRate !== null &&
                  item.highRate !== null
              )
              .map((item, index) => (
                <ReferenceArea
                  key={`error-${index}`}
                  x1={item.period}
                  x2={item.period}
                  y1={item.lowRate || 0}
                  y2={item.highRate || 0}
                  fill='rgba(0, 186, 52, 0.1)'
                  fillOpacity={0.3}
                  stroke='none'
                />
              ))}

            <ChartTooltip
              content={
                <ChartTooltipContent
                  hideLabel
                  className='w-[160px]'
                  cursor={false}
                  defaultIndex={1}
                  formatter={(
                    value: any,
                    name: any,
                    item: Payload<any, any>,
                    index: number
                  ) => {
                    if (item.payload.isBreak) {
                      return (
                        <>
                          {index === 0 && (
                            <div className='flex flex-col items-center w-full gap-2'>
                              <div className='w-full text-center font-medium'>
                                ── 7 Day Data ──
                              </div>
                            </div>
                          )}
                          {index !== 0 && null}
                        </>
                      );
                    }

                    return (
                      <>
                        {/* Display Period */}
                        {index === 0 && (
                          <div className='w-full'>{item.payload.period}</div>
                        )}

                        {/* Main data row */}
                        <div className='flex items-center w-full gap-2'>
                          <div
                            className='h-2.5 w-2.5 shrink-0 rounded-[2px] bg-[--color-bg]'
                            style={
                              {
                                '--color-bg': `var(--color-${name})`,
                              } as React.CSSProperties
                            }
                          />
                          <span className='flex-grow'>
                            {laneHistoryFromServiceChartConfig[
                              name as keyof typeof laneHistoryFromServiceChartConfig
                            ]?.label || name}
                          </span>
                          <div className='flex items-baseline gap-0.5 font-mono font-medium tabular-nums text-foreground'>
                            ${formatDecimalValue(value)}
                          </div>
                        </div>

                        {/* Show reports information */}
                        {index === 2 && (
                          <div className='flex w-full items-center border-t pt-1.5 text-xs font-medium text-foreground'>
                            Reports
                            <div className='ml-auto flex items-baseline gap-0.5 font-mono font-medium tabular-nums text-foreground'>
                              {item.payload.reports}
                            </div>
                          </div>
                        )}
                      </>
                    );
                  }}
                />
              }
            />
            {/* Display the data series lines */}
            {(['averageRate', 'highRate', 'lowRate'] as const).map((key) => (
              <Line
                key={key}
                dataKey={key as DataKey<typeof key>}
                type='monotone'
                stroke={laneHistoryFromServiceChartConfig[key]?.color}
                strokeWidth={2}
                dot={true}
                connectNulls
              />
            ))}
            <ChartLegend content={<ChartLegendContent />} />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
