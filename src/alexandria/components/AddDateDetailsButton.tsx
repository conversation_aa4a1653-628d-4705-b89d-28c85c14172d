import React from 'react';

import { PlusIcon } from 'lucide-react';

import { Button } from 'components/Button';

interface AddDateDetailsButtonProps {
  onClick: () => void;
  buttonNamePosthog?: string;
  className?: string;
}

export const AddDateDetailsButton: React.FC<AddDateDetailsButtonProps> = ({
  onClick,
  buttonNamePosthog,
  className = '',
}) => {
  return (
    <Button
      className={`w-44 mx-auto h-8 text-[14px] text-grayscale-content-2 flex items-center justify-center gap-2 border border-transparent hover:border-gray-600 hover:bg-gray-200 ${className}`}
      type='button'
      variant='ghost'
      buttonNamePosthog={buttonNamePosthog ?? null}
      onClick={onClick}
    >
      <PlusIcon className='h-4 w-4' />
      Add dates
    </Button>
  );
};
