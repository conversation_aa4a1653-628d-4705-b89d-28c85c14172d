import { useMemo } from 'react';

import { KeyedMutator } from 'swr';

import { StopTypes } from 'types/Appointment';
import { EmailTemplate, TemplateType } from 'types/EmailTemplates';
import { Maybe } from 'types/UtilityTypes';
import { useThrowableSWR } from 'utils/fetcher';

export type CarrierEmailTemplatesResponse = {
  dispatchTemplate: EmailTemplate;
  pickupTemplate: EmailTemplate;
  afterPickupTemplate: EmailTemplate;
  inTransitTemplate: EmailTemplate;
  dropoffTemplate: EmailTemplate;
  afterDropoffTemplate: EmailTemplate;
};

export type AppointmentEmailTemplatesResponse = {
  appointmentRequestTemplate: EmailTemplate;
};

type EmailTemplatesResponseMap = {
  [TemplateType.CARRIER]: CarrierEmailTemplatesResponse;
  [TemplateType.APPOINTMENT]: AppointmentEmailTemplatesResponse;
};

export type EmailTemplatesFetchResult<T extends TemplateType> = {
  emailTemplates: Maybe<EmailTemplatesResponseMap[T]>;
  invalidate: KeyedMutator<EmailTemplatesResponseMap[T]>;
  isLoading: boolean;
  error: Error;
};

export default function useFetchEmailTemplates<T extends TemplateType>(
  loadID?: Maybe<number>,
  templateType: T = TemplateType.CARRIER as T,
  stopType?: StopTypes
): EmailTemplatesFetchResult<T> {
  const endpoint = useMemo(() => {
    if (!loadID) return null;

    const query = stopType ? `?stopType=${stopType}` : '';

    switch (templateType) {
      case TemplateType.CARRIER:
        return `load/${loadID}/sops/carrier`;
      case TemplateType.APPOINTMENT:
        return `appt/${loadID}/sops${query}`;
      default:
        return null;
    }
  }, [loadID, templateType, stopType]);

  const { data, isLoading, mutate, error } =
    useThrowableSWR<EmailTemplatesResponseMap[T]>(endpoint);

  return {
    emailTemplates: data || null,
    invalidate: mutate,
    isLoading: isLoading,
    error: error,
  };
}
