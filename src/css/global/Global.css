.drumkit-sidebar-modifier {
  width: calc(100vw - 300px);
  overflow-x: scroll;
}

.drumkit-discrete-root-element {
  background: #ffffff;
  width: 300px;
  height: 100vh;
  position: fixed;
  z-index: 9999999;
  right: 0;
  bottom: 0;
  top: 0;
}

#drumkit-content-view-root {
  @tailwind base;
  @tailwind components;
  @tailwind utilities;
  @tailwind screens;
}

#drumkit-content-view-root * {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 14px;
}

/*
  https://tailwindcss.com/docs/padding
  Tailwind padding classes enforced with !important due to conflict with RelayTMS.

  Not sure if due to a tailwind version mismatch or just a CSS relative unit (em/rem) confusion, but
  according to the documentation px-5 and py-5 classes should be 1.25rem = 20px, but <PERSON><PERSON> was overriding
  the class 3rem !important = 48px, which would've been correctly implemented through px-12 and py-12.
*/

#drumkit-content-view-root .px-5 {
  padding-left: 1.25rem !important;
  padding-right: 1.25rem !important;
}

#drumkit-content-view-root .py-5 {
  padding-top: 1.25rem !important;
  padding-bottom: 1.25rem !important;
}

/*
  Tailwind background-color class on hover enforced with !important due to conflict with RelayTMS.

  We use the tailwind class .bg-white for our non-hovered state, but RelayTMS has another class with the
  same name that overrides the hovered event with an !important flag:
  .bg-white { background-color: #fff !important; }

  As a solution we simply override our hovered state tailwind class, since it has a higher specificity index.
*/

#drumkit-content-view-root .hover\:bg-orange-main:hover {
  background-color: #f98600 !important;
}

#drumkit-content-view-root html:focus-within {
  scroll-behavior: smooth;
}
#drumkit-content-view-root body {
  min-height: 100vh;
  text-rendering: optimizeSpeed;
}

#drumkit-content-view-root .multiRipple {
  width: 100%;
  height: 100%;
  position: relative;
}

#drumkit-content-view-root .multiRipple div {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 0.3rem solid #979fd0;
  animation: ripple 1.5s infinite;
}

#drumkit-content-view-root .multiRipple div:nth-child(2) {
  animation-delay: 0.5s;
}

@keyframes ripple {
  from {
    transform: scale(0);
    opacity: 1;
  }
  to {
    transform: scale(1);
    opacity: 0;
  }
}

[data-radix-popper-content-wrapper] {
  background: '#fff' !important;
  padding: 1rem;
  z-index: 50 !important;
}

[data-radix-menu-content] {
  background-color: '#fff' !important;
  padding: 1rem;
}

/* Custom classes to automatically resize AI hint label if there's not enough space for it
https://medium.com/swlh/hiding-an-element-when-there-is-no-enough-space-thanos-snap-technique-8a11e31267c0*/
.ai-hint-span {
  max-width: max(
    0px,
    calc((90% - 70px) * 999)
  ); /* 70px approximate width of icon + padding + text */
  overflow: hidden;
}

.ai-hint-text {
  max-width: max(
    0px,
    calc((70% - 48px) * 999) /* 48px = approximate width of 'AI-filled' */
  ); /* 70% to account for the space the sparkle icon takes up */
  overflow: hidden;
}

.select-trigger > span {
  font-size: 14px !important;
}

[role='tablist']::-webkit-scrollbar,
[role='tablist'] div::-webkit-scrollbar {
  background-color: transparent;
  width: 16px;
}

[role='tablist']::-webkit-scrollbar-track,
[role='tablist'] div::-webkit-scrollbar-track {
  background-color: transparent;
}

[role='tablist']::-webkit-scrollbar-thumb,
[role='tablist'] div::-webkit-scrollbar-thumb {
  background-color: #c6c6c6;
  border-radius: 16px;
  border: 4px solid #fafafa;
}

[role='tablist']::-webkit-scrollbar-thumb:hover,
[role='tablist'] div::-webkit-scrollbar-thumb:hover {
  background-color: #878787;
}

[role='tablist']::-webkit-scrollbar-button,
[role='tablist'] div::-webkit-scrollbar-button {
  display: none;
}

/* Overriding some of antd default styles to make it concise */
.ant-picker-input {
  font-size: 14px !important;
}

form .ant-picker-suffix {
  display: none !important;
}

form .ant-select {
  width: 100%;
  height: 32px !important;
  border: 1px solid #585757 !important;
  margin-top: 4px !important;
  border-radius: 4px !important;

  &.ant-select-disabled {
    border: 1px solid #e8e8e8 !important;
  }
}

form .ant-select .ant-select-selector,
form .ant-select .ant-select-selection-search-input {
  color: #585757 !important;
  cursor: pointer !important;
  border: none !important;
  border-radius: 4px !important;
}

form .ant-select .ant-select-selection-item,
form .ant-select .ant-select-selection-search {
  cursor: pointer !important;
  font-size: 14px !important;
  border-radius: 4px !important;
}

.search-filter .ant-select-selector {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin-right: 16px !important;
}

.search-filter .ant-select-focused .ant-select-selector {
  box-shadow: none !important;
}

.search-filter .ant-select-selection-item {
  background: transparent !important;
  padding: 0 !important;
  font-size: 14px !important;
}

.search-filter .ant-select-arrow {
  right: 0px !important;
}

.search-filter .ant-select-arrow svg {
  width: 12px !important;
}

.search-container .ant-picker-suffix {
  display: flex !important;
}

.search-container .ant-picker.ant-picker-outlined {
  border: 1px solid #d9d9d9 !important;
  border-radius: 4px !important;
  padding-left: 6px !important;
  padding-right: 6px !important;
}

.search-container .ant-input.ant-input-outlined {
  font-size: 12px !important;
  border: 1px solid #d9d9d9 !important;
  padding: 4px 4px 4px 6px !important;
  border-radius: 4px !important;
}

.search-container .ant-picker-input {
  font-size: 10px !important;
}

.search-container .ant-select-selector,
.search-container .ant-select-selection-search-input {
  height: 100% !important;
}

.ant-carousel .slick-track {
  display: flex !important;
  align-items: center !important;
}

.ant-carousel .slick-dots {
  bottom: -16px !important;
}

.ant-carousel .slick-slider {
  width: calc(100% - 16px);
  margin: 0px auto;
}

.ant-carousel .single-card.slick-slider {
  width: 100%;
}

.ant-carousel .slick-slide {
  transition: all 0.2s ease;
}

.ant-carousel .slick-slide:has(.carousel-card-hover:hover) {
  transform: scale(1.025);
}

.ant-carousel .slick-dots li button {
  font-size: 0 !important;
  background: #585757 !important;
}

.ant-carousel .slick-dots li.slick-active button {
  background: #585757 !important;
  opacity: 1;
}

.ant-carousel .slick-prev {
  inset-inline-start: -12px !important;
}

.ant-carousel .slick-next {
  inset-inline-end: 0 !important;
}

.ant-carousel .slick-prev,
.ant-carousel .slick-next {
  position: absolute;
  top: 50%;
  width: 12px !important;
  height: 12px !important;
  transform: translateY(-50%);
  color: #585757 !important;
  opacity: 0.4 !important;
  font-size: 0 !important;
  line-height: 0 !important;
  transition: opacity 0.2s ease;
}

.ant-carousel .slick-prev:hover,
.ant-carousel .slick-next:hover,
.ant-carousel .slick-prev:focus,
.ant-carousel .slick-next:focus {
  opacity: 1 !important;
}

.ant-carousel .slick-prev::after,
.ant-carousel .slick-next::after {
  box-sizing: border-box;
  position: absolute;
  display: inline-block;
  width: 12px !important;
  height: 12px !important;
  font-size: 14px !important;
  border-color: #585757 !important;
  border-inline-width: 2px 0 !important;
  border-block-width: 2px 0 !important;
  border-radius: 1px;
  content: '';
}

.ant-carousel .slick-prev::after {
  transform: rotate(-45deg) !important;
}

.ant-carousel .slick-next::after {
  transform: rotate(135deg) !important;
}

.ant-carousel .carousel-card h3.text-md {
  font-size: 14px !important;
}

.ant-carousel .carousel-card .font-medium,
.ant-carousel .carousel-card .text-xs div {
  font-size: 12px !important;
}

.ant-divider {
  border-block-start: 1px solid rgba(5, 5, 5, 0.06) !important;
}

.ant-float-btn-body {
  border-radius: 4px !important;
  background-color: #f98600 !important;
}

.ant-float-btn-icon {
  display: none;
}

.ant-float-btn-description {
  font-family: 'Plus Jakarta Sans', sans-serif !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}

/* select dropdown fights for z-index with drumkit-root element on TMS Wrapper */
.ant-select-dropdown {
  z-index: 9999999 !important;
  min-width: 200px !important;
}

.ant-picker-dropdown {
  z-index: 9999999 !important;
}

.mcp-logo-icon {
  fill: #4786fe;
}

.mcp-logo-icon:hover {
  fill: gray;
}

@keyframes wobble {
  from {
    transform: translate3d(0, 0, 0);
  }
  15% {
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}

.animate-wobble-repeat-1 {
  animation: wobble 1s 1;
}

/* Quill */
.ql-snow{
  .ql-picker{
      &.ql-size{
        width: 60px !important;
          .ql-picker-label,
          .ql-picker-item{
              &::before{
                  content: attr(data-value) !important;
              }
          }
      }
  }
}

.ql-toolbar.ql-snow .ql-formats {
  margin-right: 0px !important;
}

.ql-toolbar.ql-snow {
  padding: 6px 4px !important;
}

.ql-editor{
  font-family: 'Plus Jakarta Sans' !important;
  line-height: 1.5;
}

#ql-picker-options-1{
  left: -50px !important;
}

.ql-tooltip:not(.ql-hidden) {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  border: 1px solid #ccc !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  justify-content: flex-start !important;
}

.ql-action::after{
  margin-left: 4px !important;
}

/* Hack for having the remove link button on the same row as the edit link button */
.ql-tooltip .ql-remove {
  margin-left: 36px !important;
  margin-top: -26px !important;
}

/* Additional Quill styles for Gmail compatibility */
.ql-toolbar.ql-snow,
.ql-container.ql-snow{
  border: 1px solid #ccc !important;
}

.ql-toolbar.ql-snow button svg,
.ql-snow .ql-toolbar button svg {
  margin: 0 4px !important;
  width: 18px !important;
  height: 18px !important;
}

.ql-snow a {
  color: #06c !important;
}
