import { useContext } from 'react';

import DRUMKIT_AUTH_URL from '@constants/DrumkitAuthUrl';

import { SidebarStateContext } from 'contexts/sidebarStateContext';

export default function Login(): JSX.Element {
  const {
    currentState: { isChromeSidePanel },
  } = useContext(SidebarStateContext);

  return (
    <div id='drumkit-content-view-root'>
      {/* eslint-disable-next-line custom/no-grid-without-explicit-margin-and-width */}
      <div className='h-screen grid grid-cols-1 gap-4 place-items-center place-content-center mx-8'>
        <a
          type='button'
          href={`${DRUMKIT_AUTH_URL}/login`}
          target='_blank'
          className='bg-white border rounded-full text-center shadow-md text-black cursor-pointer m-4 py-2 px-3 place-items-center w-full hover:bg-grayscale-bg-secondary'
        >
          Sign into Drumkit
        </a>
        <p className='m-0 p-0 text-center text-grayscale-content'>
          {isChromeSidePanel
            ? "Close and reopen Drumkit when you're done."
            : "Refresh the page when you're done."}
        </p>
      </div>
    </div>
  );
}
