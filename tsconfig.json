{"compilerOptions": {"allowJs": false, "allowSyntheticDefaultImports": true, "baseUrl": ".", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "moduleResolution": "node", "noEmit": true, "noFallthroughCasesInSwitch": true, "paths": {"assets/*": ["src/alexandria/assets/*"], "components/*": ["src/alexandria/components/*"], "constants/*": ["src/alexandria/constants/*"], "contexts/*": ["src/alexandria/contexts/*"], "hooks/*": ["src/alexandria/hooks/*"], "lib/*": ["src/alexandria/lib/*"], "icons/*": ["src/alexandria/icons/*"], "types/*": ["src/alexandria/types/*"], "utils/*": ["src/alexandria/utils/*"], "pages/*": ["src/alexandria/pages/*"], "@alexandria/*": ["src/alexandria/*"], "@assets/*": ["src/assets/*"], "@auth/*": ["src/auth/*"], "@components/*": ["src/components/*"], "@constants/*": ["src/constants/*"], "@contexts/*": ["src/contexts/*"], "@css/*": ["src/css/*"], "@hooks/*": ["src/hooks/*"], "@lib/*": ["src/lib/*"], "@pages/*": ["src/pages/*"], "@utils/*": ["src/utils/*"], "virtual:reload-on-update-in-background-script": ["./src/global.d.ts"], "virtual:reload-on-update-in-view": ["./src/global.d.ts"], "@src/*": ["src/*"]}, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ESNext", "types": ["vite/client", "jest", "chrome"], "typeRoots": ["./src/global.d.ts", "./node_modules/@types"]}, "exclude": ["dist", "node_modules"], "include": ["src", "scripts", "vite.config.ts", "node_modules/@types", "postcss.config.js", "./package.json"]}